<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.springcenter</groupId>
    <artifactId>jic-box-activity</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>jic-box-activity-api</module>
        <module>jic-box-activity-center</module>
        <module>jic-box-activity-gateway</module>
    </modules>

    <properties>
        <jic-box-activity.version>1.0-SNAPSHOT</jic-box-activity.version>

        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>8</java.version>

        <spring-boot-dependencies.version>2.2.5.RELEASE</spring-boot-dependencies.version>
        <spring-cloud-dependencies.version>Hoxton.SR3</spring-cloud-dependencies.version>
        <spring-cloud-alibaba-dependencies.version>2.2.1.RELEASE</spring-cloud-alibaba-dependencies.version>
        <jaxrs.version>3.12.1.Final</jaxrs.version>
        <servlet-api.version>4.0.1</servlet-api.version>

        <jnbyimage.version></jnbyimage.version>
        <jic.image.version>${jnbyimage.version}</jic.image.version>
        <docker.registry.url></docker.registry.url>
        <docker.registry.host>http://${docker.registry.url}:2375</docker.registry.host>
        <docker.plugin.version>1.2.0</docker.plugin.version>
        <jnby.common.version>1.1.0</jnby.common.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.spring.center.retail</groupId>
                <artifactId>jic-retail-api</artifactId>
                <version>1.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jnby</groupId>
                        <artifactId>jnby-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.spring.center.reference</groupId>
                        <artifactId>jic-starter-swagger</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jnby</groupId>
                <artifactId>jnby-common</artifactId>
                <version>${jnby.common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.plugin</groupId>
                        <artifactId>spring-plugin-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-openfeign</artifactId>
                <version>2.2.2.RELEASE</version>
            </dependency>
            <!-- validation -->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.10</version>
            </dependency>
            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>11.2.0.3</version>
            </dependency>
            <!-- 集成mybatis -->
            <!-- <dependency>
                 <groupId>org.mybatis.spring.boot</groupId>
                 <artifactId>mybatis-spring-boot-starter</artifactId>
                 <version>1.3.4</version>
             </dependency>-->
            <!-- 分布式事务 -->
            <dependency>
                <groupId>com.jnby</groupId>
                <artifactId>tx-queue</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>ST4</artifactId>
                <version>4.3.1</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>stringtemplate</artifactId>
                <version>3.2.1</version>
                <optional>true</optional>
            </dependency>
            <!-- 分页 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>4.1.0</version>
            </dependency>
            <!-- validation -->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>

            <!-- xxl-job -->
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy</artifactId>
                <version>2.5.8</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.34.Final</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.5</version>
            </dependency>
            <dependency>
                <groupId>org.spring.center.job</groupId>
                <artifactId>jic-job-core</artifactId>
                <version>2.0.0</version>
            </dependency>

            <!-- histrx -->
            <dependency>
                <groupId>com.netflix.hystrix</groupId>
                <artifactId>hystrix-core</artifactId>
                <version>1.5.18</version>
            </dependency>
            <dependency>
                <groupId>io.reactivex</groupId>
                <artifactId>rxjava-reactive-streams</artifactId>
                <version>1.2.1</version>
            </dependency>

            <!-- lomback -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.16</version>
            </dependency>

            <!-- unit test -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>3.8.1</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.easymock</groupId>
                <artifactId>easymockclassextension</artifactId>
                <version>2.4</version>
            </dependency>

            <!-- 七牛 -->
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>7.2.0</version>
            </dependency>
            <!-- easyExcel 解析 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.0.0-beta1</version>
            </dependency>
            <!-- swagger -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.9.2</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>2.9.2</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>1.9.6</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>172.18.0.152</id>
            <name>Jnby Repository</name>
            <url>http://172.18.0.152:8081/repository/maven-public/</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
                <!--                <updatePolicy>always</updatePolicy>-->
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <!--                <updatePolicy>always</updatePolicy>-->
            </snapshots>
        </repository>
    </repositories>
    <!-- 打包发布 -->
    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://172.18.0.152:8081/repository/maven-releases</url>
        </repository>

        <snapshotRepository>
            <id>snapshots</id><!--这个ID需要与你的snapshots仓库的Repository ID一致-->
            <url>http://172.18.0.152:8081/repository/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>

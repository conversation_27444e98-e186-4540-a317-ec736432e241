package org.springcenter.box.activity.api.fallback;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.api.IBoxActivityApi;
import org.springcenter.box.activity.api.dto.CActivityUpdateTaskReq;
import org.springcenter.box.activity.api.dto.JoinRecordQueryReq;
import org.springcenter.box.activity.api.dto.JoinRecordQueryResp;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

@Slf4j
@Component
public class JicBoxActivityServiceFallbackFactory implements IBoxActivityApi {
    @Override
    public ResponseResult<Boolean> updateTask(CommonRequest<CActivityUpdateTaskReq> request) {
        log.error("updateTask: 服务熔断");
        return ResponseResult.success(false);
    }

    @Override
    public ResponseResult<JoinRecordQueryResp> queryJoinRecord(@Validated @RequestBody CommonRequest<JoinRecordQueryReq> request) {
        log.error("queryJoinRecord: 服务熔断");
        return ResponseResult.success(null);
    }

}

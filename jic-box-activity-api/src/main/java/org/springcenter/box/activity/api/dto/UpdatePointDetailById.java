package org.springcenter.box.activity.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class UpdatePointDetailById {

    @ApiModelProperty(value = "id")
    @NotBlank(message = "id不能为空")
    private String id;


    private BigDecimal point;

    private String memo;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activeTime;


    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;




    @ApiModelProperty(value = "修改人不能为空")
    @NotBlank(message = "修改人不能为空不能为空")
    private String updateBy;


    private Long delFlag;
}

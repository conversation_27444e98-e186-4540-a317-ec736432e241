package org.springcenter.box.activity.api.fallback;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.api.IBoxActivityApi;
import org.springcenter.box.activity.api.IUserPointApi;
import org.springcenter.box.activity.api.dto.*;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

@Slf4j
@Component
public class UserPointServiceFallbackFactory implements IUserPointApi  {

    @Override
    public ResponseResult addPoint(CommonRequest<AddPointReqDto> request) {
        log.error("addPoint: 服务熔断");
        return ResponseResult.error();
    }

    @Override
    public ResponseResult subtractPoint(CommonRequest<SubtractPointReqDto> request) {

        log.error("subtractPoint: 服务熔断");
        return ResponseResult.error();
    }


    @Override
    public ResponseResult<BJstPushOrder> getLogistics(CommonRequest<GetLogisticsReqDto> request) {
        log.error("getLogistics: 服务熔断");
        return ResponseResult.error();
    }
}

package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class SubtractPointReqDto {
    @ApiModelProperty(value = "退款id")
    @NotBlank(message = "退款id不能为空")
    private String  refundId;


    @ApiModelProperty(value = "退款单号")
    @NotBlank(message = "退款单号不能为空")
    private String  refundSn;


    @ApiModelProperty(value = "订阅id")
    @NotBlank(message = "订阅id不能为空")
    private String subId;



    @ApiModelProperty(value = "unionId")
    @NotBlank(message = "unionId不能为空")
    private String unionId;


    @ApiModelProperty(value = "customerId")
    @NotBlank(message = "customerId不能为空")
    private String customerId;
    

}

package org.springcenter.box.activity.api.dto;

import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@ApiModel("服务单参与明细入参")
@Data
public class JoinRecordQueryReq {
    @ApiModelProperty(value = "box单号")
    private String boxSn;
    @ApiModelProperty(value = "boxId，小程序端必填。")
    private String boxId;
    @ApiModelProperty(value = "unionId，小程序端必填。暂时不能强制校验，后台目前使用的是BoxSn查询", required = true)
    private String unionId;

    public void check() {
        if (StringUtils.isBlank(boxId)) {
            Preconditions.checkArgument(StringUtils.isNotBlank(boxSn), "boxId与boxSn二选一必填");
        }
        if (StringUtils.isBlank(boxSn)) {
            Preconditions.checkArgument(StringUtils.isNotBlank(boxId), "boxId与boxSn二选一必填");
        }
    }

    public void checkMini() {
        Preconditions.checkArgument(StringUtils.isNotBlank(boxId), "boxId不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(unionId), "unionId不能为空");
    }
}

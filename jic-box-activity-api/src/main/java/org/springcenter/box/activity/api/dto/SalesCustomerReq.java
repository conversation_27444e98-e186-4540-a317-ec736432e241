package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导购会员列表查询入参
 */
@Data
public class SalesCustomerReq extends BaseSalesCustomerReq implements Serializable {

    @ApiModelProperty(value = "用户手机号")
    private String userPhone;

    @ApiModelProperty(value = "用户品牌卡号")
    private String cardNo;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty(value = "计划月份")
    private String planMonth;

    @ApiModelProperty(value = "最小金额")
    private long minPrice;

    @ApiModelProperty(value = "最大金额")
    private long maxPrice;

    @ApiModelProperty(value = "搜索关键词")
    private String q;

    @ApiModelProperty(value = "订阅期筛选开始时间格式 yyyy-MM")
    private String subStartTime;

    @ApiModelProperty(value = "订阅期筛选结束时间格式 yyyy-MM")
    private String subEndTime;

    @ApiModelProperty(value = "是否展示打标,1是，0否")
    private String subTag;
}

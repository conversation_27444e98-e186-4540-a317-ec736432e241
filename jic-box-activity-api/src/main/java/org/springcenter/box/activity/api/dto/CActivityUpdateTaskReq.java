package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel("计算入参")
@Data
public class CActivityUpdateTaskReq {
    @ApiModelProperty(value = "活动ID")
    private String activityId;
    @ApiModelProperty(value = "BOX单号")
    @NotBlank(message = "BOX单号不能为空")
    private String boxNo;
    @ApiModelProperty(value = "任务类型：1=付款成功，2=退款成功")
    @NotNull(message = "任务类型不能为空")
    private Integer type;
    @ApiModelProperty(value = "消费者ID", required = true)
    private String unionId;

    public void check() {
        if (type == null || (type != 1 && type != 2)) {
            throw new RuntimeException("任务类型不正确，请检查");
        }
        if (StringUtils.isBlank(boxNo)) {
            throw new RuntimeException("BOX单号不能为空");
        }
    }
}

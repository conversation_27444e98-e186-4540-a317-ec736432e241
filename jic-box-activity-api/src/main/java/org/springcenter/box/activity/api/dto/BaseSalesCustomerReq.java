package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class BaseSalesCustomerReq implements Serializable {
    @ApiModelProperty(value = "门店下导购的hrId")
    @NotEmpty(message = "门店下导购的hrId不能为空")
    private List<Long> hrIds;

    @ApiModelProperty(value = "门店ID集合")
    @NotEmpty(message = "门店下导购的归属门店不能为空")
    private List<Long> cStoreIds;
}

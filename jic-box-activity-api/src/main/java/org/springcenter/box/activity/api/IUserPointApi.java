package org.springcenter.box.activity.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;

import io.swagger.annotations.ApiOperation;
import org.springcenter.box.activity.api.dto.*;
import org.springcenter.box.activity.api.fallback.UserPointServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "jic-box-activity-center",fallbackFactory = UserPointServiceFallbackFactory.class)
public interface IUserPointApi {


    /**
     * 增加点数
     */

    @PostMapping("/user/point/addPoint")
    ResponseResult addPoint(@RequestBody CommonRequest<AddPointReqDto> request);


    /**
     * 减去点数
     * @param request
     * @return
     */
    @PostMapping("/user/point/subtractPoint")
    ResponseResult subtractPoint(@RequestBody CommonRequest<SubtractPointReqDto> request);




    /**
     * 获取物流信息
     * @param request
     * @return
     */
    @PostMapping("/jst/pushOrder/getLogistics")
    ResponseResult<BJstPushOrder> getLogistics(@RequestBody CommonRequest<GetLogisticsReqDto> request);

}

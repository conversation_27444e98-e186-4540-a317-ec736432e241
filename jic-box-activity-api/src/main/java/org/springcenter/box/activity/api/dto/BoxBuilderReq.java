package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;

@ApiModel("搭盒人员请求")
@Data
public class BoxBuilderReq {
    @ApiModelProperty(value = "搭盒人员：1=搭配师,2=导购")
    @NotNull(message = "搭盒人员类型不能为空")
    private Integer builderType;
    @ApiModelProperty(value = "导购fashionerId：如果类型为导购,则必填。")
    private String fashionerId;

    public void check() {
        if (this.builderType != null && this.builderType == 2 && StringUtils.isBlank(this.fashionerId)) {
            throw new RuntimeException("导购ID不能为空");
        }
    }
}

package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class AddPointByOmReqDto {

    @ApiModelProperty(value = "unionId")
    @NotBlank(message = "unionId不能为空")
    private String unionId;


    @ApiModelProperty(value = "customerId")
    @NotBlank(message = "customerId不能为空")
    private String customerId;

    @ApiModelProperty(value = "点数")
    @NotNull(message = "点数不能为空")
    private BigDecimal point;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "创建人")
    @NotBlank(message = "创建人不能为空")
    private String createBy;

}

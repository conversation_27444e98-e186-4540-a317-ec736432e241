package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("活动详情回参")
@Data
public class CActivityInfoResp {
    @ApiModelProperty(value = "活动ID")
    private String id;
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    @ApiModelProperty(value = "创建时间")
    private String createPerson;
    @ApiModelProperty(value = "活动开始时间")
    private String startTime;
    @ApiModelProperty(value = "活动结束时间")
    private String endTime;
    @ApiModelProperty(value = "评估时间")
    private String evaluateTime;
    @ApiModelProperty(value = "多选参与人群：1=新客+非自提盒子，2=新客+自提盒子，3=老客+非自提盒子，4=老客+自提盒子")
    private List<Integer> crowd;
    @ApiModelProperty(value = "搭盒人员-导购")
    private Boolean boxBuilderGuide;
    @ApiModelProperty(value = "搭盒人员-导购-门店包ID")
    private String boxBuilderGuideStore;
    @ApiModelProperty(value = "搭盒人员-搭配师")
    private Boolean boxBuilderFashioner;
    @ApiModelProperty(value = "盒子类型-订阅盒子")
    private Boolean boxTypeSubscription;
    @ApiModelProperty(value = "盒子类型-单次盒子")
    private Boolean boxTypeSingle;
    @ApiModelProperty(value = "商品范围：1=所有，2=指定商品过滤且满足金额门槛，3=满足门槛且含1件指定商品")
    private Integer boxBrandType;
    @ApiModelProperty(value = "指定商品包ID,多个英文逗号分隔")
    private String boxBrandList;
    @ApiModelProperty(value = "门槛单位：1=元，2=件", required = true)
    private Integer unit;
    @ApiModelProperty(value = "达成门槛：元/件")
    private BigDecimal threshold;

    @ApiModelProperty(value = "活动状态：1=未开始,2=进行中,3=核算中,4=待发放,5=已结束,6=下架")
    private Integer status;

    @ApiModelProperty(value = "发放任务状态：0=待核算，1=待发放，2=发放中")
    private Integer giftSendTaskStatus;

    @ApiModelProperty(value = "赠品")
    private CActivityGift gift;

    @Data
    public static class CActivityGift {
        @ApiModelProperty(value = "赠品ID")
        private String id;
        @ApiModelProperty(value = "赠品名称")
        private String giftName;
        @ApiModelProperty(value = "赠品类型：1=外部实物，2=内部实物，3=兑换卡")
        private Integer giftType;
        @ApiModelProperty(value = "活动ID")
        private String activityId;
        @ApiModelProperty(value = "凭证ID：sku、券号、外部兑换地址")
        private String voucherId;
        @ApiModelProperty(value = "价值")
        private BigDecimal price;
        @ApiModelProperty(value = "总数")
        private Long totalNum;
        @ApiModelProperty(value = "库存")
        private Long remainNum;
        @ApiModelProperty(value = "图片地址：多个英文逗号分割")
        private String picList;
    }
}

package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("明细列表回参")
@Data
public class JoinRecordInfoResp {
    @ApiModelProperty(value = "记录ID")
    private String id;
    @ApiModelProperty(value = "BOX单号")
    private String boxNo;
    @ApiModelProperty(value = "boxId")
    private String boxId;
    @ApiModelProperty(value = "活动ID")
    private String activityId;
    @ApiModelProperty(value = "DD单号集合：英文逗号分隔")
    private String orderNoList;
    @ApiModelProperty(value = "实付金额")
    private BigDecimal payment;
    @ApiModelProperty(value = "实付件数")
    private Integer quantity;
    @ApiModelProperty(value = "达成状态:0=不可发，1=可发")
    private Integer accomplishStatus;
    @ApiModelProperty(value = "物流名称")
    private String logistics;
    @ApiModelProperty(value = "物流单号")
    private String logisticsNo;
    @ApiModelProperty(value = "地址配置ID")
    private String addressId;
    @ApiModelProperty(value = "收件人姓名")
    private String addressName;
    @ApiModelProperty(value = "收件人手机")
    private String addressPhone;
    @ApiModelProperty(value = "省")
    private String addressProvince;
    @ApiModelProperty(value = "市")
    private String addressCity;
    @ApiModelProperty(value = "区")
    private String addressDistrict;
    @ApiModelProperty(value = "地址")
    private String address;
    @ApiModelProperty(value = "用户unionId")
    private String unionId;

    @ApiModelProperty(value = "发放状态:0=待发放，1=发放中等待回调，2=发放成功，3=发放失败")
    private Integer giftSendStatus;
    @ApiModelProperty(value = "失败原因")
    private String giftSendErrorMsg;
    @ApiModelProperty(value = "操作备注")
    private String optRemark;
    @ApiModelProperty(value = "ERP订单界面-外部单号")
    private String soId;

    @ApiModelProperty(value = "兑换地址")
    private String exchangeUrl;
    @ApiModelProperty(value = "兑换卡号")
    private String exchangeNumber;
    @ApiModelProperty(value = "兑换密钥")
    private String exchangeSecret;
}

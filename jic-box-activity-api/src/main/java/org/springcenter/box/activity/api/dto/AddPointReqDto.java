package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class AddPointReqDto {
    @ApiModelProperty(value = "订单id")
    @NotBlank(message = "订单id不能为空")
    private String  orderId;


    @ApiModelProperty(value = "订单单号")
    @NotBlank(message = "订单单号不能为空")
    private String  orderSn;


    @ApiModelProperty(value = "订阅id")
    @NotBlank(message = "订阅id不能为空")
    private String subId;



    @ApiModelProperty(value = "unionId")
    @NotBlank(message = "unionId不能为空")
    private String unionId;


    @ApiModelProperty(value = "customerId")
    @NotBlank(message = "customerId不能为空")
    private String customerId;

}

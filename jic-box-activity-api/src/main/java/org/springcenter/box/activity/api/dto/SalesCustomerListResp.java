package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

//导购归属会员列表实体
@Data
public class SalesCustomerListResp extends BaseSalesCustomerResp implements Serializable {

    @ApiModelProperty(value = "true黑名单 false不是黑名单")
    private Boolean isBlack = false;

    @ApiModelProperty(value = "累计消费金额")
    private BigDecimal cumulativeTotalConsume;

    @ApiModelProperty(value = "是否含有订阅计划节点")
    private Boolean haveSubscribe = false;

    @ApiModelProperty(value = "BOX中的用户ID")
    private String userId;

    @ApiModelProperty(value = "卡等级,1银卡，2金卡，3白金卡")
    private int levelId;

    @ApiModelProperty(value = "会员归属品牌")
    private long weid;

    private String subId;

    @ApiModelProperty(value = "含有订阅计划信息")
    private SubscribePlan subscribePlan;

    @ApiModelProperty(value = "总点数")
    private BigDecimal totalPoint;

    @ApiModelProperty(value = "可用点数")
    private BigDecimal canUsePoint;

    @ApiModelProperty(value = "自主要盒标签，0为否，1是")
    private int selfBoxTag;

    @Data
    public static class SubscribePlan{

        @ApiModelProperty(value = "订阅器内累计消费金额")
        private String totalPrice;

        @ApiModelProperty(value = "订阅器内下个节点月份")
        private String dateStr;

        @ApiModelProperty(value = "订阅器节点月份")
        private String monthStr;

        @ApiModelProperty(value = "订阅ID")
        private String subId;
    }
}

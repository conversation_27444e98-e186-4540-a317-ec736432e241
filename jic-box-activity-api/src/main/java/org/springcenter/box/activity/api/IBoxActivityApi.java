package org.springcenter.box.activity.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.ApiOperation;
import org.springcenter.box.activity.api.dto.CActivityUpdateTaskReq;
import org.springcenter.box.activity.api.dto.JoinRecordQueryReq;
import org.springcenter.box.activity.api.dto.JoinRecordQueryResp;
import org.springcenter.box.activity.api.fallback.JicBoxActivityServiceFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "jic-box-activity-center", fallbackFactory = JicBoxActivityServiceFallbackFactory.class)
public interface IBoxActivityApi {

    @ApiOperation(value = "任务更新")
    @PostMapping("/guider/c/activity/updateTask")
    ResponseResult<Boolean> updateTask(@RequestBody CommonRequest<CActivityUpdateTaskReq> request);

    @ApiOperation(value = "根据BOX单号查询满赠活动达成状态")
    @PostMapping("/guider/c/activity/join/record/query")
    ResponseResult<JoinRecordQueryResp> queryJoinRecord(@Validated @RequestBody CommonRequest<JoinRecordQueryReq> request);
}

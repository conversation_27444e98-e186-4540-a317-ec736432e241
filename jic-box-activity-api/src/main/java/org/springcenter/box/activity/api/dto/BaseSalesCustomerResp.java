package org.springcenter.box.activity.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseSalesCustomerResp implements Serializable {
    @ApiModelProperty(value = "卡号")
    private String cardNo;

    @ApiModelProperty(value = "手机号")
    private String mobil;

    @ApiModelProperty(value = "品牌卡名称")
    private String brandName;

    @ApiModelProperty(value = "unionid")
    private String unionid;

    @ApiModelProperty(value = "头像")
    private String headimgurl;

    @ApiModelProperty(value = "性别")
    private String sex;

    @ApiModelProperty(value = "用户昵称")
    private String nickname;

    @ApiModelProperty(value = "归属导购")
    private String clerkName;

    @ApiModelProperty(value = "归属导购ID，hremployid")
    private long clerkId;

    @ApiModelProperty(value = "门店ID")
    private long storeId;
}

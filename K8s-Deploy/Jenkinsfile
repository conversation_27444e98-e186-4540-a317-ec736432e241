pipeline{
    agent any
    stages {
       stage('Git'){
          steps{
              git branch: '$branch', credentialsId: '8551cc53-dc15-4df0-9fa7-2c720d375812', url: '***********************:box-group/jic-box-activity.git'}
            }
        stage('构建Maven项目') {
            steps {
              script{
                    if(mv == 'jic-box-activity-center-gateway'){
                    echo '构建jic-box-activity-center-gateway'
                    sh '''
                       export JAVA_HOME=/usr/local/java/jdk1.8.0_162
                       /usr/local/java/maven/apache-maven-3.5.3/bin/mvn clean install -pl  jic-box-activity-center-gateway -am -DskipTests -U
                    '''
                    }
                    if(mv == 'jic-box-activity-center'){
                    echo '构建jic-box-activity-center'
                    sh '''
                       export JAVA_HOME=/usr/local/java/jdk1.8.0_162
                       /usr/local/java/maven/apache-maven-3.5.3/bin/mvn clean install -pl  jic-box-activity-center -am -DskipTests -U
                    '''
                    }
              }
            }
        }
        stage('Build & Push Image'){
            steps{
              script {
                if(profiles == 'test'){
                    echo '测试镜像build'
                    sh '''
                       Registry=jnbyharbor.jnby.com
                       docker login -u admin -p Windows2023 $Registry
                       cd ${Projects}
                       docker build -t $Registry/box-group/jic-box-activity/${Projects}:${version} -f Dockerfile .
                       docker push $Registry/box-group/jic-box-activity/${Projects}:${version}
                    '''
                }
                if(profiles == 'prod'){
                    echo '正式镜像build'
                    sh '''
                       Registry=jnbyharbor.jnby.com
                       docker login -u admin -p Windows2023 $Registry
                       pwd
                       cd ${Projects}
                       docker build -t $Registry/box-group/jic-box-activity/${Projects}:${version} -f Dockerfile .
                       docker push $Registry/box-group/jic-box-activity/${Projects}:${version}
                    '''
                }
            }
          }
        }
        stage('Deploy to K8S'){
            steps{
              script {
                 if (profiles == 'test') {
                    echo '接下来进行测试项目的发布...'
                    sh '''
                      cd K8s-Deploy
                      sed -i "s@{api-replicas}@${api_replicas}@g" jic-${deploy}-test.yaml
                      sed -i "/image/{s/latest/${version}/}" jic-${deploy}-test.yaml
                      sed -i "/profiles/{s/profiles/${profiles}/}" jic-${deploy}-test.yaml
                      kubectl config use-context k8s-test
                      kubectl apply -f  jic-${deploy}-test.yaml
                    '''
                }
                if (profiles == 'prod') {
                   echo '接下来进行生产的发布...'
                   sh '''
                     cd K8s-Deploy
                     sed -i "s@{api-replicas}@${api_replicas}@g" jic-${deploy}-prod.yaml
                     sed -i "/image/{s/latest/${version}/}" jic-${deploy}-prod.yaml
                     sed -i "/profiles/{s/profiles/${profiles}/}" jic-${deploy}-prod.yaml
                     kubectl config use-context k8s-prod
                     kubectl apply -f  jic-${deploy}-prod.yaml
                    '''
                }
            }
            }
        }
        stage ('Clean dockerimages and show img version') {
           steps{
             script {
                  echo "移除本地构建的镜像"
                  sh '''
                    docker image rm -f `docker image ls | grep jic-box-activity/${Projects} | awk '{print $3}'`
                   '''
                currentBuild.description = "jic-box-activity/${Projects}-${profiles}:${version}"
             }
            }
        }
    }
}




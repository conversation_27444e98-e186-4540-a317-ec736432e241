apiVersion: apps/v1
kind: Deployment
metadata:
  name: jic-box-activity-center
  labels:
    app: jic-box-activity-center
spec:
  replicas: 1
  template:
    metadata:
      name: jic-box-activity-center
      labels:
        app: jic-box-activity-center
    spec:
      containers:
        - name: jic-box-activity-center
          image: "harbor.jnby.com/jic-box-activity/jic-box-activity-center:latest"
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9701
              name: box-activity
              protocol: TCP
          lifecycle:
            preStop:
              exec:
                command:
                  - sh
                  - c
                  - "sleep 5"
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 9701
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 9701
            timeoutSeconds: 1
      restartPolicy: Always
      imagePullSecrets:
        - name: 152harbor

  selector:
    matchLabels:
      app: jic-box-activity-center


apiVersion: apps/v1
kind: Deployment
metadata:
  name: jic-box-activity-center-prod
  namespace: box
  labels:
    web: jic-box-activity-center-prod
spec:
  replicas: {api-replicas}
  selector:
    matchLabels:
      web: jic-box-activity-center-prod
  template:
    metadata:
      labels:
        web: jic-box-activity-center-prod
    spec:
      containers:
      - name: jic-box-activity-center-prod
        image: jnbyharbor.jnby.com/box-group/jic-box-activity/jic-box-activity-center:latest
        imagePullPolicy: "Always"
        ports:
        - containerPort: 9701
        env:
        - name: profiles
          value: prod
        lifecycle:
          preStop:
            exec:
              command:
                - sh
                - c
                - "sleep 5"
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 20
          successThreshold: 1
          tcpSocket:
            port: 9701
          timeoutSeconds: 1
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 20
          successThreshold: 1
          tcpSocket:
            port: 9701
          timeoutSeconds: 1
      restartPolicy: Always
      imagePullSecrets:
      - name: box-group

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    prometheus.io/path: /actuator/prometheus
    prometheus.io/port: "9701"
    prometheus.io/scrape: "true"
  name: jic-box-activity-center-prod
  namespace: box
  labels:
    web: jic-box-activity-center-prod
spec:
  type: ClusterIP
  ports:
    - port: 9701
  selector:
    web: jic-box-activity-center-prod


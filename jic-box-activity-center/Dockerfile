FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.3

MAINTAINER box-group

RUN mkdir -p /jic-box-activity/jic-box-activity-center

WORKDIR /jic-box-activity/jic-box-activity-center

EXPOSE 9701

COPY target/jic-box-activity-center.jar jic-box-activity-center.jar

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java", "-Xmx4g", "-Xms4g","-XX:NewRatio=3","-Xss512k", "-Xmn2g","-XX:SurvivorRatio=2", "-XX:+UseParallelGC", "-Dreactor.netty.pool.leasingStrategy=lifo", "-jar", "jic-box-activity-center.jar"]

CMD ["--spring.profiles.active=prod"]



package org.springcenter.box.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.convert.CActivityConvertor;
import org.springcenter.box.activity.domain.box.Box;
import org.springcenter.box.activity.domain.box.CActivityJoinRecord;
import org.springcenter.box.activity.dto.request.CActivityEffectReq;
import org.springcenter.box.activity.dto.request.CActivityListReq;
import org.springcenter.box.activity.enums.FrontActivityStatusEnum;
import org.springcenter.box.activity.mapper.box.*;
import org.springcenter.box.activity.service.*;
import org.springcenter.box.activity.service.crowd.CrowdFilterService;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class CActivityServiceImplTest {

    @InjectMocks
    private CActivityServiceImpl activityService;


    @Mock
    private CActivityConvertor cActivityConvertor;

    @Mock
    private CActivityMapper cActivityMapper;

    @Mock
    private CActivityGiftMapper cActivityGiftMapper;

    @Mock
    private FashionerMapper fashionerMapper;

    @Mock
    private IStoreCenterHttpService storeCenterHttpService;

    @Mock
    private CrowdFilterService crowdFilterService;

    @Mock
    private BoxMapper boxMapper;

    @Mock
    private CActivityJoinRecordMapper cActivityJoinRecordMapper;

    @Mock
    private IOrderService orderService;

    @Mock
    private IGiftService giftService;

    @Mock
    private IJoinRecordService joinRecordService;

    @Mock
    private ILogService logService;

    @Mock
    private IProductHttpService iProductHttpService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 当盒子不存在时，抛出异常
     */
    @Test
    public void effectActivity_BoxDoesNotExist_ThrowsException() {
        CActivityEffectReq requestData = new CActivityEffectReq();
        requestData.setBoxNo("boxNo");
        requestData.setUnionId("unionId");

        when(boxMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        assertThrows(BoxActivityException.class, () -> activityService.effectActivity(requestData));
    }

    /**
     * 当盒子已参加活动时，抛出异常
     */
    @Test
    public void effectActivity_BoxAlreadyParticipated_ThrowsException() {
        CActivityEffectReq requestData = new CActivityEffectReq();
        requestData.setBoxNo("boxNo");
        requestData.setUnionId("unionId");

        Box box = new Box();
        box.setBoxSn("boxNo");
        box.setUnionid("unionId");

        when(boxMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(box);

        CActivityJoinRecord joinRecord = new CActivityJoinRecord();
        joinRecord.setBoxSn("boxNo");
        joinRecord.setIsDelete(0);

        when(cActivityJoinRecordMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(joinRecord);

        assertThrows(BoxActivityException.class, () -> activityService.effectActivity(requestData));
    }

    /**
     * 当盒子已参加活动时，返回空列表
     */
    @Test
    public void effectActivity_NoRunningActivities_ReturnsEmptyList() {
        CActivityEffectReq requestData = new CActivityEffectReq();
        requestData.setBoxNo("boxNo");
        requestData.setUnionId("unionId");

        Box box = new Box();
        box.setBoxSn("boxNo");
        box.setUnionid("unionId");

        when(boxMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(box);
        when(cActivityJoinRecordMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        CActivityListReq listReq = new CActivityListReq();
        listReq.setActivityStatus(FrontActivityStatusEnum.RUNNING.getCode());

        when(activityService.activityList(listReq, new Page(1, 20))).thenReturn(Collections.emptyList());

        List<CActivityInfoResp> result = activityService.effectActivity(requestData);

        assertEquals(Collections.emptyList(), result);
    }

}

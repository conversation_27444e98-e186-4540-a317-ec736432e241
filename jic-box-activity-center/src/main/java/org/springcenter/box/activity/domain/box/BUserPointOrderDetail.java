package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: lwz
 * @Date: 2024-12-11 14:11:52
 * @Description: 
 */
@TableName("B_USER_POINT_ORDER_DETAIL")
@ApiModel(value="BUserPointOrderDetail对象", description="")
public class BUserPointOrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;

    @TableField("UNION_ID")
    private String unionId;


    @TableField("CUSTOMER_ID")
    private String customerId;

    @TableField("USED_POINT")
    private BigDecimal usedPoint;

    @TableField("ORDER_ID")
    private String orderId;

    @TableField("ORDER_SN")
    private String orderSn;

    @TableField("SKU_ID")
    private String skuId;

    @TableField("SKU_NO")
    private String skuNo;

    @TableField("SPU_ID")
    private String spuId;

    @TableField("SPU_NO")
    private String spuNo;

    @TableField("IMG_URL")
    private String imgUrl;

    @TableField("GOODS_NAME")
    private String goodsName;


    @ApiModelProperty(value = "颜色")
    @TableField("COLOR_NAME")
    private String colorName;


    @ApiModelProperty(value = "颜色")
    @TableField("COLOR_NO")
    private String colorNo;


    @ApiModelProperty(value = "规格")
    @TableField("SIZE_NAME")
    private String sizeName;

    @ApiModelProperty(value = "规格")
    @TableField("SIZE_NO")
    private String sizeNo;

    @ApiModelProperty(value = "地址快照")
    @TableField("LOGISTICS_SNAPSHOT_ID")
    private String logisticsSnapshotId;


    @TableField("CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField("DEL_FLAG")
    private Long delFlag;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public BigDecimal getUsedPoint() {
        return usedPoint;
    }

    public void setUsedPoint(BigDecimal usedPoint) {
        this.usedPoint = usedPoint;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getSpuId() {
        return spuId;
    }

    public void setSpuId(String spuId) {
        this.spuId = spuId;
    }

    public String getSpuNo() {
        return spuNo;
    }

    public void setSpuNo(String spuNo) {
        this.spuNo = spuNo;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getColorName() {
        return colorName;
    }

    public void setColorName(String colorName) {
        this.colorName = colorName;
    }

    public String getColorNo() {
        return colorNo;
    }

    public void setColorNo(String colorNo) {
        this.colorNo = colorNo;
    }

    public String getSizeName() {
        return sizeName;
    }

    public void setSizeName(String sizeName) {
        this.sizeName = sizeName;
    }

    public String getSizeNo() {
        return sizeNo;
    }

    public void setSizeNo(String sizeNo) {
        this.sizeNo = sizeNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Long delFlag) {
        this.delFlag = delFlag;
    }


    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }


    public String getLogisticsSnapshotId() {
        return logisticsSnapshotId;
    }

    public void setLogisticsSnapshotId(String logisticsSnapshotId) {
        this.logisticsSnapshotId = logisticsSnapshotId;
    }

    @Override
    public String toString() {
        return "BUserPointOrderDetail{" +
                "id='" + id + '\'' +
                ", unionId='" + unionId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", usedPoint=" + usedPoint +
                ", orderId='" + orderId + '\'' +
                ", orderSn='" + orderSn + '\'' +
                ", skuId='" + skuId + '\'' +
                ", skuNo='" + skuNo + '\'' +
                ", spuId='" + spuId + '\'' +
                ", spuNo='" + spuNo + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", colorName='" + colorName + '\'' +
                ", colorNo='" + colorNo + '\'' +
                ", sizeName='" + sizeName + '\'' +
                ", sizeNo='" + sizeNo + '\'' +
                ", logisticsSnapshotId='" + logisticsSnapshotId + '\'' +
                ", createTime=" + createTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                '}';
    }
}

package org.springcenter.box.activity.api;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Preconditions;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.config.redis.RedissonUtil;
import org.springcenter.box.activity.domain.box.BPointDetail;
import org.springcenter.box.activity.domain.box.BUserPointOrderDetail;
import org.springcenter.box.activity.domain.box.UserPointOrderWithDetail;
import org.springcenter.box.activity.dto.request.*;
import org.springcenter.box.activity.service.IBUserPointOrderDetailService;
import org.springcenter.box.activity.service.ICombinePointOrderService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequestMapping("/point/order/")
@RestController
@Api(tags = "用户兑换订单")
@Slf4j
public class PointOrderController {


    @Resource
    private ICombinePointOrderService iCombinePointOrderService;


    @Resource
    private RedissonUtil redissonUtil;


    @Resource
    private IBUserPointOrderDetailService ibUserPointOrderDetailService;


    @ApiOperation(value = "获取所有兑换详细列表", notes = "获取所有兑换详细列表")
    @PostMapping("/allUserOrderDetailList")
    public ResponseResult allUserOrderList(@Validated @RequestBody CommonRequest<AllUserOrderDetailListReqDto> request) {
        AllUserOrderDetailListReqDto allUserOrderDetailListReqDto = Preconditions.checkNotNull(request.getRequestData(), "入参不能为空");
        Page page = request.getPage();
        return ResponseResult.success(iCombinePointOrderService.allUserOrderDetailList(allUserOrderDetailListReqDto, page),page);
    }




    @ApiOperation(value = "获取兑换订单列表", notes = "获取兑换订单列表")
    @PostMapping("/userOrderList")
    public ResponseResult<List<UserPointOrderWithDetail>> userOrderList(@Validated @RequestBody CommonRequest<UserOrderListReqDto> request) {
        UserOrderListReqDto userOrderListReqDto = Preconditions.checkNotNull(request.getRequestData(), "入参不能为空");
        Page page = request.getPage();
        return ResponseResult.success(iCombinePointOrderService.userOrderList(userOrderListReqDto.getUnionId(), page),page);
    }


    @ApiOperation(value = "获取兑换订单详情", notes = "获取兑换订单详情")
    @PostMapping("/userOrderInfo")
    public ResponseResult<UserPointOrderWithDetail>  userOrderInfo(@Validated @RequestBody CommonRequest<UserOrderInfoReqDto> request) {
        UserOrderInfoReqDto userOrderInfoReqDto = Preconditions.checkNotNull(request.getRequestData(), "入参不能为空");
        return ResponseResult.success(iCombinePointOrderService.userOrderInfo(userOrderInfoReqDto.getOrderId()));
    }

    @ApiOperation(value = "下单", notes = "下单")
    @PostMapping("/createOrder")
    public ResponseResult createOrder(@Validated @RequestBody CommonRequest<CreatePointOrderReqDto> request) {
        CreatePointOrderReqDto createPointOrderReqDto = Preconditions.checkNotNull(request.getRequestData(), "入参不能为空");
        log.info("下单unionId:{} request:{}", createPointOrderReqDto.getUnionId(), JSONObject.toJSONString(request));
        String key = "createOrder:" + createPointOrderReqDto.getCustomerId();
        if (!redissonUtil.tryLock(key)) {
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            iCombinePointOrderService.createPointOrder(createPointOrderReqDto);
        } finally {
            redissonUtil.unlock(key);
        }
        return ResponseResult.success();
    }


    @ApiOperation(value = "获取客户地址", notes = "获取客户地址")
    @PostMapping("/getCustomerLogistic")
    public ResponseResult getCustomerLogistic(@Validated @RequestBody CommonRequest<GetCustomerLogisticReqDto> commonRequest){
        GetCustomerLogisticReqDto getCustomerLogisticReqDto = Preconditions.checkNotNull(commonRequest.getRequestData(), "入参不能为空");

        return ResponseResult.success(iCombinePointOrderService.getCustomerLogistic(getCustomerLogisticReqDto));

    }


    @ApiOperation(value = "获取订单详细列表", notes = "获取订单详细列表")
    @PostMapping("/getOrderDetails")
    public ResponseResult<List<BUserPointOrderDetail>> getOrderDetails(@Validated @RequestBody CommonRequest<GetOrderDetailsReqDto> commonRequest) {
        GetOrderDetailsReqDto getCustomerLogisticReqDto = Preconditions.checkNotNull(commonRequest.getRequestData(), "入参不能为空");

        LambdaQueryWrapper<BUserPointOrderDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BUserPointOrderDetail::getOrderSn, getCustomerLogisticReqDto.getOrderSnList());
        return ResponseResult.success(ibUserPointOrderDetailService.list(queryWrapper));

    }


}

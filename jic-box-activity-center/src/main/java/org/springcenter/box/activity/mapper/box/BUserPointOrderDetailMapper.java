package org.springcenter.box.activity.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.box.activity.domain.box.BUserPointOrderDetail;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2024-12-11 14:11:52
 * @Description: Mapper
 */
public interface BUserPointOrderDetailMapper extends BaseMapper<BUserPointOrderDetail> {


    List<BUserPointOrderDetail> getByOrderIds(@Param("orderIds") List<String> orderIds);




    List<BUserPointOrderDetail> getBySpuIdOrOrderSn(@Param("spuId") String spuId,@Param("orderSn") String orderSn);
}

package org.springcenter.box.activity.service;

import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.domain.box.CActivity;
import org.springcenter.box.activity.domain.box.CActivityJoinRecord;

import java.util.List;

public interface IGiftService {

    /**
     * 扣减库存
     *
     * @param giftId
     * @return
     */
    Boolean deductInventory(String giftId);

    /**
     * 返还库存
     *
     * @param giftId
     * @return
     */
    Boolean returnInventory(String giftId);

    /**
     * 批量发放
     * @param giftType
     * @param updateList
     * @return
     */
    Boolean batchSend(Integer giftType, List<CActivityJoinRecord> updateList);
}

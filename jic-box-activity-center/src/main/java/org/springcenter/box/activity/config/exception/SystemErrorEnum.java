package org.springcenter.box.activity.config.exception;

import lombok.Getter;

@Getter
public enum SystemErrorEnum {
    /**
     * 1000-1999:内部异常
     * 2000-2999:会员异常
     * 3000-3999:订单异常
     * 4000-4999:优惠券异常
     * 5000-5999:积分异常
     */
    PARAM_ERROR(777, "参数异常"),
    UNIFIED_CODE_ERROR(888, "统一异常码，错误信息需要自定义覆盖"),
    UNKNOWN_ERROR(999, "未知异常"),
    AUTH_ERROR(1001, "auth不能为空"),
    AUTH_INVALID_ERROR(1002, "验签失败"),
    STORE_IS_NULL_ERROR(1003, "门店不存在"),
    MALL_IS_NULL_ERROR(1004, "商场不存在"),
    REPEAT_OPERATION_ERROR(1005, "正在执行中，请稍后再试"),
    ASSET_USE_POINTS_AND_COUPON_CAN_NOT_SUPPORT_ERROR(1006, "资产暂不支持都使用"),
    ASSET_USE_POINTS_CAN_NOT_SUPPORT_ERROR(1007, "积分资产暂不支持使用"),

    MEMBER_INTERFACE_TIMEOUT_ERROR(2000, "查询会员接口超时"),
    MEMBER_INTERFACE_API_ERROR(2001, "查询会员接口API请求异常"),
    MEMBER_NOT_EXIST_ERROR(2002, "会员不存在"),
    MEMBER_UNKNOWN_ERROR(2003, "查询会员接口未知异常"),

    COUPON_LIST_TIMEOUT_ERROR(4000, "查询优惠券超时"),
    COUPON_LIST_REQUEST_ERROR(4001, "查询优惠券接口请求异常"),
    COUPON_LIST_RESPONSE_ERROR(4002, "查询优惠券接口返回结果为空"),
    COUPON_LIST_UNKNOWN_ERROR(4003, "查询优惠券未知异常"),
    COUPON_CONFIG_CAN_NOT_USE_ERROR(4004, "不可使用券资产"),
    COUPON_USE_TIMEOUT_ERROR(4100, "使用优惠券超时"),
    COUPON_USE_UNKNOWN_ERROR(4101, "使用优惠券未知异常"),
    COUPON_USE_REQUEST_ERROR(4102, "使用优惠券接口请求异常"),
    COUPON_USE_RESPONSE_ERROR(4103, "使用优惠券接口返回结果为空"),
    COUPON_USE_PARTIAL_FAILURE_ERROR(4104, "使用优惠券部分失败"),
    COUPON_USE_CHECK_REQUEST_ERROR(4105, "检查券接口请求异常"),
    COUPON_USE_CHECK_RESPONSE_ERROR(4106, "券不存在，请重新选择"),
    COUPON_USE_CHECK_IS_USED_ERROR(4107, "券已使用"),
    COUPON_USE_CHECK_REPEAT_USED_ERROR(4108, "请勿重复使用"),
    COUPON_RETURN_REQUEST_ERROR(4200, "返还优惠券接口请求异常"),
    COUPON_RETURN_RESPONSE_ERROR(4201, "返还优惠券接口返回结果为空"),
    COUPON_RETURN_TIMEOUT_ERROR(4202, "返还优惠券超时"),
    COUPON_RETURN_UNKNOWN_ERROR(4203, "返还优惠券未知异常"),
    COUPON_RETURN_CHECK_REQUEST_ERROR(4205, "返还前检查券接口请求异常"),
    COUPON_RETURN_CHECK_RESPONSE_ERROR(4206, "券不存在，请重新选择"),
    COUPON_RETURN_CHECK_IS_USED_ERROR(4207, "券已使用"),
    COUPON_RETURN_CHECK_TIMEOUT_ERROR(4208, "返还优惠券前检查超时"),
    COUPON_RETURN_CHECK_UNKNOWN_ERROR(4209, "返还优惠券前检查未知异常"),
    COUPON_RETURN_CHECK_USE_ALL_NOT_FOUND_ERROR(4210, "没有检测到当前订单号使用成功的券记录"),
    COUPON_RETURN_CHECK_USE_PART_NOT_FOUND_ERROR(4211, "存在部分未核销成功的券，无法取消"),

    POINTS_CONFIG_CAN_NOT_USE_ERROR(5004, "不可使用积分资产"),
    POINTS_USE_REQUEST_ERROR(5100, "使用积分接口请求异常"),
    POINTS_USE_FAIL_ERROR(5101, "使用积分失败"),
    POINTS_USE_TIMEOUT_ERROR(5102, "使用积分超时"),
    POINTS_USE_UNKNOWN_ERROR(5103, "使用积分未知异常"),
    POINTS_RETURN_REQUEST_ERROR(5200, "返还积分接口请求异常"),
    POINTS_RETURN_FAIL_ERROR(5201, "返还积分失败"),
    POINTS_RETURN_TIMEOUT_ERROR(5202, "返还积分超时"),
    POINTS_RETURN_UNKNOWN_ERROR(5203, "返还积分未知异常"),


    ;

    private Integer errorCode;
    private String errorMsg;

    SystemErrorEnum(Integer errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }
}

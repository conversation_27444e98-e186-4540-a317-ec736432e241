package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcenter.box.activity.config.IdConfig;
import org.springcenter.box.activity.domain.box.CActivityOptLog;
import org.springcenter.box.activity.dto.LogDto;
import org.springcenter.box.activity.enums.OptTypeEnum;
import org.springcenter.box.activity.mapper.box.CActivityOptLogMapper;
import org.springcenter.box.activity.service.ILogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class LogServiceImpl extends ServiceImpl<CActivityOptLogMapper, CActivityOptLog> implements ILogService {
    @Resource
    private IdConfig idConfig;

    @Override
    public Boolean saveLog(String activityId, OptTypeEnum optType, String optPerson, LogDto oldContent, LogDto newContent) {
        return saveLog(activityId, optType, optPerson, oldContent, newContent, null);
    }

    @Override
    public Boolean saveLog(String activityId, OptTypeEnum optType, String optPerson, LogDto oldContent, LogDto newContent, String joinRecordId) {
        CActivityOptLog cActivityOptLog = new CActivityOptLog();
        cActivityOptLog.setId(idConfig.getLogId());
        cActivityOptLog.setCreateTime(new Date());
        cActivityOptLog.setUpdateTime(new Date());
        cActivityOptLog.setActivityId(activityId);
        cActivityOptLog.setJoinRecordId(joinRecordId);
        cActivityOptLog.setOptPerson(StringUtils.isNotBlank(optPerson) ? optPerson : "system");
        cActivityOptLog.setOptType(optType.getCode());
        cActivityOptLog.setOriginContent(JSON.toJSONString(oldContent));
        cActivityOptLog.setOptContent(JSON.toJSONString(newContent));
        cActivityOptLog.setCreateTime(new Date());
        cActivityOptLog.setUpdateTime(new Date());
        log.info("日志类型:[{}]，保存记录:{}", optType.getMsg(), JSON.toJSONString(cActivityOptLog));
        return this.save(cActivityOptLog);
    }
}

package org.springcenter.box.activity.api;


import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springcenter.box.activity.api.dto.AddPointByAdminReqDto;
import org.springcenter.box.activity.api.dto.AddPointByOmReqDto;
import org.springcenter.box.activity.api.dto.UpdatePointDetailById;
import org.springcenter.box.activity.domain.box.BPointDetail;
import org.springcenter.box.activity.domain.box.BSubscribeInfo;
import org.springcenter.box.activity.dto.request.AdminDetailListReqDto;
import org.springcenter.box.activity.dto.response.DetailListRespDto;
import org.springcenter.box.activity.service.IBPointDetailService;
import org.springcenter.box.activity.service.ICombineAccountService;
import org.springcenter.box.activity.service.ICombinePointDetailService;
import org.springcenter.box.activity.service.ICombinePointService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("admin/point/detail")
@RestController
@Api(tags = "后台用户点数")
@Slf4j
public class AdminPointDetailController {

    @Resource
    private ICombinePointService iCombinePointService;

    @Resource
    private ICombineAccountService iCombineAccountService;

    @Resource
    private ICombinePointDetailService iCombinePointDetailService;


    @Resource
    private IBPointDetailService ibPointDetailService;




    @ApiOperation(value = "后台增加点数", notes = "后台增加点数")
    @PostMapping("/addPointByOm")
    public ResponseResult addPointByOm(@Validated @RequestBody CommonRequest<AddPointByOmReqDto> request) {
        AddPointByOmReqDto addPointByOmReqDto = Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        log.info("后台增加点数request:{}", JSONObject.toJSONString(request));
        iCombineAccountService.getAccountByCustomerId(addPointByOmReqDto.getCustomerId());
        return ResponseResult.success(iCombinePointService.addPointByOm(addPointByOmReqDto));
    }




    @ApiOperation(value = "增加点数(专用)", notes = "增加点数(专用)")
    @PostMapping("/addPoint")
    public ResponseResult addPoint(@Validated @RequestBody CommonRequest<AddPointByAdminReqDto> request) {
        AddPointByAdminReqDto addPointByAdminReqDto = Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        log.info("Admin增加点数request:{}", JSONObject.toJSONString(request));
        iCombineAccountService.getAccountByCustomerId(addPointByAdminReqDto.getCustomerId());
        iCombinePointService.addPointByAdmin(addPointByAdminReqDto);
        return ResponseResult.success();
    }


    @ApiOperation(value = "修改信息", notes = "修改信息")
    @PostMapping("/updateById")
    public ResponseResult updateById(@Validated @RequestBody CommonRequest<UpdatePointDetailById> request) {
        UpdatePointDetailById updatePointDetailById = Preconditions.checkNotNull(request.getRequestData(), "入参不能为空");

        BPointDetail bPointDetail = new BPointDetail();
        bPointDetail.setId(updatePointDetailById.getId());
        if (ObjectUtils.isNotEmpty(updatePointDetailById.getPoint())) {
            bPointDetail.setPoint(updatePointDetailById.getPoint());
        }
        if (ObjectUtils.isNotEmpty(updatePointDetailById.getMemo())) {
            bPointDetail.setMemo(updatePointDetailById.getMemo());
        }
        if (ObjectUtils.isNotEmpty(updatePointDetailById.getActiveTime())) {
            bPointDetail.setActiveTime(updatePointDetailById.getActiveTime());
        }
        if (ObjectUtils.isNotEmpty(updatePointDetailById.getExpireTime())) {
            bPointDetail.setExpireTime(updatePointDetailById.getExpireTime());
        }
        bPointDetail.setUpdateBy(updatePointDetailById.getUpdateBy());
        bPointDetail.setDelFlag(updatePointDetailById.getDelFlag());
        bPointDetail.setUpdateTime(new Date());
        ibPointDetailService.updateById(bPointDetail);
        return ResponseResult.success();
    }



    @ApiOperation(value = "后台流水列表", notes = "后台流水列表")
    @PostMapping("/list")
    public ResponseResult<List<BPointDetail>> list(@Validated @RequestBody CommonRequest<AdminDetailListReqDto> request) {
        AdminDetailListReqDto adminDetailListReqDto = Preconditions.checkNotNull(request.getRequestData(), "入参不能为空");
        Page page = request.getPage();
        List<BPointDetail> bPointDetailList = iCombinePointService.getPointDetailListByUnionIdAndPage(adminDetailListReqDto.getUnionId(), page);
        return ResponseResult.success(bPointDetailList, page);
    }










}

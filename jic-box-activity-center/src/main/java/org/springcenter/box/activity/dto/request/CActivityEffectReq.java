package org.springcenter.box.activity.dto.request;

import org.apache.commons.lang3.StringUtils;
import org.springcenter.box.activity.api.dto.BoxBuilderReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@ApiModel("有效活动入参")
@Data
public class CActivityEffectReq {
    @ApiModelProperty(value = "当前总实付金额", required = true)
    @NotNull(message = "当前总实付金额不能为空")
    private BigDecimal payment;
    // TODO
    @ApiModelProperty(value = "BOX单号，下个版本下架")
    private String boxNo;
    @ApiModelProperty(value = "消费者ID", required = true)
    @NotBlank(message = "消费者ID不能为空")
    private String unionId;
    @ApiModelProperty(value = "搭盒人员", required = true)
    private BoxBuilderReq boxBuilderReq;

    @ApiModelProperty(value = "选择的商品", required = true)
    @NotEmpty(message = "选择的商品不能为空")
    @Valid
    private List<ProductReq> productList;

    @ApiModelProperty(value = "boxId，下个版本后续替代boxNo", required = true)
    private String boxId;

    public void check() {
        // boxNo 和 boxId 必选其一
        if (StringUtils.isBlank(boxNo) && StringUtils.isBlank(boxId)) {
            throw new RuntimeException("boxNo 和 boxId 必选其一");
        }
        boxBuilderReq.check();
    }

    @Data
    public static class ProductReq {
        @ApiModelProperty(value = "商品ID", required = true)
        @NotNull(message = "选择的商品ID不能为空")
        private Long spuId;

        @ApiModelProperty(value = "商品实付金额", required = true)
        @NotNull(message = "商品实付金额不能为空")
        private BigDecimal payment;

        @ApiModelProperty(value = "商品件数", required = true)
        @NotNull(message = "商品件数不能为空")
        private Integer quantity;
    }
}

package org.springcenter.box.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springcenter.box.activity.domain.box.BPointDetail;
import org.springcenter.box.activity.mapper.box.BPointDetailMapper;
import org.springcenter.box.activity.service.IBPointDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class HandleHistoryPointServiceImpl implements IHandleHistoryPointService {


    @Resource
    private BPointDetailMapper bPointDetailMapper;


    @Resource
    private IBPointDetailService ibPointDetailService;


   @Override
   public void handleHistoryPointChangeType() {
        int totalPage = 1;
        Page page = new Page(1, 800);

        List<BPointDetail> result = new ArrayList<>();
        while (page.getPageNo() <= totalPage) {
            List<BPointDetail> bPointDetailList = getNeedPointDetailList(page);
            if (CollectionUtils.isEmpty(bPointDetailList)) {
                break;
            } else {
                result.addAll(bPointDetailList);
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
        }

        if (CollectionUtils.isEmpty(result)) {
            return;
        }


        List<BPointDetail> needUpdatePointList = new ArrayList<>();
        result.forEach(e -> {
                BPointDetail updatePointDetail = new BPointDetail();
                updatePointDetail.setId(e.getId());
                updatePointDetail.setUpdateTime(new Date());
                updatePointDetail.setType(4L);
                needUpdatePointList.add(updatePointDetail);

        });
        if (CollectionUtils.isEmpty(needUpdatePointList)) {
            return;
        }

        List<List<BPointDetail>> parts = Lists.partition(needUpdatePointList, 800);
        parts.forEach(x -> {
            ibPointDetailService.updateBatchById(x);
        });
    }

    public List<BPointDetail> getNeedPointDetailList(Page page) {
        com.github.pagehelper.Page<BPointDetail> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        LambdaQueryWrapper<BPointDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPointDetail::getType, 0);
        queryWrapper.eq(BPointDetail::getDelFlag, 0);
        queryWrapper.eq(BPointDetail::getMemo, "历史积分");
        bPointDetailMapper.selectList(queryWrapper);
        PageInfo<BPointDetail> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

}

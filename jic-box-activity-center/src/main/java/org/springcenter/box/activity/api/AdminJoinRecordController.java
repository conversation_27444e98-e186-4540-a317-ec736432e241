package org.springcenter.box.activity.api;

import com.alibaba.fastjson.JSON;
import org.springcenter.box.activity.api.dto.JoinRecordInfoResp;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.config.redis.RedissonConfig;
import org.springcenter.box.activity.config.redis.RedissonUtil;
import org.springcenter.box.activity.service.ICActivityService;
import org.springcenter.box.activity.service.IJoinRecordService;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.dto.request.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/admin/c/activity/join/record")
@RestController
@Api(tags = "管理后台-活动明细")
@Slf4j
public class AdminJoinRecordController {
    @Resource
    private IJoinRecordService joinRecordService;
    @Resource
    private ICActivityService activityService;
    @Autowired
    private RedissonUtil redissonUtil;

    @ApiOperation(value = "明细列表", notes = "默认根据创建时间倒序。支持条件：BOX单号，快递单号")
    @PostMapping("/list")
    public ResponseResult<List<JoinRecordInfoResp>> joinRecordList(@Validated @RequestBody CommonRequest<JoinRecordListReq> request) {
        JoinRecordListReq requestData = request.getRequestData();
        requestData.setNeedQueryLogistics(true);
        log.info("明细列表,入参:{}", JSON.toJSONString(requestData));
        List<JoinRecordInfoResp> rspList = joinRecordService.listJoinRecord(requestData, request.getPage());
        log.info("明细列表,总数[{}]条, 回参:{}", request.getPage().getCount(), JSON.toJSONString(rspList));
        return ResponseResult.success(rspList, request.getPage());
    }

    @ApiOperation(value = "人工新增", notes = "成功返回ID,失败返回报错信息")
    @PostMapping("/create")
    public ResponseResult<Boolean> joinRecordCreated(@Validated @RequestBody CommonRequest<JoinRecordAddReq> request) {
        JoinRecordAddReq requestData = request.getRequestData();
        log.info("人工新增,入参:{}", JSON.toJSONString(requestData));
        // 加锁
        String key = RedissonConfig.getLockKeyJoinRecordCreate(requestData.getBoxSn());
        if (!redissonUtil.tryLock(key)) {
            log.info("人工新增 获取锁[{}]失败", key);
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            joinRecordService.joinRecordCreated(requestData);
            log.info("人工新增 完成.操作人:{} 备注内容:{}", requestData.getOptPerson(), requestData.getOptRemark());
        } finally {
            log.info("人工新增 释放锁[{}]", key);
            redissonUtil.unlock(key);
        }
        return ResponseResult.success(true);
    }

    @ApiOperation(value = "修改状态:可发/不可发", notes = "成功返回ID,失败返回报错信息")
    @PostMapping("/changeStatus")
    public ResponseResult<Boolean> joinRecordChangeStatus(@Validated @RequestBody CommonRequest<JoinRecordChangeStatusReq> request) {
        JoinRecordChangeStatusReq requestData = request.getRequestData();
        log.info("修改可发状态,入参:{}", JSON.toJSONString(requestData));
        requestData.check();
        // 加锁
        String key = RedissonConfig.getLockKeyJoinRecordUpdateStatus(requestData.getId());
        if (!redissonUtil.tryLock(key)) {
            log.info("修改可发状态 获取锁[{}]失败", key);
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            joinRecordService.joinRecordChangeStatus(requestData);
            log.info("修改可发状态 完成.操作人:{} 备注内容:{}", requestData.getOptPerson(), requestData.getOptRemark());
        } finally {
            log.info("修改可发状态 释放锁[{}]", key);
            redissonUtil.unlock(key);
        }
        return ResponseResult.success(true);
    }

    @ApiOperation(value = "修改地址", notes = "成功返回ID,失败返回报错信息")
    @PostMapping("/changeAddress")
    public ResponseResult<Boolean> joinRecordChangeAddress(@Validated @RequestBody CommonRequest<JoinRecordChangeAddressReq> request) {
        JoinRecordChangeAddressReq requestData = request.getRequestData();
        // 加锁
        String key = RedissonConfig.getLockKeyJoinRecordChangeAddress(requestData.getId());
        if (!redissonUtil.tryLock(key)) {
            log.info("修改地址 获取锁[{}]失败", key);
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            joinRecordService.joinRecordChangeAddress(requestData);
            log.info("修改地址 完成.操作人:{}", requestData.getOptPerson());
        } finally {
            log.info("修改地址 释放锁[{}]", key);
            redissonUtil.unlock(key);
        }
        return ResponseResult.success(true);
    }

    @ApiOperation(value = "一键发货", notes = "成功返回ID,失败返回报错信息")
    @PostMapping("/delivery")
    public ResponseResult<String> joinRecordDelivery(@Validated @RequestBody CommonRequest<CActivityUpdateReq> request) {
        CActivityUpdateReq requestData = request.getRequestData();
        // 加锁
        String key = RedissonConfig.getLockKeyJoinRecordDelivery(requestData.getActivityId());
        if (!redissonUtil.tryLock(key)) {
            log.info("一键发货 获取锁[{}]失败", key);
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            activityService.deliveryJobSubmit(requestData);
            log.info("一键发货 完成.操作人:{}", requestData.getOptPerson());
        } finally {
            log.info("一键发货 释放锁[{}]", key);
            redissonUtil.unlock(key);
        }
        return ResponseResult.success(requestData.getActivityId());
    }
}

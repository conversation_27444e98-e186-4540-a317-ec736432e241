package org.springcenter.box.activity.event.bus;

import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.box.activity.event.TestEvent;
import org.springcenter.box.activity.event.handler.TestEventHandler;
import org.springcenter.box.activity.service.BaseEventBus;
import org.springcenter.box.activity.service.IBusEventsService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class TestEventBus implements InitializingBean, BaseEventBus<TestEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IBusEventsService iBusEventsService;

    @Autowired
    private TestEventHandler testEventHandler;

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(testEventHandler);
    }

    @Override
    public void post(TestEvent testEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(testEvent);
    }
}

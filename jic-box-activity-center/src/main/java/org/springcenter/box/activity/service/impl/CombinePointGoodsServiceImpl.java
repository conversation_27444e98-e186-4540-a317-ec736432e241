package org.springcenter.box.activity.service.impl;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.box.activity.domain.box.BPointGoodsSku;
import org.springcenter.box.activity.domain.box.BPointGoodsSpu;
import org.springcenter.box.activity.dto.request.AddGoodsReqDto;
import org.springcenter.box.activity.dto.request.SpuGoodsListByUnionIdReqDto;
import org.springcenter.box.activity.dto.request.SpuGoodsListReqDto;
import org.springcenter.box.activity.dto.request.UpdateGoodsReqDto;
import org.springcenter.box.activity.mapper.box.BPointGoodsSkuMapper;
import org.springcenter.box.activity.mapper.box.BPointGoodsSpuMapper;
import org.springcenter.box.activity.mapper.box.PointGoods;
import org.springcenter.box.activity.service.IBPointGoodsSkuService;
import org.springcenter.box.activity.service.IBPointGoodsSpuService;
import org.springcenter.box.activity.service.ICombinePointGoodsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class CombinePointGoodsServiceImpl implements ICombinePointGoodsService {

    @Resource
    private IBPointGoodsSkuService ibPointGoodsSkuService;


    @Resource
    private IBPointGoodsSpuService ibPointGoodsSpuService;

    @Resource
    private BPointGoodsSkuMapper bPointGoodsSkuMapper;


    @Resource
    private BPointGoodsSpuMapper bPointGoodsSpuMapper;


    @Override
    public List<PointGoods> getGoodsList() {
        List<BPointGoodsSpu> bPointGoodsSpuList = ibPointGoodsSpuService.list().stream().filter(e -> Long.valueOf(0).equals(e.getDelFlag())).collect(Collectors.toList());
        List<BPointGoodsSku> bPointGoodsSkuList = ibPointGoodsSkuService.list().stream().filter(e -> Long.valueOf(0).equals(e.getDelFlag())).collect(Collectors.toList());
        Map<String, List<BPointGoodsSku>> pointGoodsSkuMap = bPointGoodsSkuList.stream().collect(Collectors.groupingBy(e -> e.getSpuId().toString()));

        List<PointGoods> pointGoodsList = new ArrayList<>();
        bPointGoodsSpuList.forEach(e -> {
            PointGoods pointGoods = new PointGoods();
            pointGoods.setBPointGoodsSpu(e);
            pointGoods.setBPointGoodsSkuList(pointGoodsSkuMap.getOrDefault(e.getId().toString(), null));
            pointGoodsList.add(pointGoods);
        });
        return pointGoodsList;
    }

    @Override
    public PointGoods goodsInfo(String supId) {
        PointGoods pointGoods = new PointGoods();
        BPointGoodsSpu bPointGoodsSpu = ibPointGoodsSpuService.getById(supId);
        pointGoods.setBPointGoodsSpu(bPointGoodsSpu);
        pointGoods.setBPointGoodsSkuList(bPointGoodsSkuMapper.selectListBySpuId(supId));
        return pointGoods;
    }

    @Override
    public List<BPointGoodsSpu> spuGoodsList(SpuGoodsListReqDto spuGoodsListReqDto) {
        BPointGoodsSpu bPointGoodsSpu = new BPointGoodsSpu();
        bPointGoodsSpu.setHasStock(spuGoodsListReqDto.getHasStock());
        bPointGoodsSpu.setStatus(spuGoodsListReqDto.getStatus());
        return bPointGoodsSpuMapper.getListBySearch(bPointGoodsSpu);
    }


    @Override
    public List<BPointGoodsSpu> spuGoodsListByUnionId(SpuGoodsListByUnionIdReqDto spuGoodsListByUnionIdReqDto) {
        BPointGoodsSpu bPointGoodsSpu = new BPointGoodsSpu();
        bPointGoodsSpu.setHasStock(spuGoodsListByUnionIdReqDto.getHasStock());
        bPointGoodsSpu.setStatus(spuGoodsListByUnionIdReqDto.getStatus());
        bPointGoodsSpu.setPoint(spuGoodsListByUnionIdReqDto.getPoint());
        return bPointGoodsSpuMapper.getMiniListBySearch(bPointGoodsSpu);
    }

    @Override
    public void batchAddList(AddGoodsReqDto addGoodsReqDto) {
        // sku
        List<BPointGoodsSku> bPointGoodsSkus = packPointGoodsList(addGoodsReqDto);
        // spu
        BPointGoodsSpu bPointGoodsSpu = new BPointGoodsSpu();
        bPointGoodsSpu.setId(addGoodsReqDto.getSpuId());
        bPointGoodsSpu.setSpuNo(addGoodsReqDto.getSpuNo());
        bPointGoodsSpu.setGoodsName(addGoodsReqDto.getGoodsName());
        bPointGoodsSpu.setImgUrl(addGoodsReqDto.getImgUrl());
        bPointGoodsSpu.setHasStock(1L);
        bPointGoodsSpu.setCreateBy(addGoodsReqDto.getCreateBy());
        bPointGoodsSpu.setPrice(addGoodsReqDto.getPrice());
        bPointGoodsSpu.setPoint(bPointGoodsSkus.stream().sorted(Comparator.comparing(BPointGoodsSku::getPoint)).findFirst().get().getPoint());
        bPointGoodsSpu.setBrandId(addGoodsReqDto.getBrandId());
        bPointGoodsSpu.setBrandName(addGoodsReqDto.getBrandName());
        // 默认上架
        bPointGoodsSpu.setStatus(addGoodsReqDto.getStatus());
        bPointGoodsSpu.setCreateTime(new Date());
        bPointGoodsSpu.setUpdateTime(new Date());

        ibPointGoodsSpuService.save(bPointGoodsSpu);
        ibPointGoodsSkuService.saveBatch(bPointGoodsSkus);
        handleSpuStock(Lists.newArrayList(addGoodsReqDto.getSpuId()));
    }


    List<BPointGoodsSku> packPointGoodsList(AddGoodsReqDto addGoodsReqDto) {
        List<BPointGoodsSku> bPointGoodsSkus = new ArrayList<>();

        addGoodsReqDto.getSkuList().forEach(e -> {
            BPointGoodsSku bPointGoodsSku = new BPointGoodsSku();
            bPointGoodsSku.setId(e.getSkuId());
            bPointGoodsSku.setSkuNo(e.getSkuNo());
            bPointGoodsSku.setImgUrl(e.getImgUrl());
            bPointGoodsSku.setStock(e.getStock());
            bPointGoodsSku.setPoint(e.getPoint());
            bPointGoodsSku.setPrice(e.getPrice());

            bPointGoodsSku.setGoodsName(addGoodsReqDto.getGoodsName());
            bPointGoodsSku.setSpuId(addGoodsReqDto.getSpuId());
            bPointGoodsSku.setSpuNo(addGoodsReqDto.getSpuNo());
            bPointGoodsSku.setGoodsName(addGoodsReqDto.getGoodsName());

            bPointGoodsSku.setColorName(e.getColorName());
            bPointGoodsSku.setColorNo(e.getColorNo());
            bPointGoodsSku.setSizeName(e.getSizeName());
            bPointGoodsSku.setSizeNo(e.getSizeNo());
            bPointGoodsSku.setCreateBy(addGoodsReqDto.getCreateBy());
            bPointGoodsSku.setCreateTime(new Date());
            bPointGoodsSku.setUpdateTime(new Date());
            bPointGoodsSkus.add(bPointGoodsSku);
        });
        return bPointGoodsSkus;
    }


    @Override
    public void batchUpdateList(UpdateGoodsReqDto updateGoodsReqDto) {
        String spuId = updateGoodsReqDto.getSpuId();
        String spuImgUrl = updateGoodsReqDto.getSpuImgUrl();
        String updateBy = updateGoodsReqDto.getUpdateBy();
        Long status = updateGoodsReqDto.getStatus();

        List<UpdateGoodsReqDto.UpdateSku> skuList = updateGoodsReqDto.getSkuList();
        if (CollectionUtils.isEmpty(skuList) && ObjectUtils.isEmpty(spuImgUrl) && ObjectUtils.isEmpty(status)) {
            throw new RuntimeException("入参不能同时为空");
        }

        if (CollectionUtils.isNotEmpty(skuList)) {
            List<BPointGoodsSku> bPointGoodsSkus = new ArrayList<>();
            skuList.forEach(e -> {
                Preconditions.checkNotNull(e.getSkuId(), "skuId不能为空");
                BPointGoodsSku bPointGoodsSku = new BPointGoodsSku();
                bPointGoodsSku.setId(e.getSkuId());

                if (ObjectUtils.isNotEmpty(e.getImgUrl())) {
                    bPointGoodsSku.setImgUrl(e.getImgUrl());
                }
                if (ObjectUtils.isNotEmpty(e.getStock())) {
                    bPointGoodsSku.setStock(e.getStock());
                }

                if (ObjectUtils.isNotEmpty(e.getPoint())) {
                    bPointGoodsSku.setPoint(e.getPoint());
                }
                bPointGoodsSku.setUpdateTime(new Date());
                bPointGoodsSku.setUpdateBy(updateBy);
                bPointGoodsSkus.add(bPointGoodsSku);
            });
            ibPointGoodsSkuService.updateBatchById(bPointGoodsSkus);
        }


        if (StringUtils.isNotEmpty(spuImgUrl) ||  ObjectUtils.isNotEmpty(status)) {
            BPointGoodsSpu bPointGoodsSpu = new BPointGoodsSpu();
            bPointGoodsSpu.setId(spuId);
            bPointGoodsSpu.setUpdateBy(updateBy);
            bPointGoodsSpu.setUpdateTime(new Date());
            if (StringUtils.isNotEmpty(spuImgUrl)) {
                bPointGoodsSpu.setImgUrl(spuImgUrl);
            }
            if (ObjectUtils.isNotEmpty(status)) {
                bPointGoodsSpu.setStatus(status);
            }
            ibPointGoodsSpuService.updateById(bPointGoodsSpu);
        }
        handleSpuStock(Lists.newArrayList(spuId));

        // 再次查询 最低的点
        List<BPointGoodsSku> bPointGoodsSkuList = bPointGoodsSkuMapper.selectListBySpuId(spuId).stream().filter(e -> Long.valueOf(0).equals(e.getDelFlag())).collect(Collectors.toList());
        BPointGoodsSpu updatePointGoodsSpu = new BPointGoodsSpu();
        updatePointGoodsSpu.setId(spuId);
        updatePointGoodsSpu.setPoint(bPointGoodsSkuList.stream().sorted(Comparator.comparing(BPointGoodsSku::getPoint)).findFirst().get().getPoint());
        updatePointGoodsSpu.setUpdateTime(new Date());
        ibPointGoodsSpuService.updateById(updatePointGoodsSpu);
    }

    @Override
    public void handleSpuStock(List<String> spuIdList) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(spuIdList),"入参不能为空");
        List<String> resultIds = bPointGoodsSkuMapper.getSpuIdListHasStock(spuIdList);
        Set<String> resultSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(resultIds)) {
            resultSet.addAll(resultIds);
        }

        List<BPointGoodsSpu> bPointGoodsSpuList = new ArrayList<>();
        spuIdList.forEach(e -> {
            BPointGoodsSpu bPointGoodsSpuTemp = new BPointGoodsSpu();
            bPointGoodsSpuTemp.setId(e);
            bPointGoodsSpuTemp.setHasStock(resultSet.contains(e) ? 1L : 0L);
            bPointGoodsSpuTemp.setUpdateTime(new Date());
            bPointGoodsSpuList.add(bPointGoodsSpuTemp);
        });

        ibPointGoodsSpuService.updateBatchById(bPointGoodsSpuList);

    }
}

package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel("明细列表入参")
@Data
public class JoinRecordListReq {
    @ApiModelProperty(value = "活动ID", required = true)
    private String activityId;
    @ApiModelProperty(value = "boxSn。无需传null")
    private String boxSn;
    @ApiModelProperty(value = "boxId。无需传null")
    private String boxId;
    @ApiModelProperty(value = "物流单号:精确匹配。无需传null")
    private String logisticsNo;
    @ApiModelProperty(value = "达成状态：0=不可发，1=可发。全部传null")
    private Integer accomplishStatus;
    @ApiModelProperty(value = "搭配师ID。无需传null")
    private String fashionerId;
    @ApiModelProperty(value = "达成状态。0=未发放，1=等待结果，2=发放成功，3=发放失败。无需传null")
    private Integer giftSendStatus;
    @ApiModelProperty(value = "boxSn数组。无需传null")
    private List<String> boxSnList;
    @ApiModelProperty(value = "需要回查地址。默认不查，查询会影响性能")
    private Boolean needQueryLogistics = false;

    @ApiModelProperty(value = "消费者ID", required = true)
    private String unionId;
    // TODO 下个版本后移除boxSnList
    @ApiModelProperty(value = "boxId数组。无需传null")
    private List<String> boxIdList;
    @ApiModelProperty(value = "隔离时间，小程序的数据需要根据上线后的时间来查询")
    private Date isolationTime;
}

package org.springcenter.box.activity.job;

import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.config.trace.XxlJobTaskLog;
import org.springcenter.box.activity.service.ICActivityService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 一键发货JOB
 * 1分钟调度一次，分片调度，忙碌丢弃
 * 乐观锁更新，每个机器只执行一个活动。
 */
@Component
@Slf4j
public class DeliveryJob extends IJobHandler {

    @Resource
    private ICActivityService activityService;

    //    @Scheduled(cron = "0 * * * * ?")
    @XxlJob("DeliveryJob")
    @Override
    public void execute() {
        XxlJobTaskLog.traceLog("一键发货JOB,本次调度开始");
        activityService.deliveryJobExecute();
        log.info("一键发货JOB,本次调度结束");
    }
}


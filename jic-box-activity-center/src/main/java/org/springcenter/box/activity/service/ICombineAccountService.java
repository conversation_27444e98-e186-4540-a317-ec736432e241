package org.springcenter.box.activity.service;

import org.springcenter.box.activity.domain.box.BUserPointAccount;

import java.util.List;

public interface ICombineAccountService {
    BUserPointAccount getAccountByCustomerId(String customerId);


    List<BUserPointAccount> getAccountByCustomerIdList(List<String> customerIdList);


    List<BUserPointAccount> getAccountByUnionIds(List<String> unionIds);


    BUserPointAccount getAccountByUnionId(String unionId);
}

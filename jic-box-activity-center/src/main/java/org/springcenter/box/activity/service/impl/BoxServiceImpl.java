package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcenter.box.activity.domain.box.Box;
import org.springcenter.box.activity.enums.UltimaBoxStatusEnum;
import org.springcenter.box.activity.mapper.box.BoxMapper;
import org.springcenter.box.activity.service.IBoxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BoxServiceImpl extends ServiceImpl<BoxMapper, Box> implements IBoxService {


    @Override
    public Map<String, String> getBoxSn2BoxIdMap(List<String> boxSnList) {
        log.info("根据boxSn查询BoxId,入参:{}", JSON.toJSONString(boxSnList));
        Map<String, String> map = Optional.ofNullable(
                this.list(new LambdaQueryWrapper<Box>()
                                .select(Box::getBoxSn, Box::getId)
                        .in(Box::getBoxSn, boxSnList))
                )
                .orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(Box::getBoxSn, Box::getId, (a, b) -> a));
        log.info("根据boxSn查询BoxId,回参:{}", JSON.toJSONString(map));
        return map;
    }

    @Override
    public List<Box> getUnFinishBoxList(String unionId) {
        LambdaQueryWrapper<Box> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Box::getUnionid, unionId);
        queryWrapper.in(Box::getStatus, Arrays.asList(UltimaBoxStatusEnum.WAIT_SUBMIT.getCode().longValue(),
                UltimaBoxStatusEnum.WAIT_SEND.getCode().longValue(),
                UltimaBoxStatusEnum.SENDING.getCode().longValue(),
                UltimaBoxStatusEnum.SIGNED.getCode().longValue(),
                UltimaBoxStatusEnum.WAIT_RETURN.getCode().longValue(),
                UltimaBoxStatusEnum.RETURNING.getCode().longValue()
                ));
        return this.list(queryWrapper);
    }
}

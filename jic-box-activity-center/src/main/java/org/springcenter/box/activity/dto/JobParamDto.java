package org.springcenter.box.activity.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class JobParamDto {
    /**
     * 时间-起
     */
    private Date fromDate;
    /**
     * 时间-止
     */
    private Date toDate;

    public static JobParamDto build(String jobParam) {
        if (StringUtils.isBlank(jobParam)) {
            new JobParamDto();
        }
        return JSON.parseObject(jobParam, JobParamDto.class);
    }
}

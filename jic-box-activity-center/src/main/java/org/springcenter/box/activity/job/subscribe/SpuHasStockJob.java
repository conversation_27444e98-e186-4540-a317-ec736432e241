package org.springcenter.box.activity.job.subscribe;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.box.activity.config.trace.XxlJobTaskLog;
import org.springcenter.box.activity.domain.box.BPointDetail;
import org.springcenter.box.activity.domain.box.BPointGoodsSpu;
import org.springcenter.box.activity.mapper.box.BPointGoodsSkuMapper;
import org.springcenter.box.activity.mapper.box.BPointGoodsSpuMapper;
import org.springcenter.box.activity.service.IBPointGoodsSpuService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SpuHasStockJob extends IJobHandler {

    @Resource
    private BPointGoodsSpuMapper bPointGoodsSpuMapper;

    @Resource
    private BPointGoodsSkuMapper bPointGoodsSkuMapper;

    @Resource
    private IBPointGoodsSpuService ibPointGoodsSpuService;

    /**
     * 处理spu 是否有库存标识
     * @throws Exception
     */
    @Override
    @XxlJob("SpuHasStockJob")
    public void execute() throws Exception {
        XxlJobTaskLog.traceLog("处理spu是否有库存开始");
        handleDate();
        XxlJobTaskLog.traceLog("处理spu是否有库存结束");

    }

    void handleDate(){
        int totalPage = 1;
        Page page = new Page(1, 500);
        Set<String> stockSubIdsSet = new HashSet<>();
        List<BPointGoodsSpu> result = new ArrayList<>();
        while (page.getPageNo() <= totalPage) {
            List<BPointGoodsSpu> bPointGoodsSpuList = getNeedBPointGoodsSpuList(page);
            if (CollectionUtils.isEmpty(bPointGoodsSpuList)) {
                break;
            } else {
                result.addAll(bPointGoodsSpuList);
                List<String> stockSubIds = bPointGoodsSkuMapper.getSpuIdListHasStock(bPointGoodsSpuList.stream().map(BPointGoodsSpu::getId).collect(Collectors.toList()));
                if(CollectionUtils.isNotEmpty(stockSubIds)){
                    stockSubIdsSet.addAll(stockSubIds);
                }
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
        }

        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        List<BPointGoodsSpu> needUpdateList = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            // 查询到有库存， 并且现在数据就是有库存不更新
            if(Long.valueOf(1).equals( result.get(i).getHasStock()) && stockSubIdsSet.contains(result.get(i).getId()) ){
                continue;
            }
            // 查询到无库存， 并且现在数据就是无 库存不更新
            if(Long.valueOf(0).equals( result.get(i).getHasStock()) && !stockSubIdsSet.contains(result.get(i).getId()) ){
                continue;
            }
            BPointGoodsSpu updateSpu = new BPointGoodsSpu();
            updateSpu.setId(result.get(i).getId());
            updateSpu.setUpdateTime(new Date());
            updateSpu.setHasStock(stockSubIdsSet.contains(result.get(i).getId())?1L:0L);
            needUpdateList.add(updateSpu);
        }

        if(CollectionUtils.isEmpty(needUpdateList)){
            return;
        }

        List<List<BPointGoodsSpu>> parts = Lists.partition(needUpdateList, 800);
        parts.forEach(x->{
            ibPointGoodsSpuService.updateBatchById(x);
        });



    }



    public List<BPointGoodsSpu> getNeedBPointGoodsSpuList(Page page) {
        com.github.pagehelper.Page<BPointGoodsSpu> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bPointGoodsSpuMapper.getAllList();
        PageInfo<BPointGoodsSpu> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }
}

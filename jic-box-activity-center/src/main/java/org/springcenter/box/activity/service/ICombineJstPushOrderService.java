package org.springcenter.box.activity.service;

import org.springcenter.box.activity.domain.box.BJstPushOrder;

import java.util.List;

public interface ICombineJstPushOrderService {

    /**
     * 创建聚水潭 推送单据
     * @param orderId
     */
    void  createJstPushOrder(String orderId);


    /**
     * 推送聚水潭
     * @param id
     */
    void pushJstOrder(String id);


    /**
     * 获取物流信息
     * @param orderDetailId
     * @return
     */
    BJstPushOrder getLogistics(String orderDetailId);


    /**
     * 填充物流信息
     * @param bJstPushOrderList
     */
    void fromJstBlankLogistics(List<BJstPushOrder> bJstPushOrderList);

    /**
     * 填充聚水潭 物流
     */
    void fromJstBlankLogisticsList();

    /**
     * 推送聚水潭
     */
    void pushJstOrders();
}

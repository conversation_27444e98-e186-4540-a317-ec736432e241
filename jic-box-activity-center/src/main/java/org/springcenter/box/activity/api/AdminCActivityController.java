package org.springcenter.box.activity.api;

import com.alibaba.fastjson.JSON;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.dto.request.CActivityCreateReq;
import org.springcenter.box.activity.dto.request.CActivityIdReq;
import org.springcenter.box.activity.dto.request.CActivityListReq;
import org.springcenter.box.activity.dto.request.CActivityUpdateReq;
import org.springcenter.box.activity.dto.request.CActivityGiftStockReq;
import org.springcenter.box.activity.service.ICActivityService;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/admin/c/activity")
@RestController
@Api(tags = "管理后台-满赠活动")
@Slf4j
public class AdminCActivityController {

    @Resource
    private ICActivityService icActivityService;

    @ApiOperation(value = "新增", notes = "成功返回活动ID,失败返回报错信息")
    @PostMapping("/create")
    public ResponseResult<String> activityCreated(@Validated @RequestBody CommonRequest<CActivityCreateReq> request) {
        CActivityCreateReq requestData = request.getRequestData();
        requestData.check();
        log.info("创建活动,入参:{}", JSON.toJSONString(requestData));
        String id = icActivityService.activityCreated(request.getRequestData());
        log.info("创建活动,回参:{}", id);
        return ResponseResult.success(id);
    }

    @ApiOperation(value = "活动列表", notes = "默认根据创建时间倒序，支持条件：活动日期范围，活动状态，活动名称模糊")
    @PostMapping("/list")
    public ResponseResult<List<CActivityInfoResp>> activityList(@RequestBody CommonRequest<CActivityListReq> request) {
        CActivityListReq requestData = request.getRequestData();
        log.info("活动列表,入参:{}", JSON.toJSONString(requestData));
        requestData.setAdminRequest(true);
        List<CActivityInfoResp> rspList = icActivityService.activityList(requestData, request.getPage());
        log.info("活动列表,总数[{}]条, 回参:{}", request.getPage().getCount(), JSON.toJSONString(rspList));
        return ResponseResult.success(rspList, request.getPage());
    }

    @ApiOperation(value = "活动详情", notes = "返回活动详情")
    @PostMapping("/info")
    public ResponseResult<CActivityInfoResp> activityInfo(@RequestBody CommonRequest<CActivityIdReq> request) {
        CActivityIdReq requestData = request.getRequestData();
        log.info("活动详情,入参:{}", JSON.toJSONString(requestData));
        CActivityInfoResp rsp = icActivityService.activityInfo(requestData.getActivityId(), true);
        log.info("活动详情,回参:{}", JSON.toJSONString(rsp));
        return ResponseResult.success(rsp);
    }

    @ApiOperation(value = "作废", notes = "手动终止活动,成功返回ID,失败返回报错信息")
    @PostMapping("/cancel")
    public ResponseResult<String> activityCancel(@Validated @RequestBody CommonRequest<CActivityUpdateReq> request) {
        CActivityUpdateReq requestData = request.getRequestData();
        log.info("作废活动,入参:{}", JSON.toJSONString(requestData));
        icActivityService.activityCancel(requestData.getActivityId(), requestData.getOptPerson());
        log.info("作废活动,成功.操作人:{}", requestData.getOptPerson());
        return ResponseResult.success(requestData.getActivityId());
    }

    @ApiOperation(value = "新增赠品库存", notes = "新增赠品的库存数量")
    @PostMapping("/addGiftStock")
    public ResponseResult<Boolean> addGiftStock(@Validated @RequestBody CommonRequest<CActivityGiftStockReq> request) {
        CActivityGiftStockReq requestData = request.getRequestData();
        log.info("更新赠品库存,入参:{}", JSON.toJSONString(requestData));
        boolean result = icActivityService.addGiftStock(requestData.getGiftId(), requestData.getAddNum(), requestData.getOptPerson());
        log.info("更新赠品库存,结果:{}", result);
        return ResponseResult.success(result);
    }


    @ApiOperation(value = "下架", notes = "手动下架活动,成功返回ID,失败返回报错信息")
    @PostMapping("/down")
    public ResponseResult<String> activityDown(@Validated @RequestBody CommonRequest<CActivityUpdateReq> request) {
        CActivityUpdateReq requestData = request.getRequestData();
        log.info("下架活动,入参:{}", JSON.toJSONString(requestData));
        icActivityService.activityDown(requestData.getActivityId(), requestData.getOptPerson());
        log.info("下架活动,成功.操作人:{}", requestData.getOptPerson());
        return ResponseResult.success(requestData.getActivityId());
    }
}

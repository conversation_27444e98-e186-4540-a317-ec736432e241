package org.springcenter.box.activity.config.exception;

public class BoxActivityException extends RuntimeException {

    private int code;

    public int getCode() {
        return code;
    }

    public BoxActivityException(SystemErrorEnum errorEnum) {
        super(errorEnum.getErrorMsg());
        this.code = errorEnum.getErrorCode();
    }

    public BoxActivityException(String errorMsg) {
        super(errorMsg);
        this.code = SystemErrorEnum.UNIFIED_CODE_ERROR.getErrorCode();
    }
}

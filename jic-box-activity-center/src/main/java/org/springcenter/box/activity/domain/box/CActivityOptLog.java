package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-10-16 16:50:26
 * @Description: 参与记录日志
 */
@Data
@TableName("C_ACTIVITY_OPT_LOG")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value = "CActivityOptLog对象", description = "参与记录日志")
public class CActivityOptLog implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "活动ID")
    @TableField("ACTIVITY_ID")
    private String activityId;
    @ApiModelProperty(value = "参与记录ID")
    @TableField("JOIN_RECORD_ID")
    private String joinRecordId;
    @ApiModelProperty(value = "操作类型：1=创建活动、2=作废活动、10=付款/退款计算失败、11=任务达成、12=任务退回、20=参与记录-人工新增、11=参与记录-可发、12=参与记录-不可发、13=参与记录-修改地址、14=参与记录-一键发货")
    @TableField("OPT_TYPE")
    private Integer optType;
    @ApiModelProperty(value = "操作人：system=系统, xxx=人工")
    @TableField("OPT_PERSON")
    private String optPerson;
    @ApiModelProperty(value = "修改内容")
    @TableField("OPT_CONTENT")
    private String optContent;
    @ApiModelProperty(value = "原内容")
    @TableField("ORIGIN_CONTENT")
    private String originContent;

}

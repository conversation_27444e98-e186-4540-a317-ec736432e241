package org.springcenter.box.activity.enums;

import org.assertj.core.util.Lists;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单状态枚举
 * <AUTHOR>
 * @date 2018/3/14
 */
public enum OrderStatusEnum {
    unpay(0, "未支付"),
    paid(1, "已支付"),
    shiped(2, "已发货"),
    done(3, "已完成"),
    cancel(4, "已取消"),
    returnAmount(5, "已退款"),
    receive(6, "已签收");


    private static final Map<Integer, OrderStatusEnum> LOOKUP = new HashMap<>();

    static {
        for (OrderStatusEnum s : EnumSet.allOf(OrderStatusEnum.class)) {
            LOOKUP.put(s.getCode(), s);
        }
    }

    private Integer code;
    private String name;

    OrderStatusEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }

    public static String get(Integer code) {
        OrderStatusEnum orderStatusEnum = LOOKUP.get(code);
        return orderStatusEnum.getName();
    }

    /**
     * 实付状态
     */
    public static List<Integer> paidCodeList() {
        return Lists.newArrayList(
                paid.getCode(),
                shiped.getCode(),
                done.getCode(),
                receive.getCode()
                );
    }
}
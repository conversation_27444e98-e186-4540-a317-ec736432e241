package org.springcenter.box.activity.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("活动列表查询回参")
@Data
public class CActivityListResp {
    @ApiModelProperty(value = "活动ID")
    private String id;
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    @ApiModelProperty(value = "活动开始时间")
    private String startTime;
    @ApiModelProperty(value = "活动结束时间")
    private String endTime;
    @ApiModelProperty(value = "创建人")
    private String createPerson;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    @ApiModelProperty(value = "活动状态：1=未开始,2=进行中,3=核算中,4=待发放,5=已结束")
    private Integer status;
    @ApiModelProperty(value = "达成门槛：元")
    private BigDecimal threshold;
}

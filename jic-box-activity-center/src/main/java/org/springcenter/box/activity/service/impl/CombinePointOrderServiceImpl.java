package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.assertj.core.util.Lists;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.box.activity.config.IdConfig;
import org.springcenter.box.activity.domain.box.*;
import org.springcenter.box.activity.dto.request.AllUserOrderDetailListReqDto;
import org.springcenter.box.activity.dto.request.CreatePointOrderReqDto;
import org.springcenter.box.activity.dto.request.GetCustomerLogisticReqDto;
import org.springcenter.box.activity.dto.request.UserOrderListReqDto;
import org.springcenter.box.activity.event.CreateJstPushOrderEvent;
import org.springcenter.box.activity.event.ModifySpuStockEvent;
import org.springcenter.box.activity.event.RulesUserPointEvent;
import org.springcenter.box.activity.event.bus.CreateJstPushOrderEventBus;
import org.springcenter.box.activity.event.bus.ModifySpuStockEventBus;
import org.springcenter.box.activity.event.bus.RulesUserPointEventBus;
import org.springcenter.box.activity.mapper.box.*;
import org.springcenter.box.activity.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CombinePointOrderServiceImpl implements ICombinePointOrderService {

    @Resource
    private IBUserPointOrderService ibUserPointOrderService;

    @Resource
    private BUserPointOrderMapper bUserPointOrderMapper;

    @Resource
    private ICombineAccountService iCombineAccountService;

    @Resource
    private IBPointGoodsSkuService ibPointGoodsSkuService;

    @Resource
    private IBPointGoodsSpuService ibPointGoodsSpuService;

    @Resource
    private IBUserPointOrderDetailService ibUserPointOrderDetailService;


    @Resource
    private IdConfig idConfig;

    @Resource
    private IBPointDetailService ibPointDetailService;


    @Resource
    private ICombinePointDetailService iCombinePointDetailService;

    @Resource
    private BUserPointOrderDetailMapper bUserPointOrderDetailMapper;

    @Resource
    private BPointGoodsSkuMapper bPointGoodsSkuMapper;

    @Resource(name = "boxTransactionTemplate")
    private TransactionTemplate template;


    @Resource
    private CustomerLogisticsMapper customerLogisticsMapper;


    @Resource
    private IBLogisticsSnapshotService ibLogisticsSnapshotService;


    @Resource
    private ModifySpuStockEventBus modifySpuStockEventBus;


    @Resource
    private CreateJstPushOrderEventBus createJstPushOrderEventBus;


    @Resource
    private RulesUserPointEventBus rulesUserPointEventBus;

    @Override
    public List<BUserPointOrderDetail> allUserOrderDetailList(AllUserOrderDetailListReqDto allUserOrderDetailListReqDto, Page page) {
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bUserPointOrderDetailMapper.getBySpuIdOrOrderSn(allUserOrderDetailListReqDto.getSpuId(), allUserOrderDetailListReqDto.getOrderSn());
        PageInfo<BUserPointOrderDetail> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<BUserPointOrderDetail> bUserPointOrders = pageInfo.getList();
        return bUserPointOrders;
    }

    @Override
    public List<UserPointOrderWithDetail> userOrderList(String unionId, Page page) {
        List<UserPointOrderWithDetail> userPointOrderWithDetails = new ArrayList<>();

        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bUserPointOrderMapper.userOrderList(unionId);
        PageInfo<BUserPointOrder> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<BUserPointOrder> bUserPointOrders = pageInfo.getList();
        if (CollectionUtils.isEmpty(bUserPointOrders)) {
            return userPointOrderWithDetails;
        }
        List<BUserPointOrderDetail> bUserPointOrderDetailList = bUserPointOrderDetailMapper.getByOrderIds(bUserPointOrders.stream().map(BUserPointOrder::getId).collect(Collectors.toList()));

        bUserPointOrders.forEach(e -> {
            UserPointOrderWithDetail userPointOrderWithDetailTemp = new UserPointOrderWithDetail();
            userPointOrderWithDetailTemp.setBUserPointOrder(e);
            userPointOrderWithDetailTemp.setBUserPointOrderDetailList(bUserPointOrderDetailList.stream().filter(x -> x.getOrderId().equals(e.getId())).collect(Collectors.toList()));
            userPointOrderWithDetails.add(userPointOrderWithDetailTemp);
        });
        return userPointOrderWithDetails;
    }

    @Override
    public UserPointOrderWithDetail userOrderInfo(String orderId) {
        BUserPointOrder bUserPointOrder = bUserPointOrderMapper.selectById(orderId);
        List<BUserPointOrderDetail> bUserPointOrderDetailList = bUserPointOrderDetailMapper.getByOrderIds(Lists.newArrayList(bUserPointOrder.getId()));

        UserPointOrderWithDetail userPointOrderWithDetail = new UserPointOrderWithDetail();
        userPointOrderWithDetail.setBUserPointOrder(bUserPointOrder);
        userPointOrderWithDetail.setBUserPointOrderDetailList(bUserPointOrderDetailList);
        return userPointOrderWithDetail;
    }

    @Override
    public void createPointOrder(CreatePointOrderReqDto createPointOrderReqDto) {
        String customerId = createPointOrderReqDto.getCustomerId();
        String unionId = createPointOrderReqDto.getUnionId();
        BigDecimal usePoint = createPointOrderReqDto.getUsePoint();
        String customerLogisticsId = createPointOrderReqDto.getCustomerLogisticsId();

        BUserPointAccount bUserPointAccount = iCombineAccountService.getAccountByCustomerId(customerId);
        if (bUserPointAccount == null) {
            throw new RuntimeException("兑换值不足");
        }
        if (bUserPointAccount.getCanUsePoint().compareTo(usePoint) < 0) {
            throw new RuntimeException("兑换值不足");
        }
        BigDecimal realTimePoint = iCombinePointDetailService.realTimePointByCustomerId(customerId);
        log.info("实时查询点数unionId:{} realTimePoint:{}", unionId, realTimePoint);
        if (realTimePoint.compareTo(usePoint) < 0) {
            throw new RuntimeException("兑换值不足");
        }
        CustomerLogistics customerLogistics = Preconditions.checkNotNull(customerLogisticsMapper.selectById(customerLogisticsId),"客户地址不存在");
        log.info("客户下单地址unionId:{} customerLogistics:{}",unionId, JSONObject.toJSONString(customerLogistics));
        List<String> skuIds = createPointOrderReqDto.getSkuList().stream().map(CreatePointOrderReqDto.Sku::getSkuId).collect(Collectors.toList());
        List<String> spuIds = createPointOrderReqDto.getSkuList().stream().map(CreatePointOrderReqDto.Sku::getSpuId).collect(Collectors.toList());
        List<BPointGoodsSpu> bPointGoodsSpuList = ibPointGoodsSpuService.listByIds(spuIds).stream().filter(e -> Long.valueOf(0).equals(e.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bPointGoodsSpuList)) {
            throw new RuntimeException("商品被下架" + bPointGoodsSpuList.get(0).getGoodsName());
        }
        List<BPointGoodsSku> bPointGoodsSkuList = ibPointGoodsSkuService.listByIds(skuIds);
        if (CollectionUtils.isEmpty(bPointGoodsSkuList)) {
            throw new RuntimeException("商品信息不存在");
        }

        // 进行兑换
        BLogisticsSnapshot bLogisticsSnapshot = packageLogisticsSnapshot(customerLogistics);
        BUserPointOrder bUserPointOrder = packUserPointOrder(bLogisticsSnapshot.getId(),customerLogisticsId, customerId, unionId, usePoint);
        List<BUserPointOrderDetail> bUserPointOrderDetailList = packageUserPointOrderDetailList(bUserPointOrder, bPointGoodsSkuList);
        List<PointDetailWithSub> pointDetailWithSubList = iCombinePointDetailService.getPointDetailWithSub(customerId);
        List<BPointDetail> bPointDetailList = packagePointDetailList(bUserPointOrder, pointDetailWithSubList);

        template.execute(action -> {
            // 扣除库存sku
            skuIds.stream().forEach(e -> {
                int result = bPointGoodsSkuMapper.subtractStockBySkuId(e);
                if (result != 1) {
                    throw new RuntimeException("扣除库存失败" + "skuId:" + e);
                }
            });
            ibLogisticsSnapshotService.save(bLogisticsSnapshot);
            ibUserPointOrderService.save(bUserPointOrder);
            ibUserPointOrderDetailService.saveBatch(bUserPointOrderDetailList);
            ibPointDetailService.saveBatch(bPointDetailList);
            handleSpuStock(spuIds);
            handleCreateJstPushOrder(bUserPointOrder.getId());
            rulesUserPoint(customerId);
            return true;
        });
    }

    BLogisticsSnapshot packageLogisticsSnapshot(CustomerLogistics customerLogistics){
        BLogisticsSnapshot bLogisticsSnapshot = new BLogisticsSnapshot();
        bLogisticsSnapshot.setId(idConfig.getLogisticsSnapshotId());
        bLogisticsSnapshot.setReceiveContact(customerLogistics.getContactName());
        bLogisticsSnapshot.setReceivePhone(customerLogistics.getContactPhone());
        bLogisticsSnapshot.setReceiveProvince(customerLogistics.getProvince());
        bLogisticsSnapshot.setReceiveCity(customerLogistics.getCity());
        bLogisticsSnapshot.setReceiveDistrict(customerLogistics.getDistrict());
        bLogisticsSnapshot.setReceiveAddress(customerLogistics.getAddress());
        bLogisticsSnapshot.setCreateTime(new Date());
        return bLogisticsSnapshot;
    }

    BUserPointOrder packUserPointOrder(String logisticsSnapshotId,String customerLogisticsId, String customerId,String unionId, BigDecimal usePoint) {
        BUserPointOrder bUserPointOrder = new BUserPointOrder();
        bUserPointOrder.setId(idConfig.getUserPointOrderId());
        bUserPointOrder.setUnionId(unionId);
        bUserPointOrder.setCustomerId(customerId);
        bUserPointOrder.setOrderSn("OP" + idConfig.getUserPointOrderId());
        bUserPointOrder.setUsedPoint(usePoint);
        bUserPointOrder.setCreateTime(new Date());
        bUserPointOrder.setUpdateTime(new Date());
        bUserPointOrder.setLogisticsSnapshotId(logisticsSnapshotId);
        bUserPointOrder.setCustomerLogisticsId(customerLogisticsId);
        log.info("封装订单unionId:{} orderSn:{}", unionId, bUserPointOrder.getOrderSn());
        return bUserPointOrder;
    }

    List<BUserPointOrderDetail> packageUserPointOrderDetailList(BUserPointOrder bUserPointOrder, List<BPointGoodsSku> bPointGoodsSkuList) {
        List<BUserPointOrderDetail> bUserPointOrderDetailList = new ArrayList<>();
        BigDecimal tempPoint = BigDecimal.ZERO;
        for (int i = 0; i < bPointGoodsSkuList.size(); i++) {
            tempPoint = tempPoint.add(bPointGoodsSkuList.get(i).getPoint());
        }
        if (tempPoint.compareTo(bUserPointOrder.getUsedPoint()) != 0) {
            log.info("非法支付点数");
            throw new RuntimeException("非法支付点数");
        }
        bPointGoodsSkuList.stream().forEach(e -> {
            if (e.getStock() <= 0) {
                throw new RuntimeException(e.getGoodsName() + "-库存不足" + "skuId:" + e.getId());
            }
            BUserPointOrderDetail bUserPointOrderDetail = new BUserPointOrderDetail();
            bUserPointOrderDetail.setId(idConfig.getUserPointOrderDetailId());
            bUserPointOrderDetail.setUnionId(bUserPointOrder.getUnionId());
            bUserPointOrderDetail.setCustomerId(bUserPointOrder.getCustomerId());
            bUserPointOrderDetail.setOrderId(bUserPointOrder.getId());
            bUserPointOrderDetail.setOrderSn(bUserPointOrder.getOrderSn());
            bUserPointOrderDetail.setUsedPoint(e.getPoint());
            bUserPointOrderDetail.setSkuId(e.getId());
            bUserPointOrderDetail.setSkuNo(e.getSkuNo());
            bUserPointOrderDetail.setSpuId(e.getSpuId());
            bUserPointOrderDetail.setSpuNo(e.getSpuNo());
            bUserPointOrderDetail.setGoodsName(e.getGoodsName());
            bUserPointOrderDetail.setImgUrl(e.getImgUrl());

            bUserPointOrderDetail.setColorName(e.getColorName());
            bUserPointOrderDetail.setColorNo(e.getColorNo());
            bUserPointOrderDetail.setSizeName(e.getSizeName());
            bUserPointOrderDetail.setSizeNo(e.getSizeNo());
            bUserPointOrderDetail.setLogisticsSnapshotId(bUserPointOrder.getLogisticsSnapshotId());

            bUserPointOrderDetail.setCreateTime(new Date());
            bUserPointOrderDetail.setUpdateTime(new Date());
            bUserPointOrderDetailList.add(bUserPointOrderDetail);
        });
        return bUserPointOrderDetailList;
    }

    List<BPointDetail> packagePointDetailList(BUserPointOrder bUserPointOrder, List<PointDetailWithSub> pointDetailWithSubList) {
        String unionId = bUserPointOrder.getUnionId();
        BigDecimal usePoint = bUserPointOrder.getUsedPoint();
        log.info("封装点数信息unionId:{} usePoint:{}", unionId, usePoint);
        List<BPointDetail> bPointDetailList = new ArrayList<>();
        BigDecimal usePointTemp = new BigDecimal(usePoint.toString());
        for (int i = 0; i < pointDetailWithSubList.size(); i++) {
            PointDetailWithSub pointDetailWithSubTemp = pointDetailWithSubList.get(i);
            log.info("订阅周期unionId:{} subId:{} point:{}", unionId, pointDetailWithSubTemp.getSubInfo().getId(), pointDetailWithSubTemp.getPoint());

            // 当前周期 点数小于等于0 不进行扣点（有可能存在兑换完之后再退款的 这时候周期就变成负数了）
            if(pointDetailWithSubTemp.getPoint().compareTo(BigDecimal.ZERO) <= 0){
                log.info("存在小于等于0点数的订阅周期不进行扣点unionId:{} subId:{} point:{}", unionId, pointDetailWithSubTemp.getSubInfo().getId(), pointDetailWithSubTemp.getPoint());
                continue;
            }
            if (usePointTemp.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            // 当前周期直接能够覆盖
            if (pointDetailWithSubTemp.getPoint().compareTo(usePointTemp) >= 0) {
                BPointDetail bPointDetail = new BPointDetail();
                bPointDetail.setId(idConfig.getPointDetailId());
                bPointDetail.setExpireTime(pointDetailWithSubTemp.getExpireTime());
                bPointDetail.setActiveTime(new Date());
                bPointDetail.setPoint(usePointTemp.negate());
                bPointDetail.setUnionId(unionId);
                bPointDetail.setCustomerId(bUserPointOrder.getCustomerId());
                bPointDetail.setOutId(bUserPointOrder.getId());
                bPointDetail.setOutSn(bUserPointOrder.getOrderSn());
                bPointDetail.setCreateTime(new Date());
                bPointDetail.setUpdateTime(new Date());
                bPointDetail.setType(2L);
                bPointDetail.setSubId(pointDetailWithSubTemp.getSubInfo().getId());
                bPointDetailList.add(bPointDetail);
                usePointTemp = usePointTemp.subtract(usePointTemp);
                continue;
            }
            // 当前周期不能够覆盖 先扣除当前周期的
            if (pointDetailWithSubTemp.getPoint().compareTo(usePointTemp) < 0) {
                BPointDetail bPointDetail = new BPointDetail();
                bPointDetail.setId(idConfig.getPointDetailId());
                bPointDetail.setExpireTime(pointDetailWithSubTemp.getExpireTime());
                bPointDetail.setActiveTime(new Date());
                bPointDetail.setPoint(pointDetailWithSubTemp.getPoint().negate());
                bPointDetail.setUnionId(unionId);
                bPointDetail.setCustomerId(bUserPointOrder.getCustomerId());
                bPointDetail.setOutId(bUserPointOrder.getId());
                bPointDetail.setOutSn(bUserPointOrder.getOrderSn());
                bPointDetail.setCreateTime(new Date());
                bPointDetail.setUpdateTime(new Date());
                bPointDetail.setType(2L);
                bPointDetail.setSubId(pointDetailWithSubTemp.getSubInfo().getId());
                bPointDetailList.add(bPointDetail);
                usePointTemp = usePointTemp.subtract(pointDetailWithSubTemp.getPoint());
                continue;
            }

        }
        if(CollectionUtils.isEmpty(bPointDetailList)){
            throw new RuntimeException("封装点数错误");
        }
        if (usePointTemp.compareTo(BigDecimal.ZERO) != 0) {
            log.info("封装点数使用点数封装详细信息异常当前usePointTemp:{}", usePointTemp);
            throw new RuntimeException("封装点数错误");
        }
        return bPointDetailList;
    }

    void handleSpuStock(List<String> spuIdList)  {
        try{
            ModifySpuStockEvent event = new ModifySpuStockEvent(spuIdList,
                    RandomUtils.nextLong(),
                    RandomUtils.nextLong(),
                    UUID.randomUUID());
            log.info("事件-处理spu是否有库存标识userToken:{} event:{}",event.getUserToken(),JSONObject.toJSONString(event));
            modifySpuStockEventBus.post(event);
        }catch (Exception e){
            log.error("spu库存事件异常spuIdList:{}",spuIdList);
        }
    }

    void handleCreateJstPushOrder(String orderId){
        try{
            CreateJstPushOrderEvent event = new CreateJstPushOrderEvent(orderId,
                    RandomUtils.nextLong(),
                    RandomUtils.nextLong(),
                    UUID.randomUUID());
            log.info("事件-创建聚水潭推送事件userToken:{}",event.getUserToken());
            createJstPushOrderEventBus.post(event);
        }catch (Exception e){
            log.error("创建聚水潭推送事件orderId:{}",orderId);
        }
    }

    @Override
    public BLogisticsSnapshot getCustomerLogistic(GetCustomerLogisticReqDto getCustomerLogisticReqDto) {
        String orderDetailId = getCustomerLogisticReqDto.getOrderDetailId();
        BUserPointOrderDetail bUserPointOrderDetail = bUserPointOrderDetailMapper.selectById(orderDetailId);
        BLogisticsSnapshot bLogisticsSnapshot = ibLogisticsSnapshotService.getById(bUserPointOrderDetail.getLogisticsSnapshotId());
        return bLogisticsSnapshot;
    }

    void rulesUserPoint(String customerId) {
        try {
            RulesUserPointEvent event = new RulesUserPointEvent(customerId,
                    RandomUtils.nextLong(),
                    RandomUtils.nextLong(),
                    UUID.randomUUID());
            log.info("事件-规则用户点数userToken:{} event:{}", event.getUserToken(),JSONObject.toJSONString(event));
            rulesUserPointEventBus.post(event);
        } catch (Exception e) {
            log.info("规则用户点数userToken:{}", customerId);
        }
    }
}

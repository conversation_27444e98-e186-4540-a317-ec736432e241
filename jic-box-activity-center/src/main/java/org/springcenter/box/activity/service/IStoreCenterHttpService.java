package org.springcenter.box.activity.service;

import java.util.List;

public interface IStoreCenterHttpService {

    /**
     * 根据门店包ID获取所有门店ID
     *
     * @param storePackageId 门店包ID
     * @return 门店ID集合
     */
    List<Long> listStoreId(Long storePackageId);

    /**
     * 是否命中门店包的门店
     *
     * @param storePackageId 门店包ID
     * @param storeId 门店Id
     * @return true命中,false不命中
     */
    Boolean hitStore(Long storePackageId, Long storeId);
}

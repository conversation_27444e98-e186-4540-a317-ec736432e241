package org.springcenter.box.activity.service;

import org.springcenter.product.api.dto.background.SearchProductIdIsExistResp;

import java.util.List;
import java.util.Map;

public interface IProductHttpService {

    /**
     * 检查商品是否在商品包中
     *
     * @param pacIds     商品包集合
     * @param productIds 商品集合
     * @return 每个商品是否命中商品包中
     */
    List<SearchProductIdIsExistResp> judgeProductInPackage(List<String> pacIds, List<Long> productIds);

    /**
     * 检查商品是否在商品包中
     *
     * @param pacIds     商品包集合
     * @param productIds 商品集合
     * @return 0不存在 1存在
     */
    Map<Long, Integer> getProductId2ExistMap(List<String> pacIds, List<Long> productIds);
}

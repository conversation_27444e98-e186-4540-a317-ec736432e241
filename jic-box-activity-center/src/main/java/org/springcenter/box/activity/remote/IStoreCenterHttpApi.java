package org.springcenter.box.activity.remote;

import com.jnby.common.ResponseResult;
import org.springcenter.box.activity.remote.entity.HitStoreEntityReq;
import org.springcenter.box.activity.remote.entity.ListStoreEntityReq;
import org.springcenter.box.activity.remote.entity.ListStoreEntityResp;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

public interface IStoreCenterHttpApi {
    /**
     * 根据门店包号获取门店信息
     */
    @POST("sdk/store-center/store/package/location")
    Call<ResponseResult<List<ListStoreEntityResp>>> listStore(@Body ListStoreEntityReq req);

    /**
     * 是否命中门店包的门店
     */
    @POST("sdk/store-center/store/package/memberHit")
    Call<ResponseResult<Boolean>> hitStore(@Body HitStoreEntityReq req);
}

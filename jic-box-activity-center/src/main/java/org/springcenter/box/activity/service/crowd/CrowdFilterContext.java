package org.springcenter.box.activity.service.crowd;

import org.springcenter.box.activity.domain.box.Box;
import org.springcenter.box.activity.api.dto.BoxBuilderReq;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import lombok.Data;
import org.springcenter.box.activity.dto.request.CActivityEffectReq;
import org.springcenter.box.activity.service.IProductHttpService;
import org.springcenter.box.activity.service.IStoreCenterHttpService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 活动条件判断请求
 */
@Data
public class CrowdFilterContext {
    // 活动详情
    private CActivityInfoResp activityInfo;
    // BOX服务单
    private Box box;
    // 是否存在DD单
    private Boolean hasOrder;
    // BOX单下所有付款成功的orderId
    private List<String> orderIds;
    // 搭盒人员
    private BoxBuilderReq boxBuilderReq;
    // 导购门店ID
    private String guiderStoreId;

    // 选择的商品
//    private List<Long> productIds;
    // 当前选择的商品（如果商品是指定商品包的条件则在内部过滤一次）
    private List<CActivityEffectReq.ProductReq> productList;

    // 商品接口服务
    private IProductHttpService iProductHttpService;
    // 门店包服务
    private IStoreCenterHttpService iStoreCenterHttpService;

    // 失败原因
    private String errorMsg;
}

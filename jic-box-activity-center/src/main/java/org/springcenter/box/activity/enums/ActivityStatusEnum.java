package org.springcenter.box.activity.enums;

import org.springcenter.box.activity.config.exception.BoxActivityException;
import lombok.Getter;

import java.util.Date;

/**
 * 活动状态数据库枚举
 */
@Getter
public enum ActivityStatusEnum {
    EFFECTIVE(1, "有效"),
    END(2, "结束/作废"),
    ;

    private final Integer code;
    private final String msg;

    ActivityStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 获取前端状态枚举
     *
     * @return
     */
    public static FrontActivityStatusEnum getFrontStatusEnum(Integer code, Date startTime, Date endTime, Date evaluateTime, Integer isDelete) {
        if (code == null || startTime == null || endTime == null || evaluateTime == null) {
            throw new BoxActivityException("状态枚举异常");
        }
        if (isDelete != null && isDelete == 1) {
            return FrontActivityStatusEnum.DOWN;
        }
        if (END.getCode().equals(code)) {
            return FrontActivityStatusEnum.ENDING;
        }
        if (EFFECTIVE.getCode().equals(code)) {
            Date now = new Date();
            if (now.before(startTime)) {
                return FrontActivityStatusEnum.NOT_START;
            } else if (now.after(endTime) && now.before(evaluateTime)) {
                return FrontActivityStatusEnum.ACCOUNTING;
            } else if (now.after(evaluateTime)) {
                return FrontActivityStatusEnum.WAITING;
            } else {
                return FrontActivityStatusEnum.RUNNING;
            }
        }
        throw new BoxActivityException("状态枚举异常");
    }

    public static boolean isEffect(Integer code) {
        return EFFECTIVE.getCode().equals(code);
    }
}

package org.springcenter.box.activity.mq;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.cloud.sleuth.annotation.NewSpan;

/**
 * 消息监听路由
 * 1、先按照topic进行路由，然后根据同一个topic下的tags进行广播路由（保证所有的子消费者都能消费到）
 * <AUTHOR>
 * @Date 2021/7/6 4:53 下午
 * @Version 1.0
 */
public interface IMessageListener {
    /**
     * 要订阅的topic
     * @return
     */
    String getTopic();

    /**
     * 需要消费的tag，多个使用 a||b||c
     * @return
     */
    String getTags();

    /**
     * 业务需要处理逻辑
     * @param msg
     * @return
     */
    @NewSpan
    ConsumeConcurrentlyStatus consume(Message msg);
}

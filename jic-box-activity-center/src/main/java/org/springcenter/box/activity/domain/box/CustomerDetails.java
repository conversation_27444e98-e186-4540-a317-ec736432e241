package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-10-11 13:44:16
 * @Description: 
 */
@Data
@TableName("CUSTOMER_DETAILS")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="CustomerDetails对象", description="")
public class CustomerDetails implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "顾客unionid")
    @TableField("UNIONID")
    private String unionid;
    @ApiModelProperty(value = "外部id")
    @TableField("OUT_ID")
    private Long outId;
    @TableField("OPENID")
    private String openid;
    @TableField("NICK_NAME")
    private String nickName;
    @ApiModelProperty(value = "头像")
    @TableField("HEAD_URL")
    private String headUrl;
    @ApiModelProperty(value = "手机号")
    @TableField("PHONE")
    private String phone;
    @ApiModelProperty(value = "金币数")
    @TableField("COIN")
    private String coin;
    @ApiModelProperty(value = "体重")
    @TableField("WEIGHT")
    private String weight;
    @ApiModelProperty(value = "身高")
    @TableField("HEIGHT")
    private String height;
    @ApiModelProperty(value = "肤色")
    @TableField("SKIN")
    private String skin;
    @ApiModelProperty(value = "身型")
    @TableField("BODY")
    private String body;
    @ApiModelProperty(value = "0 女 1男")
    @TableField("GENDER")
    private Long gender;
    @ApiModelProperty(value = "裤子尺码")
    @TableField("TROUSERS")
    private String trousers;
    @ApiModelProperty(value = "鞋子尺码")
    @TableField("SHOES")
    private String shoes;
    @ApiModelProperty(value = "外套尺码")
    @TableField("COAT")
    private String coat;
    @ApiModelProperty(value = "衬衫/上衣尺码")
    @TableField("SHIRT")
    private String shirt;
    @ApiModelProperty(value = "内衣尺码（仅女性）")
    @TableField("UNDERWEAR")
    private String underwear;
    @ApiModelProperty(value = "风格偏好")
    @TableField("STYLE")
    private String style;
    @ApiModelProperty(value = "品牌系列偏好（仅女性）")
    @TableField("BRAND")
    private String brand;
    @ApiModelProperty(value = "上衣偏好")
    @TableField("TOP_CLOTHING")
    private String topClothing;
    @ApiModelProperty(value = "下装偏好")
    @TableField("UNDER_CLOTHING")
    private String underClothing;
    @ApiModelProperty(value = "订阅过期时间")
    @TableField("SUB_EXPIRE_TIME")
    private Date subExpireTime;
    @ApiModelProperty(value = "理型师id 0为未分配理型师")
    @TableField("FASHIONER_ID")
    private String fashionerId;
    @ApiModelProperty(value = "顾客答题问卷id")
    @TableField("SURVEY_ID")
    private String surveyId;
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "0未答题 1已答题")
    @TableField("HAS_ANSWER")
    private Long hasAnswer;
    @ApiModelProperty(value = "0未显示地址提示 1已显示地址提示")
    @TableField("SHOW_ADDR_NOTICE")
    private Long showAddrNotice;
    @ApiModelProperty(value = "0-主动注册 1-其他被邀请注册")
    @TableField("REG_FROM")
    private String regFrom;
    @ApiModelProperty(value = "0-订阅服务 1-主题盒子 2-理型师邀请 3-邀请订阅  4-心意盒子")
    @TableField("REG_TYPE")
    private Long regType;
    @ApiModelProperty(value = "顾客分类id(默认:0)")
    @TableField("CATEGORY_ID")
    private String categoryId;
    @ApiModelProperty(value = "推送备注")
    @TableField("PUSH_MEMO")
    private String pushMemo;
    @ApiModelProperty(value = "udeskid")
    @TableField("UDESK_ID")
    private BigDecimal udeskId;
    @ApiModelProperty(value = "肩宽")
    @TableField("SHOULDER_WIDTH")
    private String shoulderWidth;
    @ApiModelProperty(value = "胸围")
    @TableField("BUST")
    private String bust;
    @ApiModelProperty(value = "臀围")
    @TableField("HIPLINE")
    private String hipline;
    @ApiModelProperty(value = "腰围")
    @TableField("WAISTLINE")
    private String waistline;
    @ApiModelProperty(value = "邀请码")
    @TableField("INVITE_CODE")
    private String inviteCode;
    @ApiModelProperty(value = "小程序码")
    @TableField("MIN_QRCODE")
    private String minQrcode;
    @ApiModelProperty(value = "信用金状态 10-未支付 20-微信支付 30-累计消费免押 40-白名单 50-支付分")
    @TableField("CREDIT_STATUS")
    private Long creditStatus;
    @ApiModelProperty(value = "身体特征")
    @TableField("BODY_FEATURE")
    private String bodyFeature;
    @ApiModelProperty(value = "基础色")
    @TableField("BASE_COLOR")
    private String baseColor;
    @ApiModelProperty(value = "彩色")
    @TableField("COLOR")
    private String color;
    @ApiModelProperty(value = "面料")
    @TableField("FABRIC")
    private String fabric;
    @ApiModelProperty(value = "星座")
    @TableField("CONSTELLATION")
    private String constellation;
    @ApiModelProperty(value = "生日")
    @TableField("BIRTHDAY")
    private String birthday;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "用户类型")
    @TableField("CUSTOMER_TYPE_ID")
    private String customerTypeId;
    @ApiModelProperty(value = "权益类型（0:券;1:现金;2:全部）")
    @TableField("EQUITY_TYPE")
    private Long equityType;
    @ApiModelProperty(value = "注册介绍人")
    @TableField("REFERRER_ID")
    private String referrerId;
    @ApiModelProperty(value = "现金")
    @TableField("CASH")
    private Float cash;
    @ApiModelProperty(value = "合伙人")
    @TableField("PARTNER_ID")
    private String partnerId;
    @ApiModelProperty(value = "注册领取的权益")
    @TableField("EQUITY_ID")
    private String equityId;
    @ApiModelProperty(value = "是否提示（0 ：不提示  1：提示）")
    @TableField("IS_TIPS")
    private Long isTips;
    @ApiModelProperty(value = "线下注册品牌")
    @TableField("REG_BRAND")
    private String regBrand;
    @ApiModelProperty(value = "1服务结束")
    @TableField("ENDTYPE")
    private Long endtype;
    @ApiModelProperty(value = "是否参加主题盒子活动订阅0无1普通订阅2活动续订")
    @TableField("ACTIVITY_SUBSCRIBE")
    private BigDecimal activitySubscribe;
    @ApiModelProperty(value = "头部")
    @TableField("HEAD")
    private String head;
    @ApiModelProperty(value = "胳膊")
    @TableField("GB")
    private String gb;
    @ApiModelProperty(value = "腰")
    @TableField("YAO")
    private String yao;
    @ApiModelProperty(value = "风格偏好")
    @TableField("STYLES")
    private String styles;
    @ApiModelProperty(value = "腿 （腿）")
    @TableField("LEG")
    private String leg;
    @ApiModelProperty(value = "累计消费")
    @TableField("TOT_ACTUAL_AMOUNT")
    private BigDecimal totActualAmount;
    @ApiModelProperty(value = "联系时间")
    @TableField("TOUCH_TIME")
    private Date touchTime;
    @ApiModelProperty(value = "联系次数")
    @TableField("TOUCH_NUM")
    private BigDecimal touchNum;
    @ApiModelProperty(value = "可服务列表移除次数")
    @TableField("RM_NUM")
    private BigDecimal rmNum;
    @ApiModelProperty(value = "集团卡类型id")
    @TableField("VIPTYPE_ID")
    private BigDecimal viptypeId;
    @ApiModelProperty(value = "集团卡类型名称")
    @TableField("VIPTYPE_NAME")
    private String viptypeName;
    @ApiModelProperty(value = "最后一次购买的品牌")
    @TableField("LAST_BUY_BRAND")
    private String lastBuyBrand;
    @ApiModelProperty(value = "集团卡号")
    @TableField("JNBY_CARDNO")
    private String jnbyCardno;
    @ApiModelProperty(value = "后台备注")
    @TableField("BG_REMARK")
    private String bgRemark;
    @ApiModelProperty(value = "渠道id")
    @TableField("CHANNEL_ID")
    private String channelId;
    @ApiModelProperty(value = "是否首次主动要盒  1是 0否")
    @TableField("FIRST_ASK_BOX")
    private Integer firstAskBox;

}

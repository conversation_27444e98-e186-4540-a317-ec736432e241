package org.springcenter.box.activity.config;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 1/29/21 10:38 AM
 */
@Component
@RefreshScope
public class BusProperties {
    public String isInMemory = "false";
    public String maxFailureRetries = "10";
    public String minInFlightEntries = "1";
    public String maxInFlightEntries = "100";
    public String maxEntriesClaimed = "10";
    public String persistentQueueMode = "STICKY_POLLING";
    public String claimedTime = "5m";
    public String pollingSleepTimeMs = "3000";
    public String isProcessingOff = "false";
    public String maxDispatchThreads = "100";
    public String nbLifecycleDispatchThreads = "1";
    public String nbLifecycleCompleteThreads = "2";
    public String capacity = "30000";
    public String tableName = "ACTIVITY_BUS_EVENTS";
    public String historyTableName = "ACTIVITY_BUS_EVENTS_HISTORY";
    public String reapThreshold = "10m";
    public String maxReDispatchCount = "10";
    public String reapSchedule = "3m";

    public String getIsInMemory() {
        return isInMemory;
    }

    public void setIsInMemory(String isInMemory) {
        this.isInMemory = isInMemory;
    }

    public String getMaxFailureRetries() {
        return maxFailureRetries;
    }

    public void setMaxFailureRetries(String maxFailureRetries) {
        this.maxFailureRetries = maxFailureRetries;
    }

    public String getMinInFlightEntries() {
        return minInFlightEntries;
    }

    public void setMinInFlightEntries(String minInFlightEntries) {
        this.minInFlightEntries = minInFlightEntries;
    }

    public String getMaxInFlightEntries() {
        return maxInFlightEntries;
    }

    public void setMaxInFlightEntries(String maxInFlightEntries) {
        this.maxInFlightEntries = maxInFlightEntries;
    }

    public String getMaxEntriesClaimed() {
        return maxEntriesClaimed;
    }

    public void setMaxEntriesClaimed(String maxEntriesClaimed) {
        this.maxEntriesClaimed = maxEntriesClaimed;
    }

    public String getPersistentQueueMode() {
        return persistentQueueMode;
    }

    public void setPersistentQueueMode(String persistentQueueMode) {
        this.persistentQueueMode = persistentQueueMode;
    }

    public String getClaimedTime() {
        return claimedTime;
    }

    public void setClaimedTime(String claimedTime) {
        this.claimedTime = claimedTime;
    }

    public String getPollingSleepTimeMs() {
        return pollingSleepTimeMs;
    }

    public void setPollingSleepTimeMs(String pollingSleepTimeMs) {
        this.pollingSleepTimeMs = pollingSleepTimeMs;
    }

    public String getIsProcessingOff() {
        return isProcessingOff;
    }

    public void setIsProcessingOff(String isProcessingOff) {
        this.isProcessingOff = isProcessingOff;
    }

    public String getMaxDispatchThreads() {
        return maxDispatchThreads;
    }

    public void setMaxDispatchThreads(String maxDispatchThreads) {
        this.maxDispatchThreads = maxDispatchThreads;
    }

    public String getNbLifecycleDispatchThreads() {
        return nbLifecycleDispatchThreads;
    }

    public void setNbLifecycleDispatchThreads(String nbLifecycleDispatchThreads) {
        this.nbLifecycleDispatchThreads = nbLifecycleDispatchThreads;
    }

    public String getNbLifecycleCompleteThreads() {
        return nbLifecycleCompleteThreads;
    }

    public void setNbLifecycleCompleteThreads(String nbLifecycleCompleteThreads) {
        this.nbLifecycleCompleteThreads = nbLifecycleCompleteThreads;
    }

    public String getCapacity() {
        return capacity;
    }

    public void setCapacity(String capacity) {
        this.capacity = capacity;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getHistoryTableName() {
        return historyTableName;
    }

    public void setHistoryTableName(String historyTableName) {
        this.historyTableName = historyTableName;
    }

    public String getReapThreshold() {
        return reapThreshold;
    }

    public void setReapThreshold(String reapThreshold) {
        this.reapThreshold = reapThreshold;
    }

    public String getMaxReDispatchCount() {
        return maxReDispatchCount;
    }

    public void setMaxReDispatchCount(String maxReDispatchCount) {
        this.maxReDispatchCount = maxReDispatchCount;
    }

    public String getReapSchedule() {
        return reapSchedule;
    }

    public void setReapSchedule(String reapSchedule) {
        this.reapSchedule = reapSchedule;
    }
}

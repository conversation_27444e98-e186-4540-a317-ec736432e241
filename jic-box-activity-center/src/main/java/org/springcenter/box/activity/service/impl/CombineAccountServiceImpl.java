package org.springcenter.box.activity.service.impl;

import com.google.common.base.Preconditions;
import org.springcenter.box.activity.domain.box.BUserPointAccount;
import org.springcenter.box.activity.domain.box.CustomerDetails;
import org.springcenter.box.activity.mapper.box.*;

import org.springcenter.box.activity.service.IBUserPointAccountService;
import org.springcenter.box.activity.service.ICombineAccountService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CombineAccountServiceImpl implements ICombineAccountService {

    @Resource
    private BUserPointAccountMapper bUserPointAccountMapper;

    @Resource
    private CustomerDetailsMapper customerDetailsMapper;


    @Resource
    private IBUserPointAccountService ibUserPointAccountService;


    @Override
    public BUserPointAccount getAccountByCustomerId(String customerId) {
        BUserPointAccount bUserPointAccount = bUserPointAccountMapper.selectByCustomerId(customerId);
        if (bUserPointAccount != null) {
            return bUserPointAccount;
        }

        CustomerDetails customerDetails = Preconditions.checkNotNull(customerDetailsMapper.selectById(customerId),"用户信息不存在");

        BUserPointAccount insertUserPointAccount = new BUserPointAccount();
        insertUserPointAccount.setId(customerId);
        insertUserPointAccount.setUnionId(customerDetails.getUnionid());
        insertUserPointAccount.setCreateTime(new Date());
        insertUserPointAccount.setUpdateTime(new Date());
        ibUserPointAccountService.saveOrUpdate(insertUserPointAccount);

        return  bUserPointAccountMapper.selectByCustomerId(customerId);
    }




    @Override
    public List<BUserPointAccount> getAccountByCustomerIdList(List<String> customerIdList) {
        return bUserPointAccountMapper.getListByCustomerIds(customerIdList);
    }

    @Override
    public List<BUserPointAccount> getAccountByUnionIds(List<String> unionIds) {
        return bUserPointAccountMapper.getListByUnionIds(unionIds);

    }

    @Override
    public BUserPointAccount getAccountByUnionId(String unionId) {
        return bUserPointAccountMapper.selectByUnionId(unionId);
    }


}

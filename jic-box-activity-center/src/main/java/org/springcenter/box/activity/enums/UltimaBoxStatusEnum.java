package org.springcenter.box.activity.enums;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/11/9 10:50
 */
public enum UltimaBoxStatusEnum {
    WAIT_SUBMIT(-1,"待提交"),
    WAIT_SEND(0, "待发货"),
    SENDING(1, "发货中"),
    SIGNED(2, "已签收"),
    WAIT_RETURN(9, "待还货"),
    RETURNING(10, "还货中"),
    WAIT_STORAGE(3, "待入库"),
    FISHED(5,"已完成"),
    CANCEL(6, "已取消"),
    invalid(7, "系统作废"),
    ;

    private Integer code;
    private String desc;

    UltimaBoxStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }



    public static UltimaBoxStatusEnum find(Integer code){
        UltimaBoxStatusEnum[] enums = UltimaBoxStatusEnum.values();
        UltimaBoxStatusEnum  boxStatusEnum = null;

        for (int i = 0; i < enums.length; i++) {
            if (enums[i].getCode().equals(code)){
                boxStatusEnum = enums[i];
            }
        }
        return boxStatusEnum;
    }

}

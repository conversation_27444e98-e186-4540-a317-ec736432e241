package org.springcenter.box.activity.service.crowd;

import org.springcenter.box.activity.domain.box.Box;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消费者人群判断
 */
@Slf4j
@Service
public class CustomerFilter implements CrowdFilterHandler {

    CrowdFilterHandler next = null;

    @Override
    public String filterName() {
        return "客户类型";
    }

    @Override
    public CrowdFilterHandler getNext() {
        return next;
    }
    @Override
    public void setNext(CrowdFilterHandler next) {
        this.next = next;
    }

    @Override
    public boolean filter(CrowdFilterContext context) {
        CActivityInfoResp activityInfo = context.getActivityInfo();
        Box box = context.getBox();
        // 多选条件，任一符合即可
        List<Integer> crowdList = activityInfo.getCrowd();
        if (CollectionUtils.isEmpty(crowdList) || box == null) {
            log.info("客户类型为空，无法判断客户条件");
            return false;
        }
        // 是否新客，是否有DD单
        boolean newCustomer = false;
        if (Boolean.FALSE.equals(context.getHasOrder())) {
            newCustomer = true;
        }

        // 是否自提
        boolean selfTake = false;
        String trackNumber = context.getBox().getTrackNumber();
        if ("自提".equals(trackNumber)) {
            selfTake = true;
        }
        log.info("当前消费者是否新客:{}, 是否自提:{}", newCustomer, selfTake);

        // 参与人群：1=新客+非自提盒子，2=新客+自提盒子，3=老客+非自提盒子，4=老客+自提盒子
        for (Integer crowd : crowdList) {
            if (crowd == 1 && newCustomer && !selfTake) {
                log.info("命中【新客+非自提盒子】，可参与");
                return true;
            } else if (crowd == 2 && newCustomer && selfTake) {
                log.info("命中【新客+自提盒子】，可参与");
                return true;
            } else if (crowd == 3 && !newCustomer && !selfTake) {
                log.info("命中【老客+非自提盒子】，可参与");
                return true;
            } else if (crowd == 4 && !newCustomer && selfTake) {
                log.info("命中【老客+自提盒子】，可参与");
                return true;
            }
        }
        context.setErrorMsg("参与人群不符合条件");
        return false;
    }

    @Override
    public void startLog() {
        log.info("开始判断客户条件");
    }

    @Override
    public void endLog() {
        log.info("结束判断客户条件");
    }
}

package org.springcenter.box.activity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum BoxSourceTypeEnum {


    FREE(0, "免费盒子"),
    SUB(1, "订阅要盒"),
    SINGLE(2, "单次要盒"),
    ;

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    // 是否单次盒子
    public static boolean isSingle(Integer code) {
        if (SINGLE.getCode().equals(code)) {
            return true;
        }
        if (FREE.getCode().equals(code)) {
            return true;
        }
        return false;
    }

    // 是否订阅盒子
    public static boolean isSub(Integer code) {
        return SUB.getCode().equals(code);
    }
}

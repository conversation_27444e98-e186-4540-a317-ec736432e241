package org.springcenter.box.activity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付单转换类
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SubOrderDto {
    @ApiModelProperty(value = "商品id")
    private String productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
}

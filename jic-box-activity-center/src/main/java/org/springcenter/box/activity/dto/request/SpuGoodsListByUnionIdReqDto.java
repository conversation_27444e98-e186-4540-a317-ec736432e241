package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class SpuGoodsListByUnionIdReqDto {
    private String unionId;

    @ApiModelProperty(value = "上下架(0下架 1上架)")
    private Long status;

    @ApiModelProperty(value = "是否有库存（0无 1有）")
    private Long hasStock;


    @ApiModelProperty(value = "起始点数")
    private BigDecimal point;
}

package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springcenter.box.activity.api.dto.CActivityUpdateTaskReq;
import org.springcenter.box.activity.domain.box.*;
import org.springcenter.box.activity.dto.OrderDto;
import org.springcenter.box.activity.dto.SubOrderDto;
import org.springcenter.box.activity.dto.request.OrderReq;
import org.springcenter.box.activity.enums.BoxRefundStatusEnum;
import org.springcenter.box.activity.enums.OrderStatusEnum;
import org.springcenter.box.activity.enums.PaymentStatusEnum;
import org.springcenter.box.activity.mapper.box.BoxRefundDetailsMapper;
import org.springcenter.box.activity.mapper.box.BoxRefundMapper;
import org.springcenter.box.activity.mapper.box.OrderDetailMapper;
import org.springcenter.box.activity.mapper.box.OrderMapper;
import org.springcenter.box.activity.mapper.box.condition.OrderQueryCondition;
import org.springcenter.box.activity.service.ICActivityService;
import org.springcenter.box.activity.service.ICustomerDetailsService;
import org.springcenter.box.activity.service.IOrderService;
import org.springcenter.box.activity.service.IProductHttpService;
import org.springcenter.box.activity.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderServiceImpl implements IOrderService {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private BoxRefundMapper boxRefundMapper;
    @Resource
    private ICustomerDetailsService customerDetailsService;
    @Resource
    private ICActivityService activityService;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Resource
    private IProductHttpService iProductHttpService;
    @Autowired
    private BoxRefundDetailsMapper boxRefundDetailsMapper;

    /**
     * 根据 order_id 的列表查询所有支付子单 ORDER_DETAIL 状态=已支付
     * 过滤出商品包里包含该商品的子单
     * 根据 order_detail_id 查询所有退款单 BOX_REFUND_DETAILS
     *
     * 组合支付组成：
     * 储值卡+商场代金券+微信支付/支付宝支付/花呗支付
     * (ORDER_DETAIL中取 PRICEACTUAL+BALANCE_AMT+SHOP_VOU_AMT) - (BOX_REFUND_DETAILS 中取 REFUND_AMOUNT+REFUND_BALANCE)
     *
     * @param orderReq
     * @return
     */
    @Override
    public List<OrderDto> listOrderDto(OrderReq orderReq) {
        log.info("获取DD单列表和实付金额 请求参数:{}", JSON.toJSONString(orderReq));
        orderReq.check();

        OrderQueryCondition condition = OrderQueryCondition.builder()
                .boxSn(orderReq.getBoxSn())
                .paymentUpdateTimeStart(DateUtil.getStrDateTime(orderReq.getCreateStartTime()))
                .paymentUpdateTimeEnd(DateUtil.getStrDateTime(orderReq.getCreateEndTime()))
                .orderStatusList(OrderStatusEnum.paidCodeList())
                .paymentStatus(PaymentStatusEnum.success.getCode())
                .build();

        // 拉取所有付款时间在活动区间的DD单（order_ + payment 表）
        List<OrderDto> orderDtoList = orderMapper.listOrderIdLeftJoinPayment(condition);
        log.info("拉取所有付款时间在活动区间的DD单:{}", JSON.toJSONString(orderDtoList));
        if (CollectionUtils.isEmpty(orderDtoList)) {
            return new ArrayList<>();
        }
        // 根据 order_id 的列表查询所有支付子单 ORDER_DETAIL 状态=已支付
        LambdaQueryWrapper<OrderDetail> orderDetailQuery = new LambdaQueryWrapper<>();
        List<String> orderIdList = orderDtoList.stream().map(OrderDto::getId).collect(Collectors.toList());
        orderDetailQuery.in(OrderDetail::getOrderId, orderIdList);
        orderDetailQuery.eq(OrderDetail::getStatus, OrderStatusEnum.paid.getCode());
        log.info("所有支付子单入参:{}", JSON.toJSONString(orderIdList));
        List<OrderDetail> orderDetailList = orderDetailMapper.selectList(orderDetailQuery);
        log.info("所有支付子单回参:{}", JSON.toJSONString(orderDetailList));
        if (CollectionUtils.isEmpty(orderDetailList)) {
            return new ArrayList<>();
        }
        // 过滤出商品包里包含该商品的子单
        if (Boolean.TRUE.equals(orderReq.getNeedFilterItem()) && CollectionUtils.isNotEmpty(orderReq.getPacIds())) {
            log.info("需要过滤商品");
            List<Long> productIds = orderDetailList.stream().map(OrderDetail::getOutId).distinct().map(Long::new).collect(Collectors.toList());
            List<String> pacIds = orderReq.getPacIds();
            Map<Long, Integer> productId2Exist = iProductHttpService.getProductId2ExistMap(pacIds, productIds);
            Iterator<OrderDetail> orderIter = orderDetailList.iterator();
            while (orderIter.hasNext()) {
                OrderDetail next = orderIter.next();
                Integer exist = productId2Exist.get(Long.valueOf(next.getOutId()));
                // 不存在
                if (exist != null && exist == 0) {
                    log.info("商品未命中指定商品包，需要过滤 商品ID[{}]", next.getOutId());
                    orderIter.remove();
                }
            }
        }
        if (CollectionUtils.isEmpty(orderDetailList)) {
            log.info("过滤商品后，没有存在的子订单");
            return new ArrayList<>();
        }
        // 根据 order_detail_id 查询所有退款单 BOX_REFUND_DETAILS
        LambdaQueryWrapper<BoxRefundDetails> refundDetailsQuery = new LambdaQueryWrapper<>();
        List<String> orderDetailIdList = orderDetailList.stream().map(OrderDetail::getId).collect(Collectors.toList());
        refundDetailsQuery.in(BoxRefundDetails::getOrderDetailId, orderDetailIdList);
        refundDetailsQuery.eq(BoxRefundDetails::getStatus, BoxRefundStatusEnum.REFUNDSUCCESS.getCode());
        log.info("该BOX服务单下的所有退款单列表入参:{}", JSON.toJSONString(orderDetailIdList));
        List<BoxRefundDetails> refundDetailsList = boxRefundDetailsMapper.selectList(refundDetailsQuery);
        log.info("该BOX服务单下的所有退款单列表结果:{}", JSON.toJSONString(refundDetailsList));

        // 退款子单map
        Map<String, List<BoxRefundDetails>> orderDetailId2RefundDetailsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(refundDetailsList)) {
            orderDetailId2RefundDetailsMap = refundDetailsList.stream().collect(Collectors.groupingBy(BoxRefundDetails::getOrderDetailId));
        }

        // 支付子单map
        Map<String, List<OrderDetail>> orderId2OrderDetailList = orderDetailList.stream().collect(Collectors.groupingBy(OrderDetail::getOrderId));

        Iterator<OrderDto> iterator = orderDtoList.iterator();
        while (iterator.hasNext()) {
            OrderDto orderDto = iterator.next();
            orderDto.setSubOrders(Lists.newArrayList());
            log.info("当前订单[{}]", orderDto.getOrderSn());
            String orderId = orderDto.getId();
            List<OrderDetail> orderDetailListRsp = orderId2OrderDetailList.get(orderId);
            if (CollectionUtils.isEmpty(orderDetailListRsp)) {
                log.info("当前订单[{}]，根据子单商品过滤后子订单，无需累加，过滤执行", orderDto.getOrderSn());
                iterator.remove();
                continue;
            }
            BigDecimal orderPayment = BigDecimal.ZERO;
            // 添加实付件数计数
            int quantity = 0;
            for (OrderDetail orderDetailRsp : orderDetailListRsp) {
                // (ORDER_DETAIL 中取 PRICEACTUAL+BALANCE_AMT+SHOP_VOU_AMT) - (BOX_REFUND_DETAILS 中取 REFUND_AMOUNT+REFUND_BALANCE)
                BigDecimal priceactual = orderDetailRsp.getPriceactual() == null ? BigDecimal.ZERO : orderDetailRsp.getPriceactual();
                BigDecimal balanceAmt = orderDetailRsp.getBalanceAmt() == null ? BigDecimal.ZERO : orderDetailRsp.getBalanceAmt();
                BigDecimal shopVouAmt = orderDetailRsp.getShopVouAmt() == null ? BigDecimal.ZERO : orderDetailRsp.getShopVouAmt();
                BigDecimal subPayment = priceactual.add(balanceAmt).add(shopVouAmt);
                // 未退款的金额
                orderPayment = orderPayment.add(subPayment);
                quantity++;
                log.info("当前订单SN-子单ID:[{}-{}],支付金额[{}],储值卡金额[{}],商场代金券[{}],子单汇总实付[{}]", orderDto.getOrderSn(), orderDetailRsp.getId(),
                        priceactual, balanceAmt, shopVouAmt, subPayment);

                List<BoxRefundDetails> boxRefundDetails = orderDetailId2RefundDetailsMap.get(orderDetailRsp.getId());
                if (CollectionUtils.isEmpty(boxRefundDetails)) {
                    orderDto.getSubOrders().add(new SubOrderDto(orderDetailRsp.getOutId(), orderDetailRsp.getProductName()));
                    log.info("当前订单SN-子单ID:[{}-{}]，不存在退款单，不处理退款金额", orderDto.getOrderSn(), orderDetailRsp.getId());
                    continue;
                }

                BigDecimal refundAmount = boxRefundDetails.stream().map(refund -> {
                    if (refund.getRefundAmount() == null) {
                        refund.setRefundAmount(BigDecimal.ZERO);
                    }
                    if (refund.getRefundBalance() == null) {
                        refund.setRefundBalance(BigDecimal.ZERO);
                    }
                    return refund.getRefundAmount().add(refund.getRefundBalance());
                }).reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal totalPayment = subPayment.subtract(refundAmount);
                log.info("当前订单SN-子单ID:[{}-{}] 退款金额[{}], 最终实付金额:[{}]", orderDto.getOrderSn(), orderDetailRsp.getId(), refundAmount, totalPayment);
                if (totalPayment.compareTo(BigDecimal.ZERO) <= 0) {
                    log.info("当前订单SN-子单ID:[{}-{}] 排除退款后实付金额等于0", orderDto.getOrderSn(), orderDetailRsp.getId());
                    totalPayment = BigDecimal.ZERO;
                    quantity--;
                } else {
                    orderDto.getSubOrders().add(new SubOrderDto(orderDetailRsp.getOutId(), orderDetailRsp.getProductName()));
                }
                orderPayment = totalPayment;
            }
            orderDto.setPayment(orderPayment);
            orderDto.setQuantity(quantity);
            log.info("当前订单[{}]，最终实付金额[{}]，实付件数[{}]", orderDto.getOrderSn(), orderPayment, quantity);
        }
        return orderDtoList;
    }

    @Override
    public Order getOrderByUnionId(String unionId) {
        log.info("根据unionId获取DD单 入参:{}", unionId);
        Map<String, String> map = customerDetailsService.unionId2CustomerIdMap(Collections.singletonList(unionId));
        String cusId = map.get(unionId);
        if (StringUtils.isBlank(cusId)) {
            return null;
        }
        LambdaQueryWrapper<Order> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.eq(Order::getCustomerId, cusId);
        orderQuery.notIn(Order::getOrderStatus, OrderStatusEnum.unpay.getCode(), OrderStatusEnum.cancel.getCode());
        orderQuery.last("and rownum = 1");
        Order order = orderMapper.selectOne(orderQuery);
        log.info("根据unionId获取DD单 回参:{}", JSON.toJSONString(order));
        return order;
    }

    @Override
    public List<Order> listOrderByUpdateTimeRange(Date startTime, Date endTime, Page page) {
        com.github.pagehelper.Page<Order> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        LambdaQueryWrapper<Order> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.select(Order::getOrderSn, Order::getBoxSn, Order::getFullActivityId);
        orderQuery.in(Order::getOrderStatus, OrderStatusEnum.paidCodeList());
        orderQuery.ge(Order::getUpdateTime, startTime);
        orderQuery.le(Order::getUpdateTime, endTime);
        log.info("查询订单列表 入参:{}", JSON.toJSONString(orderQuery));
        List<Order> orders = orderMapper.selectList(orderQuery);
        log.info("查询订单列表 DB回参:{}", JSON.toJSONString(orders));
        PageInfo<Order> pageInfo = new PageInfo(hPage);
        pageInfo.setList(orders);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    @Override
    public List<BoxRefund> listRefundByUpdateTimeRange(Date startTime, Date endTime, Page page) {
        com.github.pagehelper.Page<BoxRefund> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        LambdaQueryWrapper<BoxRefund> query = new LambdaQueryWrapper<>();
        query.ge(BoxRefund::getUpdateTime, startTime);
        query.le(BoxRefund::getUpdateTime, endTime);
        log.info("查询退款列表 入参:{}", JSON.toJSONString(query));
        List<BoxRefund> boxRefunds = boxRefundMapper.selectList(query);
        log.info("查询退款列表 DB回参:{}", JSON.toJSONString(boxRefunds));
        PageInfo<BoxRefund> pageInfo = new PageInfo(hPage);
        pageInfo.setList(boxRefunds);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    @Override
    public void scanIncOrder(Date startTime, Date endTime) {
        Page page = new Page(1, 100);
        List<Order> orderList = listOrderByUpdateTimeRange(startTime, endTime, page);
        if (CollectionUtils.isEmpty(orderList)) {
            log.info("没有增量订单，本轮调度执行结束.");
            return;
        }
        int pages = page.getPages();
        for (int i = 1; i <= pages; i++) {
            if (i > 1) {
                page.setPageNo(i);
                orderList = listOrderByUpdateTimeRange(startTime, endTime, page);
                if (CollectionUtils.isEmpty(orderList)) {
                    log.info("没有增量订单，本轮调度执行结束.");
                    break;
                }
            }

            for (Order order : orderList) {
                String orderSn = order.getOrderSn();
                String cActivityId = order.getFullActivityId();
                try {
                    CustomerDetails user = customerDetailsService.getById(order.getCustomerId());
                    String unionid = user.getUnionid();
                    CActivityUpdateTaskReq req = new CActivityUpdateTaskReq();
                    req.setType(1);
                    req.setActivityId(cActivityId);
                    req.setBoxNo(order.getBoxSn());
                    req.setUnionId(unionid);
                    log.info("支付单:[{}],触发任务更新，入参:{}", orderSn, JSON.toJSONString(req));
                    activityService.updateTask(req);
                    log.info("支付单:[{}],触发任务更新，完成", orderSn);
                } catch (Exception e) {
                    log.error("支付单:[{}],触发任务更新异常", orderSn, e);
                }
            }
        }
    }

    @Override
    public void scanIncRefund(Date startTime, Date endTime) {
        Page page = new Page(1, 100);
        List<BoxRefund> refundList = listRefundByUpdateTimeRange(startTime, endTime, page);
        if (CollectionUtils.isEmpty(refundList)) {
            log.info("没有增量退款单，本轮调度执行结束.");
            return;
        }
        int pages = page.getPages();
        for (int i = 1; i <= pages; i++) {
            if (i > 1) {
                page.setPageNo(i);
                refundList = listRefundByUpdateTimeRange(startTime, endTime, page);
                if (CollectionUtils.isEmpty(refundList)) {
                    log.info("没有增量退款单，提前结束");
                    break;
                }
            }

            for (BoxRefund refund : refundList) {
                String refundSn = refund.getRefundSn();
                try {
                    String orderId = refund.getOrderId();
                    if (StringUtils.isBlank(orderId)) {
                        log.info("退款单:[{}],订单ID为空，无法处理", refundSn);
                        continue;
                    }
                    Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>().eq(Order::getId, orderId));
                    if (order == null) {
                        log.info("退款单:[{}],订单不存在，无法处理", refundSn);
                        continue;
                    }
                    CustomerDetails user = customerDetailsService.getById(order.getCustomerId());
                    String unionid = user.getUnionid();

                    CActivityUpdateTaskReq req = new CActivityUpdateTaskReq();
                    req.setType(2);
                    req.setBoxNo(order.getBoxSn());
                    req.setUnionId(unionid);
                    log.info("退款单:[{}],触发任务更新，入参:{}", refundSn, JSON.toJSONString(req));
                    activityService.updateTask(req);
                    log.info("退款单:[{}],触发任务更新，完成", refundSn);
                } catch (Exception e) {
                    log.error("退款单:[{}],触发任务更新异常", refundSn, e);
                }
            }
        }
    }

    @Override
    public List<String> listOrderIdByBoxSn(String boxSn) {
        log.info("查询订单ID列表 入参:{}", boxSn);
        // 获取支付成功的订单
        LambdaQueryWrapper<Order> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.select(Order::getId);
        orderQuery.eq(Order::getBoxSn, boxSn);
        orderQuery.in(Order::getOrderStatus, OrderStatusEnum.paidCodeList());
        List<Order> orders = orderMapper.selectList(orderQuery);
        log.info("查询订单ID列表 回参:{}", JSON.toJSONString(orders));
        return Optional.ofNullable(orders).orElse(Lists.newArrayList()).stream().map(Order::getId)
                .distinct().collect(Collectors.toList());
    }

    // @Override
    //    public List<OrderDto> listOrderDtoByCondition(OrderReq orderReq) {
    //        log.info("获取DD单列表和实付金额 请求参数:{}", JSON.toJSONString(orderReq));
    //        orderReq.check();
    //        // 拉取BOX下所有订单
    //        LambdaQueryWrapper<Order> orderQuery = new LambdaQueryWrapper<>();
    //        orderQuery.eq(Order::getBoxSn, orderReq.getBoxSn());
    //        orderQuery.between(Order::getCreateTime, orderReq.getCreateStartTime(), orderReq.getCreateEndTime());
    //        orderQuery.in(Order::getOrderStatus, OrderStatusEnum.paidCodeList());
    //        List<Order> orders = orderMapper.selectList(orderQuery);
    //        log.info("该BOX服务单下的所有DD单列表:{}", JSON.toJSONString(orders));
    //
    //        Map<String, List<BoxRefund>> orderId2RefundMap = new HashMap<>();
    //        List<OrderDto> orderDtoList = Lists.newArrayList();
    //
    //        if (CollectionUtils.isEmpty(orders)) {
    //            return orderDtoList;
    //        }
    //        List<String> orderIdList = orders.stream().map(Order::getId).collect(Collectors.toList());
    //        LambdaQueryWrapper<BoxRefund> refundQuery = new LambdaQueryWrapper<>();
    //        refundQuery.in(BoxRefund::getOrderId, orderIdList);
    //        refundQuery.eq(BoxRefund::getStatus, BoxRefundStatusEnum.REFUNDSUCCESS.getCode());
    //        List<BoxRefund> refunds = boxRefundMapper.selectList(refundQuery);
    //        log.info("该BOX服务单下的所有退款单列表:{}", JSON.toJSONString(refunds));
    //
    //        if (CollectionUtils.isNotEmpty(refunds)) {
    //            orderId2RefundMap = refunds.stream().collect(Collectors.groupingBy(BoxRefund::getOrderId));
    //        }
    //
    //        for (Order order : orders) {
    //            OrderDto orderDto = new OrderDto();
    //            orderDto.setOrderSn(order.getOrderSn());
    //            orderDto.setPaidAmount(order.getPaidAmount());
    //            orderDto.setBalanceAmt(order.getBalanceAmt());
    //            BigDecimal paidAmount = order.getPaidAmount().add(order.getBalanceAmt());
    //            orderDto.setPayment(paidAmount);
    //
    //            List<BoxRefund> boxRefunds = orderId2RefundMap.get(order.getId());
    //            if (CollectionUtils.isEmpty(boxRefunds)) {
    //                log.info("当前订单:[{}],实付金额[{}],不存在退款单，不处理退款金额", order.getOrderSn(), orderDto.getPayment());
    //                orderDtoList.add(orderDto);
    //                continue;
    //            }
    //            log.info("当前订单:[{}],计算退款金额，当前订单存在退款单", order.getOrderSn());
    //            BigDecimal refundAmount = boxRefunds.stream().map(refund -> {
    //                if (refund.getRefundAmount() == null) {
    //                    refund.setRefundAmount(BigDecimal.ZERO);
    //                }
    //                if (refund.getRefundBalance() == null) {
    //                    refund.setRefundBalance(BigDecimal.ZERO);
    //                }
    //                return refund.getRefundAmount().add(refund.getRefundBalance());
    //            }).reduce(BigDecimal.ZERO, BigDecimal::add);
    //
    //            BigDecimal payment = paidAmount.subtract(refundAmount);
    //            log.info("当前订单:[{}],计算金额，付款金额:[{}], 退款金额[{}], 最终实付金额:[{}]", order.getOrderSn(), paidAmount, refundAmount, payment);
    //            if (payment.compareTo(BigDecimal.ZERO) <= 0) {
    //                log.info("当前订单:[{}],排除退款后实付金额等于0，直接过滤该订单", order.getOrderSn());
    //                continue;
    //            }
    //            orderDto.setPayment(payment);
    //            orderDtoList.add(orderDto);
    //        }
    //
    //        return orderDtoList;
    //    }
}

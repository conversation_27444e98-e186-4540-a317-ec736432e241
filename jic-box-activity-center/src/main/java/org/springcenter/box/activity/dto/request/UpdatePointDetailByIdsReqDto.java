package org.springcenter.box.activity.dto.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UpdatePointDetailByIdsReqDto {

    @ApiModelProperty(value = "详细信息的ids")
    @NotEmpty(message = "详细信息的ids不能为空")
    private List<String> ids;


    @ApiModelProperty(value = "结束时间（yyyy-MM-dd HH:mm:ss）")
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


    @ApiModelProperty(value = "修改人")
    @NotBlank(message = "修改人不能为空")
    private String updateBy;

}

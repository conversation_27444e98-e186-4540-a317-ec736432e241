package org.springcenter.box.activity.service.crowd;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.domain.box.BOrderPromotion;
import org.springcenter.box.activity.domain.box.Box;
import org.springcenter.box.activity.mapper.box.BOrderPromotionMapper;
import org.springcenter.box.activity.service.IProductHttpService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 线下满赠过滤器
 */
@Slf4j
@Service
@Primary
public class OfflineActivityFilter implements CrowdFilterHandler {
    @Resource
    private BOrderPromotionMapper bOrderPromotionMapper;

    CrowdFilterHandler next = null;

    @Override
    public String filterName() {
        return "线下满赠";
    }

    @Override
    public CrowdFilterHandler getNext() {
        return next;
    }
    @Override
    public void setNext(CrowdFilterHandler next) {
        this.next = next;
    }

    @Override
    public boolean filter(CrowdFilterContext context) {
        List<String> orderIds = context.getOrderIds();
        if (CollectionUtils.isEmpty(orderIds)) {
            return true;
        }
        LambdaQueryWrapper<BOrderPromotion> query = new LambdaQueryWrapper<>();
        query.in(BOrderPromotion::getOrderId, orderIds);
        query.eq(BOrderPromotion::getDisType, 4);
        query.last("and rownum = 1");
        BOrderPromotion offlineActivity = bOrderPromotionMapper.selectOne(query);
        log.info("线下满赠活动查询 {}", JSON.toJSONString(offlineActivity));
        if (offlineActivity != null) {
            context.setErrorMsg("已参加线下满赠，无法再参加线上满赠");
            return false;
        }
        return true;
    }

    @Override
    public void startLog() {
        log.info("开始判断线下满赠");
    }

    @Override
    public void endLog() {
        log.info("结束判断线下满赠");
    }
}

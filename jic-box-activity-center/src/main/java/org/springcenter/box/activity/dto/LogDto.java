package org.springcenter.box.activity.dto;

import org.springcenter.box.activity.domain.box.CActivityGift;
import org.springcenter.box.activity.domain.box.CActivity;
import org.springcenter.box.activity.domain.box.CActivityJoinRecord;
import lombok.Data;

/**
 * 日志转换类
 */
@Data
public class LogDto {
    private CActivity activity;
    private CActivityGift gift;
    private CActivityJoinRecord joinRecord;

    public LogDto(CActivity activity, CActivityJoinRecord joinRecord, CActivityGift gift) {
        this.activity = activity;
        this.joinRecord = joinRecord;
        this.gift = gift;
    }

    public LogDto(CActivity activity) {
        this.activity = activity;
        this.joinRecord = null;
        this.gift = null;
    }

    public LogDto(CActivityJoinRecord joinRecord) {
        this.activity = null;
        this.joinRecord = joinRecord;
        this.gift = null;
    }

    public LogDto(CActivityGift gift) {
        this.activity = null;
        this.joinRecord = null;
        this.gift = gift;
    }
}

package org.springcenter.box.activity.convert;


import org.springcenter.box.activity.api.dto.JoinRecordInfoResp;
import org.springcenter.box.activity.dto.response.JoinRecordMiniAppGainResp;
import org.springcenter.box.activity.domain.box.CActivityJoinRecord;
import org.springcenter.box.activity.dto.response.ExportJoinRecordExcelDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = ConvertConfig.class)
public interface JoinRecordConvertor {
    JoinRecordConvertor INSTANCE = Mappers.getMapper(JoinRecordConvertor.class);

    @Mapping(source = "boxSn", target = "boxNo")
    JoinRecordInfoResp domain2Resp(CActivityJoinRecord domain);

    ExportJoinRecordExcelDto joinRecord2ExportDto(JoinRecordInfoResp joinRecord);

    JoinRecordMiniAppGainResp infoList2MiniAppGain(JoinRecordInfoResp joinRecordInfoResp);
}

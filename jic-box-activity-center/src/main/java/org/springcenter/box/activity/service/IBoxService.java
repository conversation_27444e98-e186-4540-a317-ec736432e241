package org.springcenter.box.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springcenter.box.activity.domain.box.Box;

import java.util.List;
import java.util.Map;

public interface IBoxService extends IService<Box> {

    Map<String, String> getBoxSn2BoxIdMap(List<String> boxSnList);

    /**
     * 查询未完成的服务单
     */
    List<Box> getUnFinishBoxList(String unionId);
}

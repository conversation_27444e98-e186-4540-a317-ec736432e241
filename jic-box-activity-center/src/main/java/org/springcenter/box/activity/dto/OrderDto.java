package org.springcenter.box.activity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付单转换类
 */
@Data
public class OrderDto {
    @ApiModelProperty(value = "订单ID")
    private String id;
    @ApiModelProperty(value = "订单编号")
    private String orderSn;
    @ApiModelProperty(value = "支付金额")
    private BigDecimal paidAmount;
    @ApiModelProperty(value = "储值卡金额")
    private BigDecimal balanceAmt;
    @ApiModelProperty(value = "商场代金券")
    private BigDecimal shopVouAmt;

    @ApiModelProperty(value = "有效实付:支付金额+储值卡金额-(退款金额+退储值卡金额)")
    private BigDecimal payment;
    
    @ApiModelProperty(value = "有效件数:实付金额大于0的商品计数")
    private Integer quantity;

    @ApiModelProperty(value = "过滤商品包后的符合条件的子单")
    private List<SubOrderDto> subOrders;
}

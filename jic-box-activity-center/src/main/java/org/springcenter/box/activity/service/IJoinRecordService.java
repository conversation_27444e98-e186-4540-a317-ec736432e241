package org.springcenter.box.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jnby.common.ResponseResult;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.api.dto.JoinRecordQueryReq;
import org.springcenter.box.activity.domain.box.Box;
import org.springcenter.box.activity.domain.box.CActivity;
import org.springcenter.box.activity.domain.box.CActivityGift;
import org.springcenter.box.activity.domain.box.CActivityJoinRecord;
import org.springcenter.box.activity.api.dto.JoinRecordInfoResp;
import org.springcenter.box.activity.api.dto.JoinRecordQueryResp;
import org.springcenter.box.activity.dto.request.*;
import org.springcenter.box.activity.enums.AccomplishStatusEnum;
import com.jnby.common.Page;

import java.math.BigDecimal;
import java.util.List;

public interface IJoinRecordService extends IService<CActivityJoinRecord> {

    List<JoinRecordInfoResp> listJoinRecord(JoinRecordListReq requestData, Page page);

    /**
     * 改地址
     * @param requestData
     */
    void joinRecordChangeAddress(JoinRecordChangeAddressReq requestData);

    /**
     * 手动状态
     * @param requestData
     */
    void joinRecordChangeStatus(JoinRecordChangeStatusReq requestData);

    /**
     * 人工新增
     * @param requestData
     */
    void joinRecordCreated(JoinRecordAddReq requestData);

    /**
     * 作废活动的参与记录
     */
    void joinRecordCancel(String activityId, String optPerson);

    JoinRecordQueryResp queryJoinRecordByBoxSn(JoinRecordQueryReq requestData, Boolean miniApp);

    /**
     * 导出参与记录，返回地址
     * @param req
     * @return
     */
    String exportJoinRecord(JoinRecordListReq req);

    /**
     * 构建参与记录
     */
    CActivityJoinRecord buildJoinRecord(AccomplishStatusEnum statusEnum, Box box, CActivityInfoResp activity,
                                        String orderNoList, BigDecimal payment, Integer quantity);

    /**
     * 发货
     */
    Boolean doDelivery(CActivity activity, CActivityGift gift);
    /**
     * 发货状态回查
     */
    void deliveryCallbackJob();

    /**
     * 导入兑换卡
     * @param req
     * @return
     */
    ResponseResult importExchangeGift(JoinRecordImportExcelReq req);
}

package org.springcenter.box.activity.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付状态枚举
 */
public enum PaymentStatusEnum {
    ready(0, "准备"),
    timeout(1, "超时"),
    invalid(2, "作废"),
    success(3, "成功"),
    failure(4, "失败"),
    cancel(5, "取消");


    private static final Map<Integer, PaymentStatusEnum> LOOKUP = new HashMap<>();

    static {
        for (PaymentStatusEnum s : EnumSet.allOf(PaymentStatusEnum.class)) {
            LOOKUP.put(s.getCode(), s);
        }
    }

    private Integer code;
    private String name;

    PaymentStatusEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }

    public static String get(Integer code) {
        PaymentStatusEnum paymentStatusEnum = LOOKUP.get(code);
        return paymentStatusEnum.getName();
    }
}

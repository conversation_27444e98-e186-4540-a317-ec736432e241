package org.springcenter.box.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.DateTime;
import org.springcenter.box.activity.config.IdConfig;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.config.redis.RedissonUtil;
import org.springcenter.box.activity.domain.box.*;
import org.springcenter.box.activity.mapper.box.BPointDetailMapper;
import org.springcenter.box.activity.mapper.box.BSubscribeInfoMapper;
import org.springcenter.box.activity.mapper.box.BUserPointAccountMapper;
import org.springcenter.box.activity.mapper.box.CustomerDetailsMapper;
import org.springcenter.box.activity.service.IBPointDetailService;
import org.springcenter.box.activity.service.ICombinePointDetailService;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CombinePointDetailServiceImpl implements ICombinePointDetailService {


    @Resource
    private BPointDetailMapper bPointDetailMapper;

    @Resource
    private BSubscribeInfoMapper bSubscribeInfoMapper;

    @Resource
    private RedissonUtil redissonUtil;

    @Resource
    private BUserPointAccountMapper bUserPointAccountMapper;

    @Resource
    private IdConfig idConfig;


    @Resource
    private IBPointDetailService ibPointDetailService;


    @Resource
    private CustomerDetailsMapper customerDetailsMapper;



    @Override
    public BigDecimal realTimePointByCustomerId(String customerId) {
        BigDecimal point = BigDecimal.ZERO;
        List<PointDetailWithSub> pointDetailWithSubList = getPointDetailWithSub(customerId);
        if (CollectionUtils.isEmpty(pointDetailWithSubList)) {
            return point;
        }

        for (int i = 0; i < pointDetailWithSubList.size(); i++) {
            point =point.add(pointDetailWithSubList.get(i).getPoint());
        }
        return point;
    }


    @Override
    public List<PointDetailWithSub> getPointDetailWithSub(String customerId) {
        List<PointDetailWithSub> pointDetailWithSubList = new ArrayList<>();

        List<BPointDetail> bPointDetailList = bPointDetailMapper.getListByCustomerId(customerId);
        if (CollectionUtils.isEmpty(bPointDetailList)) {
            return pointDetailWithSubList;
        }
        List<String> subIdSet = bPointDetailList.stream().map(BPointDetail::getSubId).distinct().collect(Collectors.toList());
        List<BSubscribeInfo> bSubscribeInfos = bSubscribeInfoMapper.selectInfosByIds(subIdSet);

        bSubscribeInfos.forEach(e -> {
            PointDetailWithSub pointDetailWithSub = new PointDetailWithSub();
            pointDetailWithSub.setSubInfo(e);
            pointDetailWithSub.setExpireTime(new DateTime(e.getEndTime()).plusDays(30).toDate());
            if (Long.valueOf(3).equals(e.getStatus())) {
                pointDetailWithSub.setExpireTime(new DateTime(e.getUnsubTime()).plusDays(30).toDate());
            }
            pointDetailWithSub.setPointDetailList(bPointDetailList.stream().filter(x -> x.getSubId().equals(e.getId())).collect(Collectors.toList()));
            pointDetailWithSub.setPoint(calPoint(pointDetailWithSub.getPointDetailList()));
            pointDetailWithSubList.add(pointDetailWithSub);
        });

        // 有序集合 返回
        return pointDetailWithSubList.stream().filter(e -> {
            return e.getExpireTime().after(new Date());
        }).sorted(Comparator.comparing(PointDetailWithSub::getExpireTime)).collect(Collectors.toList());
    }

    BigDecimal calPoint(List<BPointDetail> params) {
        BigDecimal result = BigDecimal.ZERO;

        for (int i = 0; i < params.size(); i++) {
            result = result.add(params.get(i).getPoint());
        }
        return result;
    }


    @Override
    public UserPointEntity rulesUserPoint(String customerId) {
        log.info("规则用户点数customerId:{}", customerId);
        String key = "rulesUserPoint:" + customerId;
        if (!redissonUtil.tryLock(key)) {
            throw new BoxActivityException("请勿重复提交");
        }
        UserPointEntity userPointEntity = new UserPointEntity();
        userPointEntity.setCustomerId(customerId);

        try {
            // 账户总点数 无周期性概念 可用即是全部
            BigDecimal canPoint = realTimePointByCustomerId(customerId);
            if (ObjectUtils.isEmpty(canPoint)) {
                canPoint = BigDecimal.ZERO;
            }

            BUserPointAccount bUserPointAccount = new BUserPointAccount();
            bUserPointAccount.setId(customerId);
            bUserPointAccount.setUpdateTime(new Date());
            bUserPointAccount.setTotalPoint(canPoint);
            bUserPointAccount.setCanUsePoint(canPoint);
            bUserPointAccountMapper.updateById(bUserPointAccount);

            userPointEntity.setTotalPoint(canPoint);
            userPointEntity.setCanPoint(canPoint);
        } finally {
            redissonUtil.unlock(key);
        }
        return userPointEntity;
    }


    @Override
    public FuturePointInDaysEntity getFuturePointInDays(String unionId, Integer days) {
        List<BPointDetail> bPointDetailList = bPointDetailMapper.getListByUnionIdAndTime(unionId, days);

        log.info("获取用户{}在{}天内的点数", unionId, days);
        FuturePointInDaysEntity futurePointInDaysEntity = new FuturePointInDaysEntity();
        futurePointInDaysEntity.setPoint(BigDecimal.ZERO);
        if(CollectionUtils.isEmpty(bPointDetailList)){
            return futurePointInDaysEntity;
        }
        // 再次查询 有效的订阅
        List<BSubscribeInfo> bSubscribeInfos = bSubscribeInfoMapper.selectInfosByIds(bPointDetailList.stream().map(BPointDetail::getSubId).distinct().collect(Collectors.toList()));

        List<WillExpirePointDetailWithSub> willExpirePointDetailWithSubs = new ArrayList<>();
        bSubscribeInfos.forEach(e -> {
            WillExpirePointDetailWithSub willExpirePointDetailWithSubTemp = new WillExpirePointDetailWithSub();
            willExpirePointDetailWithSubTemp.setSubscribeInfo(e);
            willExpirePointDetailWithSubTemp.setPointDetailList(bPointDetailList.stream().filter(x -> x.getSubId().equals(e.getId())).collect(Collectors.toList()));
            willExpirePointDetailWithSubTemp.setPoint(calPoint(willExpirePointDetailWithSubTemp.getPointDetailList()));
            willExpirePointDetailWithSubTemp.setExpireTime(willExpirePointDetailWithSubTemp.getPointDetailList().get(0).getExpireTime());
            willExpirePointDetailWithSubTemp.setSubId(e.getId());
            willExpirePointDetailWithSubs.add(willExpirePointDetailWithSubTemp);
        });

        WillExpirePointDetailWithSub willExpirePointDetailWithSubResult = willExpirePointDetailWithSubs.stream().sorted(Comparator.comparing(WillExpirePointDetailWithSub::getExpireTime)).findFirst().orElse(null);
        futurePointInDaysEntity.setPoint(willExpirePointDetailWithSubResult.getPoint());
        futurePointInDaysEntity.setExpireTime(willExpirePointDetailWithSubResult.getExpireTime());
        futurePointInDaysEntity.setSubId(willExpirePointDetailWithSubResult.getSubId());
        return futurePointInDaysEntity;
    }


    @Data
    public static class  WillExpirePointDetailWithSub {
        private BSubscribeInfo subscribeInfo;

        private List<BPointDetail> pointDetailList;

        private String subId;

        private BigDecimal point;

        private Date expireTime;
    }

    @Override
    @NewSpan
    public void realHandleSubIdAddRecord(String subId) {
        log.info("处理到期数据subId:{}", subId);
        String key = "realHandleSubIdAddRecord:" + subId;
        if (!redissonUtil.tryLock(key)) {
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            BigDecimal showPoint = bPointDetailMapper.getShowPointBySubId(subId);
            if (ObjectUtils.isEmpty(showPoint)) {
                log.info("没有找到对应的流水subId:{}", subId);
                return;
            }
            if (BigDecimal.ZERO.compareTo(showPoint) >= 0) {
                log.info("总值已经小于等于0 subId:{} 当前值:{}", subId, showPoint);
                return;
            }

            BPointDetail bPointDetailTemp = bPointDetailMapper.getListBySubId(subId).stream().findFirst().orElse(null);


            // 到期扣减数据
            BPointDetail kqPointDetail = new BPointDetail();
            kqPointDetail.setId(idConfig.getPointDetailId());
            kqPointDetail.setCustomerId(bPointDetailTemp.getCustomerId());
            kqPointDetail.setUnionId(bPointDetailTemp.getUnionId());
            kqPointDetail.setSubId(subId);
            kqPointDetail.setPoint(showPoint.negate());
            kqPointDetail.setType(3L);
            kqPointDetail.setCreateTime(new Date());
            kqPointDetail.setUpdateTime(new Date());
            kqPointDetail.setActiveTime(bPointDetailTemp.getActiveTime());
            kqPointDetail.setExpireTime(bPointDetailTemp.getExpireTime());
            ibPointDetailService.save(kqPointDetail);
        } finally {
            redissonUtil.unlock(key);
        }
    }
}

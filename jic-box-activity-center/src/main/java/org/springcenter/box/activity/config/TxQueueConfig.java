package org.springcenter.box.activity.config;
import com.alibaba.druid.pool.DruidDataSource;
import com.xz.spring.DefaultPersistentBusFactoryBean;
import com.xz.spring.autoconfig.PersistentBusProperties;
import org.killbill.bus.DefaultPersistentBus;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 1/27/21 6:49 PM
 */
@Configuration
public class TxQueueConfig {

    @Autowired
    BusProperties busProperties;

    @Primary
    @Bean("persistentBusProperties")
    public PersistentBusProperties persistentBusProperties(){
        PersistentBusProperties properties = new PersistentBusProperties();
        BeanUtils.copyProperties(busProperties, properties);
        return properties;
    }

    @Bean(value = "persistentBusFactoryBean", initMethod = "start", destroyMethod = "stop")
    public DefaultPersistentBusFactoryBean persistentBusFactoryBean(@Qualifier("persistentBusProperties") PersistentBusProperties properties, @Qualifier("boxDruidDataSource") DruidDataSource boxDruidDataSource) throws Exception {
        DefaultPersistentBusFactoryBean factoryBean = new DefaultPersistentBusFactoryBean();
        factoryBean.setDataSource(boxDruidDataSource);
        factoryBean.setConfigurationProperties(properties);
        return factoryBean;
    }

    @Bean
    public DefaultPersistentBus persistentBus(@Qualifier("persistentBusFactoryBean") DefaultPersistentBusFactoryBean persistentBusFactoryBean) throws Exception {
        return persistentBusFactoryBean.getObject();
    }
}

package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("更新赠品库存请求")
public class CActivityGiftStockReq {
    
    @NotBlank(message = "赠品ID不能为空")
    @ApiModelProperty(value = "赠品ID", required = true)
    private String giftId;

    @NotNull(message = "新增数量不能为空")
    @Min(value = 1, message = "新增数量必须大于0")
    @ApiModelProperty(value = "新增数量(正整数)", required = true)
    private Integer addNum;
    
    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空")
    private String optPerson;
} 
package org.springcenter.box.activity.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.box.activity.domain.box.BJstPushOrder;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2024-12-16 16:33:53
 * @Description: Mapper
 */
public interface BJstPushOrderMapper extends BaseMapper<BJstPushOrder> {


    List<BJstPushOrder> getListByOutId(@Param("outId") String outId);


    /**
     * 获取没有推送的单据
     * @return
     */
    List<BJstPushOrder> getNoPushList();


    /**
     * 获取需要拉取的物流信息
     * @return
     */
    List<BJstPushOrder> getNeedLogisticsList();
}

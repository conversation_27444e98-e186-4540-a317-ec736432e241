package org.springcenter.box.activity.enums;

import lombok.Getter;

/**
 * 赠品类型
 */
@Getter
public enum GiftTypeEnum {
    OUTER_ITEM(1, "外部实物"),
    INNER_ITEM(2, "内部实物"),
    EXCHANGE_GIFT(3, "兑换卡"),

    ;

    private final Integer code;
    private final String msg;

    GiftTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static boolean isExchangeGift(Integer giftType) {
        if (giftType == null) {
            return false;
        }
        return EXCHANGE_GIFT.getCode().equals(giftType);
    }
}

package org.springcenter.box.activity.enums;

import lombok.Getter;

/**
 * 操作日志类型
 */
@Getter
public enum OptTypeEnum {
    ACTIVITY_CREATE(1, "创建活动"),
    ACTIVITY_INVALID(2, "作废活动"),
    ACTIVITY_DOWN(3, "下架活动"),
    ACTIVITY_UPDATE_TASK(10, "付款/退款计算失败"),
    TASK_ACCOMPLISH(11, "任务达成"),
    TASK_BACK(12, "任务退回"),
    JOIN_RECORD_MANUAL_CREATE(20, "人工新增"),
    JOIN_RECORD_ALLOW_SEND(21, "可发"),
    JOIN_RECORD_NOT_ALLOW_SEND(22, "不可发"),
    JOIN_RECORD_UPDATE_ADDRESS(23, "修改地址"),
    JOIN_RECORD_SEND_TASK(24, "一键发货"),
    GIFT_STOCK_ADD(30, "赠品库存新增"),
    ;

    private final Integer code;
    private final String msg;

    OptTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}

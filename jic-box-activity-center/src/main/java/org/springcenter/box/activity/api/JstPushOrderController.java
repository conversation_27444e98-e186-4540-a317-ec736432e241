package org.springcenter.box.activity.api;


import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.domain.box.BJstPushOrder;
import org.springcenter.box.activity.dto.request.GetLogisticsReqDto;
import org.springcenter.box.activity.mapper.box.BJstPushOrderMapper;
import org.springcenter.box.activity.service.ICombineJstPushOrderService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RequestMapping("/jst/pushOrder")
@RestController
@Api(tags = "聚水潭单据相关")
@Slf4j
public class JstPushOrderController {
    @Resource
    private ICombineJstPushOrderService iCombineJstPushOrderService;

    @Resource
    private BJstPushOrderMapper bJstPushOrderMapper;


    @ApiOperation(value = "获取物流信息", notes = "获取物流信息")
    @PostMapping("/getLogistics")
    public ResponseResult<BJstPushOrder> getLogistics(@Validated @RequestBody CommonRequest<GetLogisticsReqDto> request){
        GetLogisticsReqDto getLogisticsReqDto = Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        return  ResponseResult.success(iCombineJstPushOrderService.getLogistics(getLogisticsReqDto.getOrderDetailId()));
    }

    @ResponseBody
    @GetMapping("/createJstPushOrder/{orderId}")
    @ApiOperation(value = "创建聚水潭推送单据(单据没有创建成的情况)(专有)")
    public ResponseResult createJstPushOrder(@PathVariable String orderId){
        iCombineJstPushOrderService.createJstPushOrder(orderId);
        return  ResponseResult.success();
    }




    @ResponseBody
    @GetMapping("/push/jstOrder/{id}")
    @ApiOperation(value = "聚水潭推送单据(专有)")
    public ResponseResult jstPushOrder(@PathVariable String id){
        iCombineJstPushOrderService.pushJstOrder(id);
        return  ResponseResult.success();
    }



    @ResponseBody
    @GetMapping("/fromJstBlankLogistics/{id}")
    @ApiOperation(value = "聚水潭拉取物流")
    public ResponseResult fromJstBlankLogistics(@PathVariable String id){
        BJstPushOrder bJstPushOrder = bJstPushOrderMapper.selectById(id);
        iCombineJstPushOrderService.fromJstBlankLogistics(Lists.newArrayList(bJstPushOrder));
        return  ResponseResult.success();
    }


}

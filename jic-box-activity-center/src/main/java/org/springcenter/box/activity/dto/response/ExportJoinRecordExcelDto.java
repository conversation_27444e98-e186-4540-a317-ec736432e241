package org.springcenter.box.activity.dto.response;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ExportJoinRecordExcelDto {
    @ExcelProperty(value = "BOX单号")
    private String boxNo;
    @ExcelProperty(value = "DD单号集合")
    private String orderNoList;
    @ExcelProperty(value = "实付金额")
    private BigDecimal payment;
    @ExcelProperty(value = "实付件数")
    private Integer quantity;
    @ExcelProperty(value = "兑换卡号")
    private String exchangeNumber;
    @ExcelProperty(value = "兑换卡密")
    private String exchangeSecret;
}

package org.springcenter.box.activity.event.handler;


import com.alibaba.fastjson.JSONObject;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.event.ModifySpuStockEvent;
import org.springcenter.box.activity.service.ICombinePointGoodsService;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ModifySpuStockHandler {


    @Resource
    private ICombinePointGoodsService iCombinePointGoodsService;


    @AllowConcurrentEvents
    @Subscribe
    public void processMyEvent(final ModifySpuStockEvent event) {
        log.info("处理spu是否有库存userToken:{} event:{}", event.getUserToken(), JSONObject.toJSONString(event));
        iCombinePointGoodsService.handleSpuStock(event.getSpuIdList());
    }
}

package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel("已获奖BOX单列表入参")
@Data
public class JoinRecordGainReq {
    @ApiModelProperty(value = "活动ID", required = true)
    @NotBlank(message = "活动ID不能为空")
    private String activityId;
    @ApiModelProperty(value = "导购/搭配师ID", required = true)
    @NotBlank(message = "搭配师ID不能为空")
    private String fashionerId;
}

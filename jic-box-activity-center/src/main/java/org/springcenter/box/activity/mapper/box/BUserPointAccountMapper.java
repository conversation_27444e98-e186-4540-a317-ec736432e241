package org.springcenter.box.activity.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.box.activity.domain.box.BUserPointAccount;

import java.math.BigDecimal;
import java.util.List;


/**
 * @Author: lwz
 * @Date: 2024-12-04 17:03:00
 * @Description: Mapper
 */
public interface BUserPointAccountMapper extends BaseMapper<BUserPointAccount> {

    List<BUserPointAccount>  getListByUnionIds(@Param("unionIds") List<String> unionIds);

    BUserPointAccount selectByCustomerId(@Param("customerId") String customerId);


    BUserPointAccount selectByUnionId(@Param("unionId") String unionId);

    List<BUserPointAccount>  getListByCustomerIds(@Param("customerIds") List<String> customerIds);


    int addPointById(@Param("id") String id, @Param("point") BigDecimal point);

    int subtractPointById(@Param("id") String id, @Param("point") BigDecimal point);


    List<BUserPointAccount> getAllUserAccount();


    /**
     * 获取超过 固定点数的账户列表
     * @param point
     * @return
     */
    List<BUserPointAccount> getListByOverPoint(@Param("point") BigDecimal point);
}

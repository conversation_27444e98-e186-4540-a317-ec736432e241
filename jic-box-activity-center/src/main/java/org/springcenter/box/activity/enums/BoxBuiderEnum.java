package org.springcenter.box.activity.enums;

import lombok.Getter;

/**
 * 搭盒人员枚举
 */
@Getter
public enum BoxBuiderEnum {
    FASHIONER(1, "搭配师"),
    GUIDER(2, "导购"),
    ;

    private final Integer code;
    private final String msg;

    BoxBuiderEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static boolean isFashioner(Integer code) {
        return FASHIONER.getCode().equals(code);
    }

    public static boolean isGuider(Integer code) {
        return GUIDER.getCode().equals(code);
    }
}

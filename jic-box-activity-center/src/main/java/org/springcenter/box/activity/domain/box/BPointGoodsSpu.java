package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: lwz
 * @Date: 2024-12-10 17:27:33
 * @Description: 
 */
@TableName("B_POINT_GOODS_SPU")
@ApiModel(value="BPointGoodsSpu对象", description="")
public class BPointGoodsSpu implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "spu的id")
    @TableId(value = "ID")
    private String id;

    @ApiModelProperty(value = "SPU_NO")
    @TableField("SPU_NO")
    private String spuNo;

    @TableField("IMG_URL")
    private String imgUrl;

    @ApiModelProperty(value = "商品名字")
    @TableField("GOODS_NAME")
    private String goodsName;


    @ApiModelProperty(value = "品牌id")
    @TableField("BRAND_ID")
    private String brandId;

    @ApiModelProperty(value = "品牌")
    @TableField("BRAND_NAME")
    private String brandName;


    @ApiModelProperty(value = "起始点数")
    @TableField("POINT")
    private BigDecimal point;


    @ApiModelProperty(value = "商品价格")
    @TableField("PRICE")
    private BigDecimal price;



    /**
     * 上下架(0下架 1上架)
     */
    @ApiModelProperty(value = "上下架(0下架 1上架)")
    @TableField("STATUS")
    private Long status;


    @ApiModelProperty(value = "是否有库存（0无 1有）")
    @TableField("HAS_STOCK")
    private Long hasStock;

    @TableField("CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField("DEL_FLAG")
    private Long delFlag;

    public BigDecimal getPoint() {
        return point;
    }

    public void setPoint(BigDecimal point) {
        this.point = point;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSpuNo() {
        return spuNo;
    }

    public void setSpuNo(String spuNo) {
        this.spuNo = spuNo;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Long delFlag) {
        this.delFlag = delFlag;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }


    public Long getHasStock() {
        return hasStock;
    }

    public void setHasStock(Long hasStock) {
        this.hasStock = hasStock;
    }

    @Override
    public String toString() {
        return "BPointGoodsSpu{" +
                "id='" + id + '\'' +
                ", spuNo='" + spuNo + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", brandId='" + brandId + '\'' +
                ", brandName='" + brandName + '\'' +
                ", point=" + point +
                ", price=" + price +
                ", status=" + status +
                ", hasStock=" + hasStock +
                ", createTime=" + createTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                '}';
    }
}

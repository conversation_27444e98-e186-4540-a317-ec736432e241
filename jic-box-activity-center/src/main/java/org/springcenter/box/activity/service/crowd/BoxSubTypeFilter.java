package org.springcenter.box.activity.service.crowd;

import org.springcenter.box.activity.enums.BoxSourceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 盒子订阅类型
 */
@Slf4j
@Service
public class BoxSubTypeFilter implements CrowdFilterHandler {
    CrowdFilterHandler next = null;

    @Override
    public String filterName() {
        return "盒子订阅类型";
    }

    @Override
    public CrowdFilterHandler getNext() {
        return next;
    }

    @Override
    public void setNext(CrowdFilterHandler next) {
        this.next = next;
    }

    @Override
    public boolean filter(CrowdFilterContext context) {
        Integer sourceType = context.getBox().getSourceType();
        if (sourceType == null) {
            log.info("盒子订阅类型为空，无法判断盒子订阅类型");
            context.setErrorMsg("盒子订阅类型为空，无法判断盒子订阅类型");
            return false;
        }

        boolean single = BoxSourceTypeEnum.isSingle(sourceType);
        boolean sub = BoxSourceTypeEnum.isSub(sourceType);

        Boolean activityBoxTypeSingle = context.getActivityInfo().getBoxTypeSingle();
        Boolean activityBoxTypeSub = context.getActivityInfo().getBoxTypeSubscription();
        log.info("是否单次盒子[{}], 勾选单次盒子[{}], 是否订阅盒子[{}], 勾选订阅盒子[{}]",
                single, activityBoxTypeSingle, sub, activityBoxTypeSub);
        if (single && Boolean.TRUE.equals(activityBoxTypeSingle)) {
            log.info("符合单次盒子");
            return true;
        }
        if (sub && Boolean.TRUE.equals(activityBoxTypeSub)) {
            log.info("符合订阅盒子");
            return true;
        }
        context.setErrorMsg("盒子订阅类型不符合");
        return false;
    }

    @Override
    public void startLog() {
        log.info("开始判断过滤盒子订阅类型条件");
    }

    @Override
    public void endLog() {
        log.info("结束判断过滤盒子订阅类型条件");
    }
}

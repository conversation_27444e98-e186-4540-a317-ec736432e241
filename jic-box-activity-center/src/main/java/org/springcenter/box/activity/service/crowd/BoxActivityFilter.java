package org.springcenter.box.activity.service.crowd;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.domain.box.Box;
import org.springcenter.box.activity.domain.box.CActivityJoinRecord;
import org.springcenter.box.activity.mapper.box.CActivityJoinRecordMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 活动校验
 */
@Slf4j
@Service
public class BoxActivityFilter implements CrowdFilterHandler {
    @Resource
    private CActivityJoinRecordMapper cActivityJoinRecordMapper;

    CrowdFilterHandler next = null;

    @Override
    public String filterName() {
        return "活动校验";
    }

    @Override
    public CrowdFilterHandler getNext() {
        return next;
    }
    @Override
    public void setNext(CrowdFilterHandler next) {
        this.next = next;
    }

    @Override
    public boolean filter(CrowdFilterContext context) {
        Box box = context.getBox();
        CActivityInfoResp activity = context.getActivityInfo();

        LambdaQueryWrapper<CActivityJoinRecord> unionQuery = new LambdaQueryWrapper<>();
        unionQuery.eq(CActivityJoinRecord::getUnionId, box.getUnionid());
        unionQuery.eq(CActivityJoinRecord::getActivityId, activity.getId());
        unionQuery.ne(CActivityJoinRecord::getBoxSn, box.getBoxSn());
        unionQuery.eq(CActivityJoinRecord::getIsDelete, 0);
        unionQuery.last("and rownum = 1");
        CActivityJoinRecord unionRecord = cActivityJoinRecordMapper.selectOne(unionQuery);
        if (unionRecord != null) {
            context.setErrorMsg("该用户已存在BOX单参与了该活动,BOX单号:"+unionRecord.getBoxSn());
            return false;
        }

        if (activity.getGift() == null
                || activity.getGift().getRemainNum() == null
                || activity.getGift().getRemainNum() == 0) {
            context.setErrorMsg("库存不足");
            return false;
        }
        return true;
    }

    @Override
    public void startLog() {
    }

    @Override
    public void endLog() {
    }
}

package org.springcenter.box.activity.convert;


import org.springcenter.box.activity.domain.box.CActivity;
import org.springcenter.box.activity.domain.box.CActivityGift;
import org.springcenter.box.activity.dto.request.CActivityCreateReq;
import org.springcenter.box.activity.dto.request.CActivityGiftCreateReq;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.dto.response.CActivityListResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = ConvertConfig.class)
public interface CActivityConvertor {

    CActivityConvertor INSTANCE = Mappers.getMapper(CActivityConvertor.class);

    @Mapping(target = "startTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "endTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "evaluateTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "crowd", expression = "java(com.google.common.base.Joiner.on(\",\").join(requestData.getCrowd()))")
    CActivity req2Activity(CActivityCreateReq requestData, String id);

    CActivityGift req2Gift(CActivityGiftCreateReq gift, String id);

    @Mapping(target = "startTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "endTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "createTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(expression = "java(" +
            "org.springcenter.box.activity.enums.ActivityStatusEnum.getFrontStatusEnum(activity.getStatus(), activity.getStartTime(), activity.getEndTime(), activity.getEvaluateTime(), activity.getIsDelete()).getCode()" +
            ")", target = "status")
    CActivityListResp activity2Resp(CActivity activity);

    @Mapping(target = "id", source = "activity.id")
    @Mapping(target = "startTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "endTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "evaluateTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "createTime", dateFormat = "yyyy-MM-dd HH:mm:ss", source = "activity.createTime")
    @Mapping(expression = "java(" +
            "org.springcenter.box.activity.enums.ActivityStatusEnum.getFrontStatusEnum(activity.getStatus(), activity.getStartTime(), activity.getEndTime(), activity.getEvaluateTime(), activity.getIsDelete()).getCode()" +
            ")", target = "status")
    @Mapping(expression = "java(gift2GiftResp(cActivityGift))", target = "gift")
    @Mapping(expression = "java(com.google.common.base.Splitter.on(\",\").splitToList(activity.getCrowd()).stream().map(java.lang.Integer::new).collect(java.util.stream.Collectors.toList()))", target = "crowd")
    CActivityInfoResp activity2InfoResp(CActivity activity, CActivityGift cActivityGift);

    CActivityInfoResp.CActivityGift gift2GiftResp(CActivityGift cActivityGift);
}

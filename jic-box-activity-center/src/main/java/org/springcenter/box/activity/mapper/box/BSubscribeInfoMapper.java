package org.springcenter.box.activity.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import org.apache.ibatis.annotations.Param;
import org.springcenter.box.activity.domain.box.BSubscribeInfo;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2023-09-07 16:55:46
 * @Description: Mapper
 */
public interface BSubscribeInfoMapper extends BaseMapper<BSubscribeInfo> {



    /**
     * 查询 进行中 和 已结束的 符合点数条件的订阅信息
     * @param unionId
     * @return
     */
    List<BSubscribeInfo>  selectPointSubInfoByUnionId(@Param("unionId") String unionId);


    /**
     * 查询 退订符合点数条件的订阅信息
     * @param unionId
     * @return
     */
    List<BSubscribeInfo>  selectPointUnSubInfoByUnionId(@Param("unionId") String unionId);


    /**
     * 查询指定时间的 订阅信息 (2024-01-01 之后)
     * @param unionId
     * @return
     */
    List<BSubscribeInfo>  selectPointSubInfoByUnionIdAndTime(@Param("unionId") String unionId);


    List<BSubscribeInfo> selectInfosByIds(@Param("ids") List<String> ids);

    /**
     * 获取用户有效的订阅周期记录
     */
    List<BSubscribeInfo> selectUserEffectSubInfo(@Param("userIds") List<String> userIds);

}

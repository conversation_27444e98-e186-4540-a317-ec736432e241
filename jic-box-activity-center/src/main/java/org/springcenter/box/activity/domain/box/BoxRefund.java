package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-10-08 17:35:47
 * @Description:
 */
@Data
@TableName("BOX_REFUND")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value = "BoxRefund对象", description = "")
public class BoxRefund implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "售后单号")
    @TableField("REFUND_SN")
    private String refundSn;
    @ApiModelProperty(value = "unionid")
    @TableField("UNIONID")
    private String unionid;
    @ApiModelProperty(value = "订单Id")
    @TableField("ORDER_ID")
    private String orderId;
    @ApiModelProperty(value = "售后状态 0 待寄回  1仓库待收货 2 待退款 3 已成功退款 4 申请驳回  5 取消退款    6 退款中 （联域状态）")
    @TableField("STATUS")
    private Long status;
    @ApiModelProperty(value = "退款说明")
    @TableField("MEMO")
    private String memo;
    @ApiModelProperty(value = "快递单号 ")
    @TableField("TRACKING_NUMBER")
    private String trackingNumber;
    @ApiModelProperty(value = "退款金额")
    @TableField("REFUND_AMOUNT")
    private BigDecimal refundAmount;
    @ApiModelProperty(value = "收货寄货ID")
    @TableField("SEND_LOGISTICS_ID")
    private String sendLogisticsId;
    @ApiModelProperty(value = "取件日期")
    @TableField("GET_DATE")
    private String getDate;
    @ApiModelProperty(value = "是否已寄回   0 否  1 是 ")
    @TableField("SEND_BACK")
    private Long sendBack;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "售后原因")
    @TableField("REFUND_REMARK")
    private String refundRemark;
    @ApiModelProperty(value = "凭证照片")
    @TableField("REFUND_PHOTOS")
    private String refundPhotos;
    @ApiModelProperty(value = "是否自动计算金额   0 否  1 是 ")
    @TableField("AUTO_AMOUNT")
    private Long autoAmount;
    @ApiModelProperty(value = "微盟售后单号")
    @TableField("WEIMO_REFUND_ID")
    private String weimoRefundId;
    @ApiModelProperty(value = "储值卡退款金额")
    @TableField("REFUND_BALANCE")
    private BigDecimal refundBalance;
}

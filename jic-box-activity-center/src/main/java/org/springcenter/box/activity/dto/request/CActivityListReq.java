package org.springcenter.box.activity.dto.request;

import org.springcenter.box.activity.api.dto.BoxBuilderReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("活动列表查询入参")
@Data
public class CActivityListReq {
    @ApiModelProperty(value = "活动名称:模糊查询")
    private String activityNameLike;
    @ApiModelProperty(value = "活动开始时间(产品砍掉了暂时不需要)")
    private String startTime;
    @ApiModelProperty(value = "活动结束时间(产品砍掉了暂时不需要)")
    private String endTime;
    @ApiModelProperty(value = "活动状态：0=全部,1=未开始,2=进行中,3=核算中,4=待发放,5=已结束,6=已下架", required = true)
    private Integer activityStatus;
    @ApiModelProperty(value = "搭盒人员(后台接口不传)")
    private BoxBuilderReq boxBuilderReq;
    @ApiModelProperty(value = "过滤0库存的活动。默认false,需要过滤=true")
    private boolean filterZeroStock = false;
    @ApiModelProperty(value = "是否管理员请求。默认false")
    private boolean adminRequest = false;
}

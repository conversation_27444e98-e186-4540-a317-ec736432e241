package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AddGoodsReqDto {
    @ApiModelProperty(value = "spuId")
    @NotBlank(message = "skuId不能为空")
    private String spuId;

    @ApiModelProperty(value = "spuNo")
    @NotBlank(message = "spuNo不能为空")
    private String spuNo;

    @ApiModelProperty(value = "商品名字")
    @NotBlank(message = "spuNo不能为空")
    private String goodsName;

    @ApiModelProperty(value = "图片")
    @NotBlank(message = "图片不能为空")
    private String imgUrl;

    @ApiModelProperty(value = "创建人")
    @NotBlank(message = "创建人不能为空")
    private String createBy;


    @NotEmpty(message = "skulist不能为空")
    @ApiModelProperty(value = "新增商品skulist")
    private List<AddSku> skuList;

    @ApiModelProperty(value = "spu价格")
    @NotNull(message = "spu价格不能为空")
    private BigDecimal price;

    @ApiModelProperty(value = "品牌id")
    @NotBlank(message = "品牌id不能为空")
    private String brandId;

    @ApiModelProperty(value = "品牌")
    @NotBlank(message = "品牌不能为空")
    private String brandName;

    @ApiModelProperty(value = "上下架(0下架 1上架)")
    @NotNull(message = "上下架不能为空")
    private Long status;




    @Data
    public static class AddSku {
        @ApiModelProperty(value = "skuId")
        @NotBlank(message = "skuId不能为空")
        private String skuId;

        @ApiModelProperty(value = "SKU_NO")
        private String skuNo;

        @ApiModelProperty(value = "图片")
        @NotBlank(message = "图片不能为空")
        private String imgUrl;


        @ApiModelProperty(value = "库存")
        @NotNull(message = "库存不能为空")
        private Long stock;


        @ApiModelProperty(value = "点数")
        @NotNull(message = "点数不能为空")
        private BigDecimal point;

        @ApiModelProperty(value = "spuId")
        @NotBlank(message = "spuId不能为空")
        private String spuId;


        @ApiModelProperty(value = "spuNo")
        @NotBlank(message = "spuNo不能为空")
        private String spuNo;

        @ApiModelProperty(value = "价格")
        @NotNull(message = "价格不能为空")
        private BigDecimal price;



        @ApiModelProperty(value = "颜色")
        @NotBlank(message = "颜色不能为空")
        private String colorName;


        @ApiModelProperty(value = "颜色")
        @NotBlank(message = "颜色不能为空")
        private String colorNo;


        @ApiModelProperty(value = "规格")
        @NotBlank(message = "规格不能为空")
        private String sizeName;

        @ApiModelProperty(value = "规格")
        @NotBlank(message = "规格不能为空")
        private String sizeNo;

    }


}

package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel("活动修改入参")
@Data
public class CActivityUpdateReq {
    @ApiModelProperty(value = "活动ID", required = true)
    @NotBlank(message = "活动ID不能为空")
    private String activityId;
    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空")
    private String optPerson;
}

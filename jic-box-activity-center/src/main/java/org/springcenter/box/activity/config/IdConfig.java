package org.springcenter.box.activity.config;

import org.springcenter.box.activity.config.exception.BoxActivityException;
import com.jnby.common.util.IdLeaf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@RefreshScope
@Component
@Slf4j
public class IdConfig {

    @Value("${distributedId_CActivity}")
    private String activityId;

    @Value("${distributedId_CActivityGift}")
    private String giftId;

    @Value("${distributedId_CActivityJoinRecord}")
    private String joinRecordId;

    @Value("${distributedId_CActivityOptLog}")
    private String logId;


    private String pointDetailId = "B_POINT_DETAIL";


    private String userPointOrder = "B_USER_POINT_ORDER";


    private String userPointOrderDetail = "B_USER_POINT_ORDER_DETAIL";

    private String jstPushOrder="B_JST_PUSH_ORDER";


    private String logisticsSnapshot ="B_LOGISTICS_SNAPSHOT";


    private String getDateId(String tag) {
        try {
            String dateId = IdLeaf.getDateId(tag);
            if (StringUtils.isBlank(dateId)) {
                throw new BoxActivityException("ID生成异常:生成ID为空");
            }
            if (dateId.length() > 38) {
                throw new BoxActivityException("ID生成异常:长度超长");
            }
            return dateId;
        } catch (Exception e) {
            log.error("ID服务异常", e);
            throw new BoxActivityException("ID服务异常");
        }
    }

    public String getActivityId() {
        return getDateId(activityId);
    }

    public String getGiftId() {
        return getDateId(giftId);
    }

    public String getJoinRecordId() {
        return getDateId(joinRecordId);
    }

    public String getLogId() {
        return getDateId(logId);
    }


    public String getPointDetailId(){
        return getDateId(pointDetailId);
    }

    public String getUserPointOrderId(){
        return getDateId(userPointOrder) ;
    }

    public String getUserPointOrderDetailId(){
        return getDateId(userPointOrderDetail) ;
    }



    public String getLogisticsSnapshotId(){
        return getDateId(logisticsSnapshot);
    }


    public String getJstPushOrderId(){
        return getDateId(jstPushOrder);
    }
}

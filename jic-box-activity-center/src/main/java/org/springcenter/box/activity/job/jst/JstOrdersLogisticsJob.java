package org.springcenter.box.activity.job.jst;

import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.config.trace.XxlJobTaskLog;
import org.springcenter.box.activity.service.ICombineJstPushOrderService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class JstOrdersLogisticsJob extends IJobHandler {

    @Resource
    private ICombineJstPushOrderService iCombineJstPushOrderService;

    /**
     * 拉取聚水潭物流
     * @throws Exception
     */
    @Override
    @XxlJob("JstOrdersLogisticsJob")
    public void execute() throws Exception {
        XxlJobTaskLog.traceLog("拉取聚水潭物流开始");
        iCombineJstPushOrderService.fromJstBlankLogisticsList();
        XxlJobTaskLog.traceLog("拉取聚水潭物流结束");
    }
}

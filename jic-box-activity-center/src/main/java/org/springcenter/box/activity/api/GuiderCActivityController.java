package org.springcenter.box.activity.api;

import com.alibaba.fastjson.JSON;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springcenter.box.activity.api.dto.*;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.config.redis.RedissonConfig;
import org.springcenter.box.activity.config.redis.RedissonUtil;
import org.springcenter.box.activity.dto.request.*;
import org.springcenter.box.activity.dto.response.JoinRecordListResp;
import org.springcenter.box.activity.enums.AccomplishStatusEnum;
import org.springcenter.box.activity.service.IBoxService;
import org.springcenter.box.activity.service.ICActivityService;
import org.springcenter.box.activity.service.ICustomerDetailsService;
import org.springcenter.box.activity.service.IJoinRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequestMapping("/guider/c/activity")
@RestController
@Api(tags = "导购/搭配师-满赠活动")
@Slf4j
public class GuiderCActivityController {

    @Resource
    private ICActivityService icActivityService;

    @Resource
    private IJoinRecordService joinRecordService;
    @Resource
    private IBoxService boxService;
    @Resource
    private ICustomerDetailsService customerDetailsService;

    @Autowired
    private RedissonUtil redissonUtil;

    @ApiOperation(value = "活动列表", notes = "默认根据创建时间倒序，支持条件：活动状态")
    @PostMapping("/list")
    public ResponseResult<List<CActivityInfoResp>> activityList(@RequestBody CommonRequest<CActivityListReq> request) {
        CActivityListReq requestData = request.getRequestData();
        log.info("导购/搭配师查询活动列表,入参:{}", JSON.toJSONString(requestData));
        List<CActivityInfoResp> rspList = icActivityService.activityList(requestData, request.getPage());
        log.info("导购/搭配师查询活动列表,总数[{}]条,回参:{}", request.getPage().getCount(), rspList);
        return ResponseResult.success(rspList, request.getPage());
    }

    @ApiOperation(value = "活动详情", notes = "返回活动详情")
    @PostMapping("/info")
    public ResponseResult<CActivityInfoResp> activityInfo(@Validated @RequestBody CommonRequest<CActivityIdReq> request) {
        CActivityIdReq requestData = request.getRequestData();
        log.info("导购/搭配师查询活动详情,入参:{}", JSON.toJSONString(requestData));
        CActivityInfoResp cActivityInfoResp = icActivityService.activityInfo(requestData.getActivityId(), false);
        log.info("导购/搭配师查询活动详情,回参:{}", JSON.toJSONString(cActivityInfoResp));
        return ResponseResult.success(cActivityInfoResp);
    }

    @ApiOperation(value = "库存检查", notes = "有库存则返true，没有返回false")
    @PostMapping("/checkInventory")
    public ResponseResult<Boolean> checkInventory(@Validated @RequestBody CommonRequest<CActivityIdReq> request) {
        CActivityIdReq requestData = request.getRequestData();
        log.info("库存检查 入参:{}", JSON.toJSONString(requestData));
        Boolean hasInventory = icActivityService.checkInventory(requestData.getActivityId());
        log.info("库存检查 回参:{}", hasInventory);
        return ResponseResult.success(true);
    }

    @ApiOperation(value = "有效活动", notes = "根据实付金额计算符合条件的有效活动")
    @PostMapping("/effect")
    public ResponseResult<List<CActivityInfoResp>> effectActivity(@Validated @RequestBody CommonRequest<CActivityEffectReq> request) {
        CActivityEffectReq requestData = request.getRequestData();
        log.info("有效活动 入参:{}", JSON.toJSONString(requestData));
        requestData.check();
        List<CActivityInfoResp> respList = icActivityService.effectActivity(requestData);
        log.info("有效活动 回参:{}", JSON.toJSONString(respList));
        return ResponseResult.success(respList);
    }

    @ApiOperation(value = "任务计算", notes = "选定一个活动，计算活动的达成情况")
    @PostMapping("/updateTask")
    public ResponseResult<Boolean> updateTask(@RequestBody CommonRequest<CActivityUpdateTaskReq> request) {
        CActivityUpdateTaskReq requestData = request.getRequestData();
        log.info("任务计算 入参:{}", JSON.toJSONString(requestData));
        requestData.check();
        // 加锁
        String key = RedissonConfig.getLockKeyUpdateTask(requestData.getBoxNo());
        if (!redissonUtil.tryLock(key)) {
            log.info("任务计算 获取锁[{}]失败", key);
            throw new BoxActivityException("正在执行任务计算，请勿重复提交");
        }
        try {
            icActivityService.updateTask(requestData);
            log.info("任务计算 完成");
        } catch (Exception e) {
            log.error("任务计算ERROR", e);
            throw e;
        } finally {
            log.info("任务计算 释放锁[{}]", key);
            redissonUtil.unlock(key);
        }
        return ResponseResult.success(true);
    }

    @ApiOperation(value = "已获奖的BOX单")
    @PostMapping("/join/record/gain")
    public ResponseResult<List<JoinRecordListResp>> joinRecordGain(@Validated @RequestBody CommonRequest<JoinRecordGainReq> request) {
        // 获取活动所有的达成BOX单号，然后过滤导购ID（或者保存的时候得存一下BOX单的搭盒人员ID）
        JoinRecordGainReq requestData = request.getRequestData();
        log.info("已获奖的BOX单列表查询,入参:{}", JSON.toJSONString(requestData));
        JoinRecordListReq req = new JoinRecordListReq();
        req.setActivityId(requestData.getActivityId());
        req.setFashionerId(requestData.getFashionerId());
        req.setAccomplishStatus(AccomplishStatusEnum.ACCOMPLISH.getCode());
        List<JoinRecordInfoResp> joinRecordInfoResps = joinRecordService.listJoinRecord(req, request.getPage());
        if (CollectionUtils.isEmpty(joinRecordInfoResps)) {
            return ResponseResult.success(new ArrayList<>(), request.getPage());
        }

        // 根据boxSn获取所有的boxId
        List<String> boxSnList = joinRecordInfoResps.stream().map(JoinRecordInfoResp::getBoxNo).collect(Collectors.toList());
        Map<String, String> boxSn2BoxIdMap = boxService.getBoxSn2BoxIdMap(boxSnList);

        // 根据unionId获取customerId
        List<String> unionIdList = joinRecordInfoResps.stream().map(JoinRecordInfoResp::getUnionId).collect(Collectors.toList());
        Map<String, String> unionId2CustomerIdMap = customerDetailsService.unionId2CustomerIdMap(unionIdList);

        List<JoinRecordListResp> rspList = new ArrayList<>();
        for (JoinRecordInfoResp joinRecordInfoResp : joinRecordInfoResps) {
            JoinRecordListResp rsp = new JoinRecordListResp();
            rspList.add(rsp);
            rsp.setId(joinRecordInfoResp.getId());
            rsp.setBoxNo(joinRecordInfoResp.getBoxNo());
            rsp.setUnionId(joinRecordInfoResp.getUnionId());
            rsp.setBoxId(boxSn2BoxIdMap.get(joinRecordInfoResp.getBoxNo()));
            rsp.setCustomerId(unionId2CustomerIdMap.get(joinRecordInfoResp.getUnionId()));
        }
        log.info("已获奖的BOX单列表查询,回参:{}", rspList);
        return ResponseResult.success(rspList, request.getPage());
    }

    @ApiOperation(value = "根据BOX单号查询满赠活动达成状态")
    @PostMapping("/join/record/query")
    public ResponseResult<JoinRecordQueryResp> queryJoinRecord(@Validated @RequestBody CommonRequest<JoinRecordQueryReq> request) {
        JoinRecordQueryReq requestData = request.getRequestData();
        log.info("根据BOX单号查询满赠活动达成状态,入参:{}", JSON.toJSONString(requestData));
        requestData.check();
        JoinRecordQueryResp rsp = joinRecordService.queryJoinRecordByBoxSn(requestData, false);
        log.info("根据BOX单号查询满赠活动达成状态,回参:{}", JSON.toJSONString(rsp));
        return ResponseResult.success(rsp);
    }

}

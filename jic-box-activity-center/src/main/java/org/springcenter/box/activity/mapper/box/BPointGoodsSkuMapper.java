package org.springcenter.box.activity.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.box.activity.domain.box.BPointGoodsSku;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2024-12-10 17:14:01
 * @Description: Mapper
 */
public interface BPointGoodsSkuMapper extends BaseMapper<BPointGoodsSku> {


    List<BPointGoodsSku> selectListBySpuId(@Param("spuId") String spuId);

    int subtractStockBySkuId(@Param("skuId") String skuId);


    List<String> getSpuIdListHasStock(@Param("spuIdList") List<String> spuIdList);
}

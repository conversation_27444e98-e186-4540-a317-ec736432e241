package org.springcenter.box.activity.job.subscribe;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springcenter.box.activity.config.IdConfig;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.config.trace.XxlJobTaskLog;
import org.springcenter.box.activity.domain.box.BPointDetail;
import org.springcenter.box.activity.mapper.box.BPointDetailMapper;
import org.springcenter.box.activity.service.IBPointDetailService;
import org.springcenter.box.activity.service.ICombinePointDetailService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 处理过期插入 过期记录
 * <AUTHOR>
 */
@Component
@Slf4j
public class ExpireAddRecordToDetailJob extends IJobHandler {

    @Resource
    private BPointDetailMapper bPointDetailMapper;


    @Resource
    private IdConfig idConfig;


    @Resource
    private IBPointDetailService ibPointDetailService;


    @Resource
    private ICombinePointDetailService iCombinePointDetailService;


    @Override
    @XxlJob("ExpireAddRecordToDetailJob")
    public void execute() throws Exception {
        XxlJobTaskLog.traceLog("处理过期插入过期记录开始");
        /**
         * 1. 筛选符合条件的订阅id（两天过期）
         * 2. 根据subId再次校验 如果已经出现过 或者 已经小于等于0，就处理
         * 3. 过期类型进去
         *
         * 注意点：
         * 防止并发，是否要加锁处理
         */
        handleAddExpireRecord();
        XxlJobTaskLog.traceLog("处理过期插入过期记录结束");

    }
    void handleAddExpireRecord() {
        int totalPage = 1;
        Page page = new Page(1, 500);

        List<String> result = new ArrayList<>();
        while (page.getPageNo() <= totalPage) {
            List<String> subIdLit = getNeedSubListInTwoDays(page);
            if (CollectionUtils.isEmpty(subIdLit)) {
                break;
            } else {
                result.addAll(subIdLit);
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
        }

        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        result.forEach(e->{
            try {
                iCombinePointDetailService.realHandleSubIdAddRecord(e);
            }catch (Exception x){
                log.error("处理过期流水异常 subId:{} msg:{}",x,x.getMessage());
            }

        });
    }


    public void handleAllHistoryAddExpireRecord() {
        log.info("处理所有过期的subId的到期流水-开始");
        int totalPage = 1;
        Page page = new Page(1, 500);

        List<String> result = new ArrayList<>();
        while (page.getPageNo() <= totalPage) {
            List<String> subIdLit = getAllExpireSubListInTwoDays(page);
            if (CollectionUtils.isEmpty(subIdLit)) {
                break;
            } else {
                result.addAll(subIdLit);
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
        }

        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        result.forEach(e->{
            try {
                iCombinePointDetailService.realHandleSubIdAddRecord(e);
            }catch (Exception x){
                log.error("处理过期流水异常 subId:{} msg:{}",x,x.getMessage());
            }

        });
        log.info("处理所有过期的subId的到期流水-结束");

    }


    /**
     * 获取两天内过期的subId 流水
     *
     * @param page
     * @return
     */
    public List<String> getNeedSubListInTwoDays(Page page) {
        com.github.pagehelper.Page<List<String>> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bPointDetailMapper.getExpireSubIdsInTwoDays();
        PageInfo<String> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }




    /**
     * 获取所有过期的subId 流水
     *
     * @param page
     * @return
     */
    public List<String> getAllExpireSubListInTwoDays(Page page) {
        com.github.pagehelper.Page<List<String>> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bPointDetailMapper.getAllExpireSubIds();
        PageInfo<String> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

}

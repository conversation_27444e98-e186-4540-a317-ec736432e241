package org.springcenter.box.activity.config;
import com.jnby.common.util.QiniuUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/19/21 3:08 PM
 */
@Configuration
public class QiniuConfig {

    @Value("${qiniu.ACCESS_KEY}")
    private String access_key;

    @Value("${qiniu.SECRET_KEY}")
    private String secret_key;

    @Value("${qiniu.bucketname}")
    private String bucket;

    @Bean(initMethod = "init")
    public QiniuUtil qiniuUtil(){
        QiniuUtil util = new QiniuUtil();
        util.setAccess_key(access_key);
        util.setBucket(bucket);
        util.setSecret_key(secret_key);
        return util;
    }
}

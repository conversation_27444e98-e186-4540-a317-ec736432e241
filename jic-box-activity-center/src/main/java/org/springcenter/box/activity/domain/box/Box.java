package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-09-30 14:21:43
 * @Description:
 */
@Data
@TableName("BOX")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value = "Box对象", description = "")
public class Box implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "box服务单编号")
    @TableField("BOX_SN")
    private String boxSn;
    @ApiModelProperty(value = "编号中的流水号")
    @TableField("SERIAL_NUMBER")
    private String serialNumber;
    @ApiModelProperty(value = "编号中日期")
    @TableField("DAYSTR")
    private String daystr;
    @ApiModelProperty(value = "顾客unionid")
    @TableField("UNIONID")
    private String unionid;
    @ApiModelProperty(value = "请求id")
    @TableField("ASK_ID")
    private String askId;
    @ApiModelProperty(value = "搭配id(多个以 | 隔开)")
    @TableField("MATCH_ID")
    private String matchId;
    @ApiModelProperty(value = "订单id")
    @TableField("ORDER_ID")
    private String orderId;
    @ApiModelProperty(value = "物流信息id")
    @TableField("LOGISTICS_ID")
    private String logisticsId;
    @ApiModelProperty(value = "状态(-1:待提交;0:未发货;1:已发货;2:已签收;3:已完成未入库;4:已评价,5.已完成已入库 6:已取消,7:系统作废,8:被合单后) 9:待还货 10：换货中")
    @TableField("STATUS")
    private Long status;
    @ApiModelProperty(value = "评分")
    @TableField("SCORE")
    private String score;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "创建理型师id")
    @TableField("CREATE_FAS_ID")
    private String createFasId;
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "运单号")
    @TableField("TRACK_NUMBER")
    private String trackNumber;
    @ApiModelProperty(value = "0未发送物流通知 1已发送物流通知")
    @TableField("HAS_LOGISTICS_NOTICE")
    private Long hasLogisticsNotice;
    @ApiModelProperty(value = "0未发送签收通知 1已发送签收通知")
    @TableField("HAS_RECEIVE_NOTICE")
    private Long hasReceiveNotice;
    @ApiModelProperty(value = "是否预警 0-否  1-还货预警 2-超时预警")
    @TableField("IS_WARN")
    private Long isWarn;
    @ApiModelProperty(value = "是否已发送预警信息  0-否 1-是")
    @TableField("IS_WARN_NOTICE")
    private Long isWarnNotice;
    @ApiModelProperty(value = "初次预警时间")
    @TableField("WARN_TIME")
    private Date warnTime;
    @ApiModelProperty(value = "是否评价 0-未评价 1-已评价")
    @TableField("IS_EVAL")
    private Long isEval;
    @ApiModelProperty(value = "外部订单id")
    @TableField("OUT_ID")
    private Float outId;
    @ApiModelProperty(value = "0-未读 1-已读")
    @TableField("IS_READ")
    private Long isRead;
    @ApiModelProperty(value = "库存方式（0：总仓 1：门店）")
    @TableField("STOCK_TYPE")
    private Long stockType;
    @ApiModelProperty(value = "是否有搭支付  0： 不是  1：是")
    @TableField("IS_YD")
    private Long isYd;
    @ApiModelProperty(value = "服务单类型 10：订阅服务  20：换货 30：补货 40：其他")
    @TableField("BOX_TYPE")
    private Long boxType;
    @ApiModelProperty(value = "顾客反馈留言")
    @TableField("CUSTOMER_MEMO")
    private String customerMemo;
    @ApiModelProperty(value = "计算单号")
    @TableField("CALC_BILLNO")
    private String calcBillno;
    @ApiModelProperty(value = "是否商场折扣")
    @TableField("IS_SHOPVIP")
    private Long isShopvip;
    @ApiModelProperty(value = "商场折扣")
    @TableField("SHOP_DISCOUNT")
    private BigDecimal shopDiscount;
    @ApiModelProperty(value = "服务单类型 10:搭配师服务单 20:导购服务单 30:有搭服务单 40:主题盒子服务单 50:先试后买")
    @TableField("TYPE")
    private Long type;
    @ApiModelProperty(value = "退回商品评价返回的券")
    @TableField("EVALUATION_COUPON")
    private String evaluationCoupon;
    @ApiModelProperty(value = "发货时间")
    @TableField("SEND_TIME")
    private Date sendTime;
    @ApiModelProperty(value = "签收时间")
    @TableField("SIGNIN_TIME")
    private Date signinTime;
    @ApiModelProperty(value = "评价时间")
    @TableField("EVAL_TIME")
    private Date evalTime;
    @ApiModelProperty(value = "完成时间")
    @TableField("FINISH_TIME")
    private Date finishTime;
    @ApiModelProperty(value = "购买时间")
    @TableField("BUY_TIME")
    private Date buyTime;
    @ApiModelProperty(value = "寄回时间")
    @TableField("RETURN_TIME")
    private Date returnTime;
    @ApiModelProperty(value = "大内淘单号")
    @TableField("EBYH_NO")
    private String ebyhNo;
    @ApiModelProperty(value = "用于订单列表红点展示  0未读 1已读")
    @TableField("READ")
    private Long read;
    @ApiModelProperty(value = "导购ID")
    @TableField("SALESREP_ID")
    private Long salesrepId;
    @ApiModelProperty(value = "搭配师")
    @TableField("FASHIONER")
    private String fashioner;
    @ApiModelProperty(value = "导购")
    @TableField("SALES")
    private String sales;
    @ApiModelProperty(value = "下单人：1搭配师，2导购，3顾客")
    @TableField("PLACLE_ORDER")
    private Long placleOrder;
    @ApiModelProperty(value = "衣袋模板id")
    @TableField("POCKET_TEL_ID")
    private String pocketTelId;
    @ApiModelProperty(value = "合并单号id")
    @TableField("MERGE_ID")
    private String mergeId;
    @ApiModelProperty(value = "取消原因")
    @TableField("CANCEL_REASON")
    private String cancelReason;
    @ApiModelProperty(value = "是否填写反馈(包括用户退回反馈和导购反馈) 0未填写，1已填写")
    @TableField("IF_FEEDBACK")
    private Integer ifFeedback;
    @ApiModelProperty(value = "导购评价")
    @TableField("SALES_EVAL")
    private String salesEval;
    @ApiModelProperty(value = "联系人 0搭配师 1导购")
    @TableField("CONTACTS")
    private Integer contacts;
    @ApiModelProperty(value = "上次联系时间")
    @TableField("LAST_CONTACT_TIME")
    private Date lastContactTime;
    @ApiModelProperty(value = "扩展字段")
    @TableField("EXTEND")
    private String extend;
    @ApiModelProperty(value = "条形码")
    @TableField("BRCODEURL")
    private String brcodeurl;
    @ApiModelProperty(value = "试穿结束时间")
    @TableField("TRY_OUT_TIME")
    private Date tryOutTime;
    @ApiModelProperty(value = "没有购买原因 1，搭配商品不喜欢 2，商场活动，门店更优惠 3，喵街买单更优惠 4.其他")
    @TableField("NO_PAY_REASON")
    private String noPayReason;
    @ApiModelProperty(value = "创建平台 1新平台 0老平台")
    @TableField("PLATFORM")
    private Integer platform;
    @ApiModelProperty(value = "指定业绩品牌")
    @TableField("EXPLICIT_BRAND")
    private String explicitBrand;
    @ApiModelProperty(value = "是否包含内淘 1是 0否")
    @TableField("IS_EB")
    private Integer isEb;
    @ApiModelProperty(value = "1 主题搭配")
    @TableField("SOURCE_CODE")
    private String sourceCode;
    @ApiModelProperty(value = "主题活动ids")
    @TableField("THEME_ACTIVITY_IDS")
    private String themeActivityIds;
    @ApiModelProperty(value = "伯俊表hr_employee的id")
    @TableField("HR_EMP_ID")
    private String hrEmpId;
    @ApiModelProperty(value = "伯俊表c_store的id")
    @TableField("C_STORE_ID")
    private String cStoreId;
    @ApiModelProperty(value = "0走oms  1走wms调拨")
    @TableField("IF_WMS")
    private Integer ifWms;
    @ApiModelProperty(value = "渠道id")
    @TableField("CHANNEL_ID")
    private String channelId;
    @ApiModelProperty(value = "是否虚拟发货：1是 0否")
    @TableField("IF_VIR_DELIVERY")
    private Integer ifVirDelivery;
    @ApiModelProperty(value = "商户小程序id")
    @TableField("APP_ID")
    private String appId;
    @ApiModelProperty(value = "是否手动结束售后 1是 0否")
    @TableField("CLOSE_REFUND")
    private Integer closeRefund;
    @ApiModelProperty(value = "创建来源id")
    @TableField("SOURCE_ID")
    private String sourceId;
    @ApiModelProperty(value = "来源类型： 0免费盒子  1订阅主动要盒 2单次主动要盒")
    @TableField("SOURCE_TYPE")
    private Integer sourceType;
    @ApiModelProperty(value = "信用订单id")
    @TableField("CREDIT_ID")
    private String creditId;
}

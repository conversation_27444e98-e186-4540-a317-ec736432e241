package org.springcenter.box.activity.api;



import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springcenter.box.activity.domain.box.BPointGoodsSku;
import org.springcenter.box.activity.domain.box.BPointGoodsSpu;
import org.springcenter.box.activity.domain.box.BUserPointOrderDetail;
import org.springcenter.box.activity.dto.request.*;
import org.springcenter.box.activity.mapper.box.BPointGoodsSpuMapper;
import org.springcenter.box.activity.mapper.box.PointGoods;
import org.springcenter.box.activity.service.IBPointGoodsSkuService;
import org.springcenter.box.activity.service.IBPointGoodsSpuService;
import org.springcenter.box.activity.service.ICombinePointGoodsService;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequestMapping("/point/goods")
@RestController
@Api(tags = "商品")
@Slf4j
public class PointGoodsController {

    @Resource
    private ICombinePointGoodsService iCombinePointGoodsService;

    @Resource
    private BPointGoodsSpuMapper bPointGoodsSpuMapper;

    @Resource
    private IBPointGoodsSkuService ibPointGoodsSkuService;

    @Resource
    private IBPointGoodsSpuService ibPointGoodsSpuService;


    @ApiOperation(value = "增加商品")
    @PostMapping("/addGoods")
    public ResponseResult addGoods(@Validated @RequestBody CommonRequest<AddGoodsReqDto> request) {
        AddGoodsReqDto addGoodsReqDto = Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        log.info("增加商品req:{}", JSONObject.toJSONString(request));
        List<BPointGoodsSpu> result = bPointGoodsSpuMapper.getListBySpuNo(addGoodsReqDto.getSpuNo());

        if(CollectionUtils.isNotEmpty(result)){
            throw new RuntimeException("商品已经存在");
        }
        iCombinePointGoodsService.batchAddList(addGoodsReqDto);
        return ResponseResult.success();
    }


    @ApiOperation(value = "校验spu存在(false不存在 true存在)")
    @PostMapping("/validateSpu")
    public ResponseResult<Boolean> validateSpu(@Validated @RequestBody CommonRequest<ValidateSpuReqDto> request) {
        ValidateSpuReqDto validateSpuReqDto = Preconditions.checkNotNull(request.getRequestData(), "入参不能为空");
        List<BPointGoodsSpu> result = bPointGoodsSpuMapper.getListBySpuNo(validateSpuReqDto.getSpuNo());
        return ResponseResult.success(CollectionUtils.isNotEmpty(result));
    }



    @ApiOperation(value = "修改商品")
    @PostMapping("/updateGoodsById")
    public ResponseResult updateGoodsById(@Validated @RequestBody CommonRequest<UpdateGoodsReqDto> request) {
        UpdateGoodsReqDto updateGoodsReqDto =  Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        log.info("修改商品.req:{}",JSONObject.toJSONString(request));
        iCombinePointGoodsService.batchUpdateList(updateGoodsReqDto);
        return ResponseResult.success();
    }

    @ApiOperation(value = "商品上下架")
    @PostMapping("/updateStatusById")
    public ResponseResult updateStatusById(@Validated @RequestBody CommonRequest<UpdateStatusReqDto> request) {
        UpdateStatusReqDto updateGoodsReqDto =  Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        log.info("商品上下架.req:{}",JSONObject.toJSONString(request));
        BPointGoodsSpu bPointGoodsSpu = new BPointGoodsSpu();
        bPointGoodsSpu.setId(updateGoodsReqDto.getSpuId());
        bPointGoodsSpu.setStatus(updateGoodsReqDto.getStatus());
        bPointGoodsSpu.setUpdateTime(new Date());
        bPointGoodsSpu.setUpdateBy(updateGoodsReqDto.getUpdateBy());
        ibPointGoodsSpuService.updateById(bPointGoodsSpu);
        return ResponseResult.success();
    }





    @ApiOperation(value = "spu商品列表")
    @PostMapping("/spuGoodsList")
    public ResponseResult<List<BPointGoodsSpu> > spuGoodsList(@Validated @RequestBody CommonRequest<SpuGoodsListReqDto> request) {
        SpuGoodsListReqDto spuGoodsListReqDto = Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        Page page = request.getPage();
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        iCombinePointGoodsService.spuGoodsList(spuGoodsListReqDto);
        PageInfo<BPointGoodsSpu> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return ResponseResult.success(pageInfo.getList(),page);
    }



    @ApiOperation(value = "spu商品列表根据unionId")
    @PostMapping("/spuGoodsListByUnionId")
    public ResponseResult<List<BPointGoodsSpu> > spuGoodsListByUnionId(@Validated @RequestBody CommonRequest<SpuGoodsListByUnionIdReqDto> request) {
        SpuGoodsListByUnionIdReqDto spuGoodsListByUnionIdReqDto = Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        Page page = request.getPage();
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        iCombinePointGoodsService.spuGoodsListByUnionId(spuGoodsListByUnionIdReqDto);
        PageInfo<BPointGoodsSpu> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return ResponseResult.success(pageInfo.getList(),page);
    }





    @ApiOperation(value = "商品详情")
    @PostMapping("/goodsInfo")
    public ResponseResult<PointGoods> goodsInfo(@Validated @RequestBody CommonRequest<GoodsInfoReqDto> commonRequest) {
        GoodsInfoReqDto goodsInfoReqDto = Preconditions.checkNotNull(commonRequest.getRequestData(), "入参不能为空");
        return ResponseResult.success(iCombinePointGoodsService.goodsInfo(goodsInfoReqDto.getSpuId()));
    }




}

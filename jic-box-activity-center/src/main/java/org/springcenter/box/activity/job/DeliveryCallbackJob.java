package org.springcenter.box.activity.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import org.springcenter.box.activity.config.trace.XxlJobTaskLog;
import org.springcenter.box.activity.service.IJoinRecordService;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 一键发货-状态更新JOB
 * 单机执行，半个小时一次
 */
@Component
@Slf4j
public class DeliveryCallbackJob extends IJobHandler {

    @Resource
    private IJoinRecordService joinRecordService;

//    @Scheduled(cron = "0 * * * * ?")
    @XxlJob("DeliveryCallbackJob")
    @Override
    public void execute() {
        XxlJobTaskLog.traceLog("一键发货状态回查JOB,本次调度开始");
        joinRecordService.deliveryCallbackJob();
        log.info("一键发货状态回查JOB,本次调度结束");
    }
}


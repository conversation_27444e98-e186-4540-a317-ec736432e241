package org.springcenter.box.activity.event.handler;

import com.alibaba.fastjson.JSONObject;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.domain.box.UserPointEntity;
import org.springcenter.box.activity.event.ModifySpuStockEvent;
import org.springcenter.box.activity.event.RulesUserPointEvent;
import org.springcenter.box.activity.service.ICombinePointDetailService;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RulesUserPointHandler {


    @Resource
    private ICombinePointDetailService iCombinePointDetailService;

    @AllowConcurrentEvents
    @Subscribe
    public void processMyEvent(final RulesUserPointEvent event) {
        log.info("规则用户点数customerId:{} userToken:{} event:{}", event.getCustomerId(), event.getUserToken(), JSONObject.toJSONString(event));
        UserPointEntity userPointEntity =  iCombinePointDetailService.rulesUserPoint(event.getCustomerId());
    }
}

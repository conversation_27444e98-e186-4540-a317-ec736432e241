package org.springcenter.box.activity.config.trace;

import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 * xxljob 工具类 同步打印远程log和本地系统log
 */
@Slf4j
public class XxlJobTaskLog {

    static final String TRACE_ID_NAME = "X-B3-TraceId";

    /**
     * 追踪日志
     *
     * @param appendLogPattern
     * @param appendLogArguments
     * @return
     */
    public static void traceLog(String appendLogPattern, Object... appendLogArguments) {
        log.info(appendLogPattern, appendLogArguments);
        XxlJobHelper.log("[" + MDC.get(TRACE_ID_NAME) + "] " + appendLogPattern, appendLogArguments);
    }


    /**
     * 追踪日志
     *
     * @param appendLogPattern
     * @param appendLogArguments
     */
    public static void traceErrorLog(String appendLogPattern, Object... appendLogArguments) {
        log.error(appendLogPattern, appendLogArguments);
        XxlJobHelper.log("[" + MDC.get(TRACE_ID_NAME) + "] " + appendLogPattern, appendLogArguments);
    }



}
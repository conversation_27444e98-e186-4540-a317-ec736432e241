package org.springcenter.box.activity.job.subscribe;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.DateTime;
import org.springcenter.box.activity.config.trace.XxlJobTaskLog;
import org.springcenter.box.activity.domain.box.BPointDetail;
import org.springcenter.box.activity.domain.box.BSubscribeInfo;
import org.springcenter.box.activity.mapper.box.BPointDetailMapper;
import org.springcenter.box.activity.mapper.box.BSubscribeInfoMapper;
import org.springcenter.box.activity.service.IBPointDetailService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PointDetailExpireTimeJob extends IJobHandler {

    @Resource
    private BPointDetailMapper bPointDetailMapper;

    @Resource
    private IBPointDetailService ibPointDetailService;

    @Resource
    private BSubscribeInfoMapper bSubscribeInfoMapper;




    /**
     * 处理流水中-过期时间
     * @throws Exception
     */
    @Override
    @XxlJob("PointDetailExpireTimeJob")
    public void execute() throws Exception {
        XxlJobTaskLog.traceLog("处理流水过期时间开始");
        handleExpireTime();
        XxlJobTaskLog.traceLog("处理流水过期时间结束");
    }


    void handleExpireTime() {
        int totalPage = 1;
        Page page = new Page(1, 500);

        List<BPointDetail> result = new ArrayList<>();
        while (page.getPageNo() <= totalPage) {
            List<BPointDetail> bPointDetailList = getNeedPointDetailList(page);
            if (CollectionUtils.isEmpty(bPointDetailList)) {
                break;
            } else {
                result.addAll(bPointDetailList);
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
        }

        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        List<String> subIds = result.stream().map(BPointDetail::getSubId).distinct().collect(Collectors.toList());
        Map<String, Date> realExpireTime = getRealExpireTime(subIds);
        List<BPointDetail> needUpdatePointList = new ArrayList<>();

        result.forEach(e -> {
            if (realExpireTime.containsKey(e.getSubId()) && !ObjectUtils.equals(e.getExpireTime(), realExpireTime.get(e.getSubId()))) {
                BPointDetail updatePointDetail = new BPointDetail();
                updatePointDetail.setId(e.getId());
                updatePointDetail.setUpdateTime(new Date());
                updatePointDetail.setExpireTime(realExpireTime.get(e.getSubId()));
                needUpdatePointList.add(updatePointDetail);
            }
        });

        if (CollectionUtils.isEmpty(needUpdatePointList)) {
            return;
        }
        List<List<BPointDetail>> parts = Lists.partition(needUpdatePointList, 800);
        parts.forEach(x -> {
            ibPointDetailService.updateBatchById(x);
        });
    }


    /**
     * 获取有效 流水
     *
     * @param page
     * @return
     */
    public List<BPointDetail> getNeedPointDetailList(Page page) {
        com.github.pagehelper.Page<BPointDetail> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bPointDetailMapper.getActiveSubTimeList();
        PageInfo<BPointDetail> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }


    /**
     * 获取真正的过期时间
     *
     * @param ids
     * @return
     */
    Map<String, Date> getRealExpireTime(List<String> ids) {
        Map<String, Date> result = new HashMap<>();
        List<List<String>> parts = Lists.partition(ids, 500);

        parts.forEach(x -> {
            List<BSubscribeInfo> bSubscribeInfos = bSubscribeInfoMapper.selectInfosByIds(x);
            bSubscribeInfos.forEach(e -> {
                result.put(e.getId(), new DateTime(e.getEndTime()).plusDays(30).toDate());
                if (Long.valueOf(3).equals(e.getStatus())) {
                    result.put(e.getId(), new DateTime(e.getUnsubTime()).plusDays(30).toDate());
                }
            });
        });
        return result;
    }
}

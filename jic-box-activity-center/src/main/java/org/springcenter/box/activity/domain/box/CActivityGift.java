package org.springcenter.box.activity.domain.box;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-09-23 14:23:03
 * @Description: 赠品
 */
@Data
@TableName("C_ACTIVITY_GIFT")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value = "CActivityGift对象", description = "赠品")
public class CActivityGift implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @TableField("IS_DELETE")
    private Integer isDelete;
    @TableField("CREATE_TIME")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @TableField("UPDATE_TIME")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    @ApiModelProperty(value = "赠品名称")
    @TableField("GIFT_NAME")
    private String giftName;
    @ApiModelProperty(value = "赠品类型：1=外部实物，2=内部实物，3=兑换卡")
    @TableField("GIFT_TYPE")
    private Integer giftType;
    @ApiModelProperty(value = "活动ID")
    @TableField("ACTIVITY_ID")
    private String activityId;
    @ApiModelProperty(value = "凭证ID：sku、券号等")
    @TableField("VOUCHER_ID")
    private String voucherId;
    @ApiModelProperty(value = "价值")
    @TableField("PRICE")
    private BigDecimal price;
    @ApiModelProperty(value = "总数")
    @TableField("TOTAL_NUM")
    private Long totalNum;
    @ApiModelProperty(value = "库存")
    @TableField("REMAIN_NUM")
    private Long remainNum;
    @ApiModelProperty(value = "图片地址：英文逗号分隔")
    @TableField("PIC_LIST")
    private String picList;
    @ApiModelProperty(value = "版本号")
    @TableField("VERSION_NUMBER")
    private Long versionNumber;
}

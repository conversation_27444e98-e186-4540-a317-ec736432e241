package org.springcenter.box.activity.domain.box;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springcenter.box.activity.enums.ActivityGiftSendTaskStatusEnum;
import org.springcenter.box.activity.enums.ActivityStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-09-23 14:21:57
 * @Description: 满赠活动
 */
@Data
@TableName("C_ACTIVITY")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value = "CActivity对象", description = "满赠活动")
public class CActivity implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @TableField("IS_DELETE")
    private Integer isDelete;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    @ApiModelProperty(value = "活动名称")
    @TableField("ACTIVITY_NAME")
    private String activityName;
    @ApiModelProperty(value = "活动开始时间")
    @TableField("START_TIME")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @ApiModelProperty(value = "活动结束时间")
    @TableField("END_TIME")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    @ApiModelProperty(value = "评估时间")
    @TableField("EVALUATE_TIME")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date evaluateTime;
    /**
     * {@link ActivityStatusEnum}
     */
    @ApiModelProperty(value = "状态：1=有效、2=作废/结束")
    @TableField("STATUS")
    private Integer status;
    @ApiModelProperty(value = "参与人群(多选逗号分割)：1=新客+非自提盒子，2=新客+自提盒子，3=老客+非自提盒子，4=老客+自提盒子")
    @TableField("CROWD")
    private String crowd;
    @ApiModelProperty(value = "搭盒人员-导购：1=勾选，0=不勾选")
    @TableField("BOX_BUILDER_GUIDE")
    private Boolean boxBuilderGuide;
    @ApiModelProperty(value = "搭盒人员-导购-门店信息")
    @TableField("BOX_BUILDER_GUIDE_STORE")
    private String boxBuilderGuideStore;
    @ApiModelProperty(value = "搭盒人员-搭配师：1=勾选，0=未勾选")
    @TableField("BOX_BUILDER_FASHIONER")
    private Boolean boxBuilderFashioner;
    @ApiModelProperty(value = "盒子类型-订阅盒子：1=勾选，0=未勾选")
    @TableField("BOX_TYPE_SUBSCRIPTION")
    private Boolean boxTypeSubscription;
    @ApiModelProperty(value = "盒子类型-单次盒子")
    @TableField("BOX_TYPE_SINGLE")
    private Boolean boxTypeSingle;
    @ApiModelProperty(value = "商品范围：1=所有，2=指定商品过滤且满足门槛，3=满足门槛且含指定商品")
    @TableField("BOX_BRAND_TYPE")
    private Integer boxBrandType;
    @ApiModelProperty(value = "指定盒子品牌")
    @TableField("BOX_BRAND_LIST")
    private String boxBrandList;
    @ApiModelProperty(value = "达成门槛：元")
    @TableField("THRESHOLD")
    private BigDecimal threshold;
    @ApiModelProperty(value = "创建人")
    @TableField("CREATE_PERSON")
    private String createPerson;
    /**
     * {@link ActivityGiftSendTaskStatusEnum}
     */
    @ApiModelProperty(value = "发放任务状态：0=待核算，1=待发放，2=发放中")
    @TableField("GIFT_SEND_TASK_STATUS")
    private Integer giftSendTaskStatus;

    @ApiModelProperty(value = "门槛单位：1=元，2=件")
    @TableField("UNIT")
    private Integer unit;
}

package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-11-19 17:24:40
 * @Description: 
 */
@Data
@TableName("B_ORDER_PROMOTION")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BOrderPromotion对象", description="")
public class BOrderPromotion implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "订单id")
    @TableField("ORDER_ID")
    private String orderId;
    @ApiModelProperty(value = "商品skuid")
    @TableField("SKU_ID")
    private BigDecimal skuId;
    @ApiModelProperty(value = "策略类型1单品 2组合 3整单")
    @TableField("PROMO_TYPE")
    private BigDecimal promoType;
    @ApiModelProperty(value = "策略id")
    @TableField("PROMO_ID")
    private BigDecimal promoId;
    @ApiModelProperty(value = "策略编号")
    @TableField("PROMO_NO")
    private String promoNo;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "优惠方式  1打折 2特价 3优惠 4满赠")
    @TableField("DIS_TYPE")
    private BigDecimal disType;
}

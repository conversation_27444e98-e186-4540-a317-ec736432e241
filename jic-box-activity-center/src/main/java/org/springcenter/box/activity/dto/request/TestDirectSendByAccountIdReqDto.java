package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springcenter.box.activity.domain.box.CustomerDetails;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class TestDirectSendByAccountIdReqDto {
    @ApiModelProperty(value = "失效的点数", required = true)
    @NotNull(message = "失效的点数不能为空")
    private BigDecimal expirePoint;

    @ApiModelProperty(value = "账户id", required = true)
    @NotBlank(message = "账户id不能为空")
    private String accountId;



}

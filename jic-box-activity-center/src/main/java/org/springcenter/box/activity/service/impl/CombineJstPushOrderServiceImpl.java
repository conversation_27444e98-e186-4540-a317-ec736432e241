package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.box.activity.config.IdConfig;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.config.redis.RedissonConfig;
import org.springcenter.box.activity.config.redis.RedissonUtil;
import org.springcenter.box.activity.domain.box.*;
import org.springcenter.box.activity.enums.GiftSendStatusEnum;
import org.springcenter.box.activity.mapper.box.BJstPushOrderMapper;
import org.springcenter.box.activity.mapper.box.BUserPointOrderDetailMapper;
import org.springcenter.box.activity.service.IBJstPushOrderService;
import org.springcenter.box.activity.service.IBLogisticsSnapshotService;
import org.springcenter.box.activity.service.IBUserPointOrderService;
import org.springcenter.box.activity.service.ICombineJstPushOrderService;
import org.springcenter.box.activity.util.DateUtil;
import org.springcenter.retail.api.IRetailApi;
import org.springcenter.retail.api.dto.req.OrderPush2JSTDTO;
import org.springcenter.retail.api.dto.req.OrderQuery2JSTDTO;
import org.springcenter.retail.api.dto.resp.PushOrder2JSTRespDTO;
import org.springcenter.retail.api.dto.resp.QueryOrder2JSTRespDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.logging.Handler;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class CombineJstPushOrderServiceImpl implements ICombineJstPushOrderService {

    @Value("${jushuitan.shopid}")
    private String jstShopId;

    @Resource
    private IBJstPushOrderService ibJstPushOrderService;

    @Resource
    private IBUserPointOrderService ibUserPointOrderService;

    @Resource
    private BUserPointOrderDetailMapper bUserPointOrderDetailMapper;


    @Resource
    private IBLogisticsSnapshotService ibLogisticsSnapshotService;


    @Resource
    private RedissonUtil redissonUtil;

    @Resource
    private IdConfig idConfig;


    @Resource
    private BJstPushOrderMapper bJstPushOrderMapper;


    @Resource
    private IRetailApi retailApi;

    @Override
    public void createJstPushOrder(String orderId) {
        // 防止并发
        String key = RedissonConfig.getLockKeyCreateJstPushOrder(orderId);
        if (!redissonUtil.tryLock(key)) {
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            BUserPointOrder bUserPointOrder = ibUserPointOrderService.getById(orderId);
            if (Long.valueOf(1).equals(bUserPointOrder.getHasJstOrder())) {
                log.info("已存在聚水潭单据");
                return;
            }
            List<BUserPointOrderDetail> bUserPointOrderDetailList = bUserPointOrderDetailMapper.getByOrderIds(Lists.newArrayList(bUserPointOrder.getId()));
            BLogisticsSnapshot bLogisticsSnapshot = ibLogisticsSnapshotService.getById(bUserPointOrder.getLogisticsSnapshotId());
            List<BJstPushOrder> result = packageJstPushOrder(bUserPointOrderDetailList, bLogisticsSnapshot);
            ibJstPushOrderService.saveBatch(result);
            BUserPointOrder updateUserPointOrder = new BUserPointOrder();
            updateUserPointOrder.setId(orderId);
            updateUserPointOrder.setHasJstOrder(1L);
            updateUserPointOrder.setUpdateTime(new Date());
            ibUserPointOrderService.updateById(updateUserPointOrder);
        } finally {
            redissonUtil.unlock(key);
        }
    }


    List<BJstPushOrder> packageJstPushOrder(List<BUserPointOrderDetail> bUserPointOrderDetailList, BLogisticsSnapshot bLogisticsSnapshot) {
        List<BJstPushOrder> jstPushOrders = new ArrayList<>();

        bUserPointOrderDetailList.forEach(e -> {
            BJstPushOrder bJstPushOrderTemp = new BJstPushOrder();
            bJstPushOrderTemp.setId(idConfig.getJstPushOrderId());
            bJstPushOrderTemp.setUnionId(e.getUnionId());
            bJstPushOrderTemp.setCustomerId(e.getCustomerId());
            bJstPushOrderTemp.setGoodsId(e.getSkuNo());
            bJstPushOrderTemp.setGoodsName(e.getGoodsName());
            bJstPushOrderTemp.setOutId(e.getId());
            bJstPushOrderTemp.setOutOrderSn(e.getOrderSn());
            bJstPushOrderTemp.setAddressName(bLogisticsSnapshot.getReceiveContact());
            bJstPushOrderTemp.setAddress(bLogisticsSnapshot.getReceiveAddress());
            bJstPushOrderTemp.setAddressPhone(bLogisticsSnapshot.getReceivePhone());
            bJstPushOrderTemp.setAddressProvince(bLogisticsSnapshot.getReceiveProvince());
            bJstPushOrderTemp.setAddressCity(bLogisticsSnapshot.getReceiveCity());
            bJstPushOrderTemp.setAddressDistrict(bLogisticsSnapshot.getReceiveDistrict());

            bJstPushOrderTemp.setCreateTime(new Date());
            bJstPushOrderTemp.setUpdateTime(new Date());

            String soId = e.getOrderSn() + e.getSkuNo();
            bJstPushOrderTemp.setSoId(soId);
            jstPushOrders.add(bJstPushOrderTemp);

        });
        return jstPushOrders;
    }


    @Override
    public BJstPushOrder getLogistics(String orderDetailId) {
        List<BJstPushOrder> bJstPushOrders = bJstPushOrderMapper.getListByOutId(orderDetailId);
        return CollectionUtils.isEmpty(bJstPushOrders) ? null : bJstPushOrders.get(0);
    }

    @Override
    public void pushJstOrder(String id) {
        log.info("开始进行推送聚水潭jstPushOrderId:{}", id);
        BJstPushOrder bJstPushOrder = Preconditions.checkNotNull(bJstPushOrderMapper.selectById(id), "推送单据不存在");

        // 聚水潭发货单
        List<OrderPush2JSTDTO> orderPush2JSTDTOList = new ArrayList<>();
        // 发货单组装
        String soId = bJstPushOrder.getSoId();
        BJstPushOrder updateJstPushOrder = new BJstPushOrder();
        updateJstPushOrder.setId(id);
        updateJstPushOrder.setUpdateTime(new Date());

        OrderPush2JSTDTO orderPush2JSTDTO = new OrderPush2JSTDTO();
        orderPush2JSTDTOList.add(orderPush2JSTDTO);
        orderPush2JSTDTO.setOrderDate(DateUtil.formatToStr(new Date(), DateUtil.DATE_FORMAT_YMDHM));
        orderPush2JSTDTO.setOuterSoId(soId);
        orderPush2JSTDTO.setSoId(soId);
        orderPush2JSTDTO.setPayAmount(BigDecimal.ZERO);
        orderPush2JSTDTO.setReceiverState(bJstPushOrder.getAddressProvince());
        orderPush2JSTDTO.setReceiverCity(bJstPushOrder.getAddressCity());
        orderPush2JSTDTO.setReceiverDistrict(bJstPushOrder.getAddressDistrict());
        orderPush2JSTDTO.setReceiverAddress(bJstPushOrder.getAddress());
        orderPush2JSTDTO.setReceiverName(bJstPushOrder.getAddressName());
        orderPush2JSTDTO.setReceiverPhone(bJstPushOrder.getAddressPhone());
        orderPush2JSTDTO.setRemark(bJstPushOrder.getOutOrderSn());
        orderPush2JSTDTO.setShopId(Integer.parseInt(jstShopId));
        List<OrderPush2JSTDTO.OrderPush2JSTDTOItem> items = new ArrayList<>();
        OrderPush2JSTDTO.OrderPush2JSTDTOItem item = new OrderPush2JSTDTO.OrderPush2JSTDTOItem();
        item.setAmount(BigDecimal.ZERO);
        item.setBasePrice(BigDecimal.ZERO);
        item.setName(bJstPushOrder.getGoodsName());
        item.setOuterOiId(bJstPushOrder.getOutId());
        item.setQty(1);
        item.setShopSkuId(bJstPushOrder.getGoodsId());
        item.setSkuId(bJstPushOrder.getGoodsId());
        items.add(item);
        orderPush2JSTDTO.setItems(items);
        log.info("通过聚水潭进行发货入参={}", JSONObject.toJSONString(orderPush2JSTDTOList));
        ResponseResult<PushOrder2JSTRespDTO> pushOrder2JSTRespDTOResponseResult = retailApi.jstPushOrder(orderPush2JSTDTOList);
        log.info("通过聚水潭进行发货返回={}", JSONObject.toJSONString(pushOrder2JSTRespDTOResponseResult));
        if (pushOrder2JSTRespDTOResponseResult.getCode() == 0) {
            if (pushOrder2JSTRespDTOResponseResult.getData() != null && CollectionUtils.isNotEmpty(pushOrder2JSTRespDTOResponseResult.getData().getDatas())) {
                Boolean issuccess = pushOrder2JSTRespDTOResponseResult.getData().getDatas().get(0).getIssuccess();
                if (!issuccess) {
                    log.error("推送聚水潭失败soId:{}",soId);
                    throw new RuntimeException("推送聚水潭失败soId"+soId);
                } else {
                    updateJstPushOrder.setStatus(1L);
                    bJstPushOrderMapper.updateById(updateJstPushOrder);
                }
            }
        }
    }

    @Override
    public void fromJstBlankLogistics(List<BJstPushOrder> bJstPushOrderList) {
        if (CollectionUtils.isEmpty(bJstPushOrderList)) {
            log.info("bJstPushOrderList信息为空直接返回");
            return;
        }

        List<String> jstSoIds = new ArrayList<>();
        jstSoIds.addAll(bJstPushOrderList.stream().map(BJstPushOrder::getSoId).collect(Collectors.toList()));
        List<QueryOrder2JSTRespDTO> queryOrder2JSTRespDTOList = requestLogistics(jstSoIds);
        if (CollectionUtils.isEmpty(queryOrder2JSTRespDTOList)) {
            log.info("拉取物流信息为空直接返回");
            return;
        }
        Map<String, BJstPushOrder> bJstPushOrderMap = new HashMap<>();
        bJstPushOrderList.forEach(e -> bJstPushOrderMap.put(e.getSoId(), e));

        queryOrder2JSTRespDTOList.forEach(e -> {
            QueryOrder2JSTRespDTO queryOrder2JSTRespDTO = e;
            if (queryOrder2JSTRespDTO != null && bJstPushOrderMap.containsKey(e.getSoId())) {
                BJstPushOrder updateJstPushOrder = new BJstPushOrder();
                updateJstPushOrder.setId(bJstPushOrderMap.get(queryOrder2JSTRespDTO.getSoId()).getId());
                updateJstPushOrder.setUpdateTime(new Date());
                updateJstPushOrder.setLogisticsNo(queryOrder2JSTRespDTO.getLId());
                updateJstPushOrder.setLogistics(queryOrder2JSTRespDTO.getLogisticsCompany());
                bJstPushOrderMapper.updateById(updateJstPushOrder);
            }
        });
    }

    @Override
    public void fromJstBlankLogisticsList() {
        int totalPage = 1;
        Page page = new Page(1, 10);

        while (page.getPageNo() <= totalPage) {
            List<BJstPushOrder> bJstPushOrdersTemp = getNeedLogisticsList(page);
            if (CollectionUtils.isEmpty(bJstPushOrdersTemp)) {
                break;
            }else{
                fromJstBlankLogistics(bJstPushOrdersTemp);
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
        }
    }

    List<QueryOrder2JSTRespDTO> requestLogistics(List<String> jstSoIds) {
        List<QueryOrder2JSTRespDTO> queryOrder2JSTRespDTOList = new ArrayList<>();
        // 查询到数据
        OrderQuery2JSTDTO orderQuery2JSTDTO = new OrderQuery2JSTDTO();
        orderQuery2JSTDTO.setSoIds(jstSoIds);
        orderQuery2JSTDTO.setShopId(Integer.parseInt(jstShopId));
        log.info("通过聚水潭进行查询是否发货信息 getJstInfo req= {}", JSONObject.toJSONString(orderQuery2JSTDTO));
        try {
            ResponseResult<List<QueryOrder2JSTRespDTO>> listResponseResult = retailApi.queryOrder(orderQuery2JSTDTO);
            log.info("通过聚水潭进行查询是否发货信息 getJstInfo resp = {}", JSONObject.toJSONString(listResponseResult));
            if (listResponseResult.getCode() == 0) {
                queryOrder2JSTRespDTOList = listResponseResult.getData();
            }
        } catch (Exception e) {
            log.info("通过聚水潭进行查询是否发货信息 发生错误  req = {}", JSONObject.toJSONString(orderQuery2JSTDTO), e);
        }

        return queryOrder2JSTRespDTOList;
    }

    @Override
    public void pushJstOrders() {
        int totalPage = 1;
        Page page = new Page(1, 900);

        List<BJstPushOrder> result = new ArrayList<>();
        while (page.getPageNo() <= totalPage) {
            List<BJstPushOrder> bJstPushOrdersTemp = getJstList(page);
            if (CollectionUtils.isEmpty(bJstPushOrdersTemp)) {
                break;
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
            result.addAll(bJstPushOrdersTemp);
        }
        if (CollectionUtils.isEmpty(result)) {
            log.info("推送数据为空，不进行处理");
            return;
        }
        result.forEach(e -> {
            try{
                pushJstOrder(e.getId());
            }catch (Exception x){
                log.error("推送聚水潭异常jstPushOrderId:{} msg:{}",e.getId(), x.getMessage());
            }
        });
    }

    public List<BJstPushOrder> getJstList(Page page) {
        com.github.pagehelper.Page<BJstPushOrder> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bJstPushOrderMapper.getNoPushList();
        PageInfo<BJstPushOrder> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }


    /**
     * 获取拉取物流信息的 推送单
     * @param page
     * @return
     */
    public List<BJstPushOrder> getNeedLogisticsList(Page page) {
        com.github.pagehelper.Page<BJstPushOrder> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bJstPushOrderMapper.getNeedLogisticsList();
        PageInfo<BJstPushOrder> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

}

package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-10-24 20:07:09
 * @Description: 
 */
@Data
@TableName("BOX_REFUND_DETAILS")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BoxRefundDetails对象", description="")
public class BoxRefundDetails implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableField("ID")
    private String id;
    @ApiModelProperty(value = "售后单主表Id")
    @TableField("BOX_REFUND_ID")
    private String boxRefundId;
    @ApiModelProperty(value = "订单明细表ID")
    @TableField("ORDER_DETAIL_ID")
    private String orderDetailId;
    @ApiModelProperty(value = "售后数量")
    @TableField("REFUND_QTY")
    private String refundQty;
    @ApiModelProperty(value = " 2 待退款 3 已成功退款 4 申请驳回  5 取消退款 ")
    @TableField("STATUS")
    private Long status;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "物流ID")
    @TableField("EXPRESSID")
    private String expressid;
    @ApiModelProperty(value = "退款金额")
    @TableField("REFUND_AMOUNT")
    private BigDecimal refundAmount;
    @ApiModelProperty(value = "储值卡退款金额")
    @TableField("REFUND_BALANCE")
    private BigDecimal refundBalance;
    @ApiModelProperty(value = "零售单详情id")
    @TableField("RETAIL_ITEM_ID")
    private String retailItemId;

}

package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lwz
 * @Date: 2024-12-16 16:33:53
 * @Description: 
 */
@TableName("B_JST_PUSH_ORDER")
@ApiModel(value="BJstPushOrder对象", description="")
public class BJstPushOrder implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;

    @TableField("UNION_ID")
    private String unionId;

    @TableField("CUSTOMER_ID")
    private String customerId;

    @ApiModelProperty(value = "ERP订单界面-外部单号")
    @TableField("SO_ID")
    private String soId;

    @ApiModelProperty(value = "收件人姓名")
    @TableField("ADDRESS_NAME")
    private String addressName;

    @ApiModelProperty(value = "收件人手机")
    @TableField("ADDRESS_PHONE")
    private String addressPhone;

    @ApiModelProperty(value = "省")
    @TableField("ADDRESS_PROVINCE")
    private String addressProvince;

    @ApiModelProperty(value = "市")
    @TableField("ADDRESS_CITY")
    private String addressCity;

    @ApiModelProperty(value = "区")
    @TableField("ADDRESS_DISTRICT")
    private String addressDistrict;

    @ApiModelProperty(value = "地址")
    @TableField("ADDRESS")
    private String address;

    @ApiModelProperty(value = "物流名称")
    @TableField("LOGISTICS")
    private String logistics;

    @ApiModelProperty(value = "物流单号")
    @TableField("LOGISTICS_NO")
    private String logisticsNo;

    /**
     * 外部单据id
     */
    @TableField("OUT_ID")
    private String outId;

    /**
     * 外部单据 订单sn
     */
    @TableField("OUT_ORDER_SN")
    private String outOrderSn;

    @TableField("GOODS_ID")
    private String goodsId;


    @TableField("GOODS_NAME")
    private String goodsName;

    @ApiModelProperty(value = "推送状态（0未推送 1已推送）")
    @TableField("STATUS")
    private Long status;

    @TableField("CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField("DEL_FLAG")
    private Long delFlag;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getSoId() {
        return soId;
    }

    public void setSoId(String soId) {
        this.soId = soId;
    }

    public String getAddressName() {
        return addressName;
    }

    public void setAddressName(String addressName) {
        this.addressName = addressName;
    }

    public String getAddressPhone() {
        return addressPhone;
    }

    public void setAddressPhone(String addressPhone) {
        this.addressPhone = addressPhone;
    }

    public String getAddressProvince() {
        return addressProvince;
    }

    public void setAddressProvince(String addressProvince) {
        this.addressProvince = addressProvince;
    }

    public String getAddressCity() {
        return addressCity;
    }

    public void setAddressCity(String addressCity) {
        this.addressCity = addressCity;
    }

    public String getAddressDistrict() {
        return addressDistrict;
    }

    public void setAddressDistrict(String addressDistrict) {
        this.addressDistrict = addressDistrict;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLogistics() {
        return logistics;
    }

    public void setLogistics(String logistics) {
        this.logistics = logistics;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getOutId() {
        return outId;
    }

    public void setOutId(String outId) {
        this.outId = outId;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Long delFlag) {
        this.delFlag = delFlag;
    }

    public String getOutOrderSn() {
        return outOrderSn;
    }

    public void setOutOrderSn(String outOrderSn) {
        this.outOrderSn = outOrderSn;
    }

    @Override
    public String toString() {
        return "BJstPushOrder{" +
                "id='" + id + '\'' +
                ", unionId='" + unionId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", soId='" + soId + '\'' +
                ", addressName='" + addressName + '\'' +
                ", addressPhone='" + addressPhone + '\'' +
                ", addressProvince='" + addressProvince + '\'' +
                ", addressCity='" + addressCity + '\'' +
                ", addressDistrict='" + addressDistrict + '\'' +
                ", address='" + address + '\'' +
                ", logistics='" + logistics + '\'' +
                ", logisticsNo='" + logisticsNo + '\'' +
                ", outId='" + outId + '\'' +
                ", outOrderSn='" + outOrderSn + '\'' +
                ", goodsId='" + goodsId + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", status=" + status +
                ", createTime=" + createTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                '}';
    }
}

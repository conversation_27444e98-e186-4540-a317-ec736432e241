package org.springcenter.box.activity.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.List;

@ApiModel("订单请求入参")
@Data
public class OrderReq {
    @ApiModelProperty(value = "box服务单号", required = true)
    private String boxSn;
    @ApiModelProperty(value = "订单创建时间起", required = true)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createStartTime;
    @ApiModelProperty(value = "订单创建时间止", required = true)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createEndTime;
    @ApiModelProperty(value = "指定商品包")
    private List<String> pacIds;
    @ApiModelProperty(value = "是否需要过滤商品")
    private Boolean needFilterItem = false;

    public void check() {
        Preconditions.checkArgument(StringUtils.isNotBlank(boxSn), "box服务单号不能为空");
        Preconditions.checkArgument(createStartTime != null, "订单创建时间不能为空");
        Preconditions.checkArgument(createEndTime != null, "订单创建时间不能为空");
    }
}

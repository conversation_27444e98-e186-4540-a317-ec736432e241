package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-10-24 19:57:26
 * @Description: 
 */
@Data
@TableName("ORDER_DETAIL")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="OrderDetail对象", description="")
public class OrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "订单id")
    @TableField("ORDER_ID")
    private String orderId;
    @ApiModelProperty(value = "商品id")
    @TableField("PRODUCT_ID")
    private String productId;
    @ApiModelProperty(value = "商品名称")
    @TableField("PRODUCT_NAME")
    private String productName;
    @ApiModelProperty(value = "商品价格")
    @TableField("PRODUCT_PRICE")
    private String productPrice;
    @ApiModelProperty(value = "商品优惠价格")
    @TableField("PRODUCT_FAVORABLE_PRICE")
    private String productFavorablePrice;
    @TableField("VIP_PRICE")
    private String vipPrice;
    @TableField("PRODUCT_SIZE")
    private String productSize;
    @ApiModelProperty(value = "商品数量")
    @TableField("PRODUCT_QUANTITY")
    private String productQuantity;
    @ApiModelProperty(value = "商品颜色规格")
    @TableField("PRODUCT_COLOR_NO")
    private String productColorNo;
    @ApiModelProperty(value = "商品颜色")
    @TableField("PRODUCT_COLOR")
    private String productColor;
    @ApiModelProperty(value = "商品品牌名")
    @TableField("PRODUCT_BRAND")
    private String productBrand;
    @ApiModelProperty(value = "缩略图")
    @TableField("IMG_URL")
    private String imgUrl;
    @ApiModelProperty(value = "0:未支付,1:已支付,2:已退款,3:待退款")
    @TableField("STATUS")
    private Long status;
    @ApiModelProperty(value = "售后原因")
    @TableField("REASON")
    private String reason;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "条码（获取商品详情条码no）")
    @TableField("SKU")
    private String sku;
    @ApiModelProperty(value = "款号")
    @TableField("PRODUCT_CODE")
    private String productCode;
    @ApiModelProperty(value = "商品id(对应接口中的id)")
    @TableField("OUT_ID")
    private String outId;
    @ApiModelProperty(value = "box详情Id")
    @TableField("BOX_DETAIL_ID")
    private String boxDetailId;
    @ApiModelProperty(value = "大季")
    @TableField("BIG_SEASON")
    private String bigSeason;
    @ApiModelProperty(value = "年份")
    @TableField("YEAR")
    private String year;
    @ApiModelProperty(value = "实付金额")
    @TableField("PAID_AMOUNT")
    private BigDecimal paidAmount;
    @ApiModelProperty(value = "是否使用优惠券 0-否 1-是")
    @TableField("USE_VOU")
    private Long useVou;
    @ApiModelProperty(value = "最终实付金额(减去券的优惠减去礼品卡金额)")
    @TableField("PRICEACTUAL")
    private BigDecimal priceactual;
    @ApiModelProperty(value = "是否退款    0 否   1  是  ")
    @TableField("IS_REFUND")
    private Long isRefund;
    @ApiModelProperty(value = "退款数量")
    @TableField("REFUND_QTY")
    private String refundQty;
    @ApiModelProperty(value = "内淘数量")
    @TableField("EB_NUM")
    private Long ebNum;
    @ApiModelProperty(value = "物流id")
    @TableField("EXPRESS_ID")
    private String expressId;
    @ApiModelProperty(value = "优惠券金额")
    @TableField("VOUCHER_AMOUNT")
    private BigDecimal voucherAmount;
    @ApiModelProperty(value = "储值卡金额")
    @TableField("BALANCE_AMT")
    private BigDecimal balanceAmt;
    @ApiModelProperty(value = "商场代金券金额")
    @TableField("SHOP_VOU_AMT")
    private BigDecimal shopVouAmt;


}

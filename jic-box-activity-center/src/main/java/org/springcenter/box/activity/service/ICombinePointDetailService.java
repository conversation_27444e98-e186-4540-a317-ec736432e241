package org.springcenter.box.activity.service;

import org.springcenter.box.activity.domain.box.FuturePointInDaysEntity;
import org.springcenter.box.activity.domain.box.PointDetailWithSub;
import org.springcenter.box.activity.domain.box.UserPointEntity;

import java.math.BigDecimal;
import java.util.List;

public interface ICombinePointDetailService {

    /**
     * 获取实时的点数
     * @param customerId
     * @return
     */
    BigDecimal realTimePointByCustomerId(String customerId);


    /**
     * 重组点数封装
     * @param customerId
     * @return
     */
    List<PointDetailWithSub> getPointDetailWithSub(String customerId);


    /**
     * 规则用户点数
     * @param customerId
     * @return
     */
    UserPointEntity rulesUserPoint(String customerId);



    /**
     * 获取用户即将过期的点数
     * @param unionId
     * @param days
     * @return
     */
    FuturePointInDaysEntity getFuturePointInDays(String unionId,Integer days);


    /**
     * 处理到期数据subId
     * @param subId
     */
    void realHandleSubIdAddRecord(String subId);
}

package org.springcenter.box.activity.job.point;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.jnbyframework.boot.common.api.dto.message.TemplateMessageDTO;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.box.activity.config.trace.XxlJobTaskLog;
import org.springcenter.box.activity.domain.box.BPointDetail;
import org.springcenter.box.activity.domain.box.BPointGoodsSpu;
import org.springcenter.box.activity.domain.box.BUserPointAccount;
import org.springcenter.box.activity.domain.box.CustomerDetails;
import org.springcenter.box.activity.enums.SysTemplateCodeEnum;
import org.springcenter.box.activity.mapper.box.BPointDetailMapper;
import org.springcenter.box.activity.mapper.box.BPointGoodsSpuMapper;
import org.springcenter.box.activity.mapper.box.BUserPointAccountMapper;
import org.springcenter.box.activity.mapper.box.CustomerDetailsMapper;
import org.springcenter.box.activity.service.ISysMessageService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * 过期7天前过期的短信和微信小程序消息
 */
@Component
@Slf4j
public class PointExpireBefore7SendMsgJob extends IJobHandler {

    @Resource
    private BPointGoodsSpuMapper bPointGoodsSpuMapper;

    @Resource
    private BUserPointAccountMapper bUserPointAccountMapper;

    @Resource
    private BPointDetailMapper bPointDetailMapper;

    @Resource
    private ISysMessageService iSysMessageService;

    @Resource
    private CustomerDetailsMapper customerDetailsMapper;

    @Override
    @XxlJob("PointExpireBefore7SendMsgJob")
    public void execute() throws Exception {
        XxlJobTaskLog.traceLog("过期7天前过期的短信和微信小程序消息开始");
        handle();
        XxlJobTaskLog.traceLog("过期7天前过期的短信和微信小程序消息结束");
    }
    void handle(){
        // 1. 先查 最低的spu 点数
        BPointGoodsSpu bPointGoodsSpu = bPointGoodsSpuMapper.getLastLowPointGoodsSpu();
        if (bPointGoodsSpu == null) {
            log.info("不存在最低商品");
            return;
        }
        log.info("查询到最低商品spuId:{} 最低点数:{} 具体商品信息:{}", bPointGoodsSpu.getId(), bPointGoodsSpu.getPoint(), JSONObject.toJSONString(bPointGoodsSpu));
        BigDecimal lowPoint = bPointGoodsSpu.getPoint();

        // 查询符合条件的账户信息
        List<BUserPointAccount> result = getAllAdaptAccount(lowPoint);
        if (CollectionUtils.isEmpty(result)) {
            log.info("不存在满足条件的账户");
            return;
        }
        // 然后再根据 查询的账户 查询出即将过期的点数
        result.forEach(e -> {
            sendMsg(e);
        });
    }


    List<BUserPointAccount> getAllAdaptAccount(BigDecimal lowPoint){
        // 查询符合条件的账户信息
        int totalPage = 1;
        Page page = new Page(1, 500);
        List<BUserPointAccount> currentResult = new ArrayList<>();
        while (page.getPageNo() <= totalPage) {
            List<BUserPointAccount> bPointDetailList = getNeedPointAccountList(page, lowPoint);
            if (CollectionUtils.isEmpty(bPointDetailList)) {
                break;
            } else {
                currentResult.addAll(bPointDetailList);
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
        }
        return currentResult;
    }


   public void sendMsg(BUserPointAccount bUserPointAccount) {
        try {
            BigDecimal expirePoint = getExpirePoint(bUserPointAccount.getId());
            if (expirePoint.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("当前用户7天过期点数用户:{} unionId:{} 点数:{} 不满足发送条件", bUserPointAccount.getId(), bUserPointAccount.getUnionId(), expirePoint);
                return;
            }
            log.info("当前用户7天过期点数用户:{} unionId:{} 点数:{}", bUserPointAccount.getId(), bUserPointAccount.getUnionId(), expirePoint);

            CustomerDetails customerDetails = customerDetailsMapper.selectById(bUserPointAccount.getId());
            sendMiniMsg(expirePoint, bUserPointAccount, customerDetails);
            sendSmsMsg(expirePoint, customerDetails);
        }catch (Exception x){
            log.error("发送消息异常-当前用户7天过期点数用户:{} unionId:{} ",bUserPointAccount.getId(), bUserPointAccount.getUnionId(),x);
        }
    }


    public BigDecimal getExpirePoint(String customerId) {
        BigDecimal  result =  bPointDetailMapper.getExpirePointInDays(7, customerId);
        if(ObjectUtils.isEmpty(result)){
            return BigDecimal.ZERO;
        }
        return  result;
    }


    public List<BUserPointAccount> getNeedPointAccountList(Page page, BigDecimal lowPoint) {
        com.github.pagehelper.Page<BPointDetail> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bUserPointAccountMapper.getListByOverPoint(lowPoint);
        PageInfo<BUserPointAccount> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }


    public void sendMiniMsg(BigDecimal expirePoint, BUserPointAccount bUserPointAccount, CustomerDetails customerDetails){
        // 发送短信 和 微信小程序消息
        String path = "pages/index_/index?type=55&channelId=" + "XCX";
        Map<String, String> map = new HashMap<>();
        // 当前剩余积分
        map.put("number2", bUserPointAccount.getTotalPoint().toString());
        // 即将过期积分
        map.put("number3", expirePoint.toString());
        // 温馨提示
        map.put("thing4", "兑换值1个月内有效，逾期失效不可兑换哟~");
        map.put("path", path);
        TemplateMessageDTO messageDTO = new TemplateMessageDTO();
        messageDTO.setToUser(customerDetails.getOpenid());
        messageDTO.setTemplateParam(map);
        messageDTO.setTemplateCode("D6jWjeKlrUAuyF9vvO8BfYLpktmeCsN8uy5LeO1-2QU");
        iSysMessageService.sendMessageByTemplate(messageDTO);
    }

    private void sendSmsMsg(BigDecimal expirePoint, CustomerDetails customerDetails) {
        String shortLink = "https://t.jnby.com?k=571ngko";
        Map<String, String> map = new HashMap<>();
        map.put("undefined", expirePoint.toString());
        map.put("address", shortLink);

        if (customerDetails == null || StringUtils.isBlank(customerDetails.getPhone())) {
            return;
        }
        TemplateMessageDTO templateMessasgeDTO = new TemplateMessageDTO();
        templateMessasgeDTO.setToUser(customerDetails.getPhone());
        templateMessasgeDTO.setTemplateCode(SysTemplateCodeEnum.POINT_EXPIRE_SEND_SMS_7282.getCode());
        templateMessasgeDTO.setTemplateParam(map);
        iSysMessageService.sendMessageByTemplate(templateMessasgeDTO);
    }

}

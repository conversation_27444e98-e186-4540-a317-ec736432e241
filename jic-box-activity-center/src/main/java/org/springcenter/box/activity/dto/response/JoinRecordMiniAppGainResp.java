package org.springcenter.box.activity.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("是否获得赠品的回参")
@Data
public class JoinRecordMiniAppGainResp {
    @ApiModelProperty(value = "记录ID")
    private String id;
    @ApiModelProperty(value = "boxId")
    private String boxId;
    @ApiModelProperty(value = "达成状态:0=不可发，1=可发")
    private Integer accomplishStatus;
}

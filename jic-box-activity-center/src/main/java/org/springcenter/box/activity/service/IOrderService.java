package org.springcenter.box.activity.service;

import org.springcenter.box.activity.domain.box.BoxRefund;
import org.springcenter.box.activity.domain.box.Order;
import org.springcenter.box.activity.dto.OrderDto;
import org.springcenter.box.activity.dto.request.OrderReq;
import com.jnby.common.Page;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface IOrderService {

    /**
     * 获取支付单列表和实付信息
     * 支付金额+储值卡金额-(退款金额+退储值卡金额)
     * @return 订单列表
     */
    List<OrderDto> listOrderDto(OrderReq orderReq);

    /**
     * 根据unionId获取订单
     * @param unionId
     * @return
     */
    Order getOrderByUnionId(String unionId);
    /**
     * 根据更新时间范围查询支付单
     */
    List<Order> listOrderByUpdateTimeRange(Date startTime, Date endTime, Page page);
    /**
     * 根据更新时间范围查询退款单
     */
    List<BoxRefund> listRefundByUpdateTimeRange(Date startTime, Date endTime, Page page);
    /**
     * 扫描增量订单
     */
    void scanIncOrder(Date startTime, Date endTime);
    /**
     * 扫描增量退款单
     */
    void scanIncRefund(Date startTime, Date endTime);

    List<String> listOrderIdByBoxSn(String boxSn);
}

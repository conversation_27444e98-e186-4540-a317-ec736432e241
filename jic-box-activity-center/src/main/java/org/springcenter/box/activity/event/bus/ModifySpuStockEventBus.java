package org.springcenter.box.activity.event.bus;

import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.box.activity.event.ModifySpuStockEvent;
import org.springcenter.box.activity.event.handler.ModifySpuStockHandler;
import org.springcenter.box.activity.service.BaseEventBus;
import org.springcenter.box.activity.service.IBusEventsService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ModifySpuStockEventBus implements InitializingBean, BaseEventBus<ModifySpuStockEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IBusEventsService iBusEventsService;

    @Resource
    private ModifySpuStockHandler modifySpuStockHandler;

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(modifySpuStockHandler);
    }

    @Override
    public void post(ModifySpuStockEvent modifySpuStockEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(modifySpuStockEvent);
    }
}

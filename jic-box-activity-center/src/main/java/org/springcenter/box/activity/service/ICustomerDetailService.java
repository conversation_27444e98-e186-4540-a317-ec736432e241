package org.springcenter.box.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jnby.common.Page;
import org.springcenter.box.activity.api.dto.SalesCustomerListResp;
import org.springcenter.box.activity.api.dto.SalesCustomerReq;
import org.springcenter.box.activity.domain.box.CustomerDetails;

import java.util.List;

public interface ICustomerDetailService extends IService<CustomerDetails> {
    List<SalesCustomerListResp> querySalesBoxSubscribeMember(SalesCustomerReq salesCustomerReq, Page page);
}

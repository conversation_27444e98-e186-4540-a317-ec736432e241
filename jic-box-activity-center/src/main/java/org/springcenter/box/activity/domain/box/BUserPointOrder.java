package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: lwz
 * @Date: 2024-12-11 10:16:24
 * @Description: 
 */
@TableName("B_USER_POINT_ORDER")
@ApiModel(value="BUserPointOrder对象", description="")
public class BUserPointOrder implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;

    @TableField("UNION_ID")
    private String unionId;

    @TableField("CUSTOMER_ID")
    private String customerId;

    @TableField("USED_POINT")
    private BigDecimal usedPoint;

    @TableField("ORDER_SN")
    private String orderSn;


    @TableField("LOGISTICS_SNAPSHOT_ID")
    private String logisticsSnapshotId;

    @TableField("CUSTOMER_LOGISTICS_ID")
    private String customerLogisticsId;

    @TableField("HAS_JST_ORDER")
    private Long hasJstOrder;


    @TableField("CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField("DEL_FLAG")
    private Long delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public BigDecimal getUsedPoint() {
        return usedPoint;
    }

    public void setUsedPoint(BigDecimal usedPoint) {
        this.usedPoint = usedPoint;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Long delFlag) {
        this.delFlag = delFlag;
    }


    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getLogisticsSnapshotId() {
        return logisticsSnapshotId;
    }

    public void setLogisticsSnapshotId(String logisticsSnapshotId) {
        this.logisticsSnapshotId = logisticsSnapshotId;
    }


    public Long getHasJstOrder() {
        return hasJstOrder;
    }

    public void setHasJstOrder(Long hasJstOrder) {
        this.hasJstOrder = hasJstOrder;
    }

    public String getCustomerLogisticsId() {
        return customerLogisticsId;
    }

    public void setCustomerLogisticsId(String customerLogisticsId) {
        this.customerLogisticsId = customerLogisticsId;
    }

    @Override
    public String toString() {
        return "BUserPointOrder{" +
                "id='" + id + '\'' +
                ", unionId='" + unionId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", usedPoint=" + usedPoint +
                ", orderSn='" + orderSn + '\'' +
                ", logisticsSnapshotId='" + logisticsSnapshotId + '\'' +
                ", customerLogisticsId='" + customerLogisticsId + '\'' +
                ", hasJstOrder=" + hasJstOrder +
                ", createTime=" + createTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                '}';
    }
}

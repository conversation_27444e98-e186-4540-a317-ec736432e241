package org.springcenter.box.activity.config.trace;

import brave.Span;
import brave.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.cloud.sleuth.util.SpanNameUtil;

/**
 * <AUTHOR>
 * XxlJob 追踪切面
 */
@Aspect
@Slf4j
public class EventBusTraceAspect {

    private static final String CLASS_KEY = "class";

    private static final String METHOD_KEY = "method";


    private Tracer tracer;

    public EventBusTraceAspect(Tracer tracer){
        this.tracer=tracer;
    }

    @Around("execution (* org.springcenter.box.activity.event.handler..*.*(..)) && execution (@com.google.common.eventbus.Subscribe  * *.*(..))")
    public Object traceBackgroundThread(final ProceedingJoinPoint pjp) throws Throwable {
        String spanName = SpanNameUtil.toLowerHyphen(pjp.getSignature().getName());
        Span span = startOrContinueRenamedSpan(spanName);
        try (Tracer.SpanInScope ws = this.tracer.withSpanInScope(span.start())) {
            span.tag(CLASS_KEY, pjp.getTarget().getClass().getSimpleName());
            span.tag(METHOD_KEY, pjp.getSignature().getName());
            return pjp.proceed();
        } catch (Throwable ex) {
            String message = ex.getMessage() == null ? ex.getClass().getSimpleName()
                    : ex.getMessage();
            span.tag("error", message);
            throw ex;
        } finally {
            span.finish();
        }
    }


    /**
     * 开启 新的Span 便于追踪
     *
     * @param spanName
     * @return
     */
    private Span startOrContinueRenamedSpan(String spanName) {
        Span currentSpan = this.tracer.currentSpan();
        if (currentSpan != null) {
            return currentSpan.name(spanName);
        }
        return this.tracer.nextSpan().name(spanName);
    }
}

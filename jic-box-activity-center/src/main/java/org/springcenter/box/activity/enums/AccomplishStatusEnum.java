package org.springcenter.box.activity.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 达成状态
 */
public enum AccomplishStatusEnum {
    UN_ACCOMPLISH(0, "未达成"),
    ACCOMPLISH(1, "达成"),
    ;

    private static final Map<Integer, AccomplishStatusEnum> LOOKUP = new HashMap<>();

    static {
        for (AccomplishStatusEnum s : EnumSet.allOf(AccomplishStatusEnum.class)) {
            LOOKUP.put(s.getCode(), s);
        }
    }

    private Integer code;
    private String name;

    AccomplishStatusEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }

    public static String get(Integer code) {
        AccomplishStatusEnum orderStatusEnum = LOOKUP.get(code);
        return orderStatusEnum.getName();
    }

    /**
     * 是否达成
     * @param code
     * @return
     */
    public static boolean isAccomplish(Integer code) {
        return ACCOMPLISH.getCode().equals(code);
    }
}
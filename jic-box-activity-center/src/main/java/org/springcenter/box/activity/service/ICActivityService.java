package org.springcenter.box.activity.service;

import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.api.dto.CActivityUpdateTaskReq;
import org.springcenter.box.activity.dto.request.CActivityCreateReq;
import org.springcenter.box.activity.dto.request.CActivityEffectReq;
import org.springcenter.box.activity.dto.request.CActivityListReq;
import com.jnby.common.Page;
import org.springcenter.box.activity.dto.request.CActivityUpdateReq;

import java.util.List;

public interface ICActivityService {

    /**
     * 创建活动
     *
     * @param requestData
     * @return 成功返回活动ID
     */
    String activityCreated(CActivityCreateReq requestData);

    /**
     * 活动列表查询
     *
     * @param requestData
     * @param page
     * @return
     */
    List<CActivityInfoResp> activityList(CActivityListReq requestData, Page page);

    /**
     * 获取活动详情
     *
     * @param activityId 活动ID
     * @param isAdmin
     * @return
     */
    CActivityInfoResp activityInfo(String activityId, Boolean isAdmin);

    /**
     * 活动
     *
     * @param activityId
     * @param optPerson
     */
    void activityCancel(String activityId, String optPerson);

    /**
     * 根据活动id检查赠品库存
     *
     * @param activityId 活动ID
     * @return true:库存充足 false:库存不足
     */
    Boolean checkInventory(String activityId);

    /**
     * 有效活动（符合人群条件）
     *
     * @param requestData
     * @return 符合人群条件的进行中活动
     */
    List<CActivityInfoResp> effectActivity(CActivityEffectReq requestData);

    /**
     * 任务更新
     *
     * @param requestData
     */
    void updateTask(CActivityUpdateTaskReq requestData);

    /**
     * 一键发货任务提交
     *
     * @param activityId
     */
    void deliveryJobSubmit(CActivityUpdateReq activityId);

    /**
     * 一键发货调度执行
     */
    void deliveryJobExecute();

    /**
     * 更新赠品库存
     *
     * @param giftId 赠品ID
     * @param addNum 增加数量(正整数)
     * @param optPerson 操作人
     * @return 更新是否成功
     */
    boolean addGiftStock(String giftId, Integer addNum, String optPerson);

    /**
     * 下架活动
     *
     * @param activityId
     * @param optPerson
     */
    void activityDown(String activityId, String optPerson);
    void activityEndByWaitSend(String activityId, String optPerson);
}

package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.springcenter.box.activity.remote.IStoreCenterHttpApi;
import org.springcenter.box.activity.remote.entity.HitStoreEntityReq;
import org.springcenter.box.activity.remote.entity.ListStoreEntityReq;
import org.springcenter.box.activity.remote.entity.ListStoreEntityResp;
import org.springcenter.box.activity.service.IStoreCenterHttpService;
import com.jnby.common.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StoreCenterHttpServiceImpl implements IStoreCenterHttpService {
    @Resource
    private IStoreCenterHttpApi storeCenterHttpApi;

    @Override
    public List<Long> listStoreId(Long storePackageId) {
        List<Long> ids = Lists.newArrayList();
        try {
            ListStoreEntityReq req = new ListStoreEntityReq();
            req.setStorePackageId(storePackageId);
            log.info("查询门店包,request={}", JSON.toJSONString(req));
            Response<ResponseResult<List<ListStoreEntityResp>>> response = storeCenterHttpApi.listStore(req).execute();
            // 请求成功
            if (!response.isSuccessful()) {
                log.info("查询门店包出错，返回空集合");
                return ids;
            }
            ResponseResult<List<ListStoreEntityResp>> body = response.body();
            log.info("查询门店包,response={}", JSON.toJSONString(body));
            ids = Optional.ofNullable(body.getData()).orElse(Lists.newArrayList()).stream().map(ListStoreEntityResp::getId).collect(Collectors.toList());
            return ids;
        } catch (Exception e) {
            log.error("查询门店包异常", e);
            return ids;
        }
    }

    @Override
    public Boolean hitStore(Long storePackageId, Long storeId) {
        Boolean hit = false;
        try {
            HitStoreEntityReq req = new HitStoreEntityReq();
            req.setStorePackageId(storePackageId);
            req.setStoreId(storeId);
            log.info("调用远程接口判断门店包是否命中 request={}", JSON.toJSONString(req));
            Response<ResponseResult<Boolean>> response = storeCenterHttpApi.hitStore(req).execute();
            // 请求成功
            if (!response.isSuccessful()) {
                log.info("调用远程接口判断门店包是否命中出错，未命中");
                return hit;
            }
            ResponseResult<Boolean> body = response.body();
            log.info("调用远程接口判断门店包是否命中 response={}", JSON.toJSONString(body));
            if (Boolean.TRUE.equals(body.getData())) {
                hit = true;
                return hit;
            }
        } catch (Exception e) {
            log.error("调用远程接口判断门店包是否命中出错异常", e);
        }
        return hit;
    }
}

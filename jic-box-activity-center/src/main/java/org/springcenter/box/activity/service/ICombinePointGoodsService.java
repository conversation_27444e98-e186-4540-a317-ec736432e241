package org.springcenter.box.activity.service;

import org.springcenter.box.activity.dto.request.AddGoodsReqDto;
import org.springcenter.box.activity.domain.box.BPointGoodsSpu;
import org.springcenter.box.activity.dto.request.SpuGoodsListByUnionIdReqDto;
import org.springcenter.box.activity.dto.request.SpuGoodsListReqDto;
import org.springcenter.box.activity.dto.request.UpdateGoodsReqDto;
import org.springcenter.box.activity.mapper.box.PointGoods;

import java.util.List;

public interface ICombinePointGoodsService {

    List<PointGoods> getGoodsList();

    List<BPointGoodsSpu>  spuGoodsList(SpuGoodsListReqDto spuGoodsListReqDto);


    List<BPointGoodsSpu>  spuGoodsListByUnionId(SpuGoodsListByUnionIdReqDto spuGoodsListByUnionIdReqDto);

    PointGoods goodsInfo(String supId);


    void batchAddList(AddGoodsReqDto addGoodsReqDto);


    void batchUpdateList(UpdateGoodsReqDto updateGoodsReqDto);


    void handleSpuStock(List<String> spuIdList);
}

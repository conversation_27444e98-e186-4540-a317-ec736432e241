package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springcenter.box.activity.enums.GiftSendStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-09-23 14:23:16
 * @Description: 参与记录
 */
@Data
@TableName("C_ACTIVITY_JOIN_RECORD")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="CActivityJoinRecord对象", description="参与记录")
public class CActivityJoinRecord implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @TableField("IS_DELETE")
    private Integer isDelete;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "活动ID")
    @TableField("ACTIVITY_ID")
    private String activityId;
    @ApiModelProperty(value = "消费者ID")
    @TableField("UNIONID")
    private String unionId;
    @ApiModelProperty(value = "BOX单号")
    @TableField("BOX_SN")
    private String boxSn;
    @ApiModelProperty(value = "DD单号集合")
    @TableField("ORDER_NO_LIST")
    private String orderNoList;
    @ApiModelProperty(value = "实付金额")
    @TableField("PAYMENT")
    private BigDecimal payment;
    @ApiModelProperty(value = "实付件数")
    @TableField("QUANTITY")
    private Integer quantity;
    @ApiModelProperty(value = "达成状态:0=未达成，1=达成")
    @TableField("ACCOMPLISH_STATUS")
    private Integer accomplishStatus;
    @ApiModelProperty(value = "物流名称")
    @TableField("LOGISTICS")
    private String logistics;
    @ApiModelProperty(value = "物流单号")
    @TableField("LOGISTICS_NO")
    private String logisticsNo;
    @ApiModelProperty(value = "地址配置ID")
    @TableField("ADDRESS_ID")
    private String addressId;
    @ApiModelProperty(value = "赠品ID")
    @TableField("GIFT_ID")
    private String giftId;
    @ApiModelProperty(value = "搭配师ID")
    @TableField("FASHIONER_ID")
    private String fashionerId;
    /**
     * {@link GiftSendStatusEnum}
     */
    @ApiModelProperty(value = "发放状态:0=待发放，1=等待结果，2=发放成功，3=发放失败")
    @TableField("GIFT_SEND_STATUS")
    private Integer giftSendStatus;
    @ApiModelProperty(value = "失败原因")
    @TableField("GIFT_SEND_ERROR_MSG")
    private String giftSendErrorMsg;
    @ApiModelProperty(value = "ERP订单界面-外部单号")
    @TableField("SO_ID")
    private String soId;
    @ApiModelProperty(value = "收件人姓名")
    @TableField("ADDRESS_NAME")
    private String addressName;
    @ApiModelProperty(value = "收件人手机")
    @TableField("ADDRESS_PHONE")
    private String addressPhone;
    @ApiModelProperty(value = "省")
    @TableField("ADDRESS_PROVINCE")
    private String addressProvince;
    @ApiModelProperty(value = "市")
    @TableField("ADDRESS_CITY")
    private String addressCity;
    @ApiModelProperty(value = "区")
    @TableField("ADDRESS_DISTRICT")
    private String addressDistrict;
    @ApiModelProperty(value = "地址")
    @TableField("ADDRESS")
    private String address;
    
    @ApiModelProperty(value = "操作人")
    @TableField("OPT_PERSON")
    private String optPerson;
    @ApiModelProperty(value = "操作备注")
    @TableField("OPT_REMARK")
    private String optRemark;
    @ApiModelProperty(value = "操作时间")
    @TableField("OPT_TIME")
    private Date optTime;

    @ApiModelProperty(value = "BOX_ID")
    @TableField("BOX_ID")
    private String boxId;

    @ApiModelProperty(value = "兑换地址")
    @TableField("EXCHANGE_URL")
    private String exchangeUrl;
    @ApiModelProperty(value = "兑换卡号")
    @TableField("EXCHANGE_NUMBER")
    private String exchangeNumber;
    @ApiModelProperty(value = "兑换密钥")
    @TableField("EXCHANGE_SECRET")
    private String exchangeSecret;

}

package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel("修改地址入参")
@Data
public class JoinRecordChangeAddressReq {
    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空")
    private String optPerson;
    @ApiModelProperty(value = "记录ID", required = true)
    @NotBlank(message = "记录ID不能为空")
    private String id;
    @ApiModelProperty(value = "配置地址ID", required = true)
    @NotBlank(message = "配置地址ID不能为空")
    private String addressId;
}

package org.springcenter.box.activity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ItemRangeEnum {

    /**
     * 商品范围
     * 1=所有，2=指定商品过滤且满足门槛，3=满足门槛且含指定商品
     */

    ALL(1, "所有商品"),
    FILTER_ITEM_AND_ACCOMPLISH(2, "指定商品过滤且满足门槛"),
    ACCOMPLISH_AND_INCLUDE_ITEM(3, "满足门槛且含指定商品"),
    ;
    public static final Map<Integer, ItemRangeEnum> LOOKUP = new HashMap<>();

    static {
        for (ItemRangeEnum s : EnumSet.allOf(ItemRangeEnum.class)) {
            LOOKUP.put(s.getCode(), s);
        }
    }
    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    /**
     * 指定商品过滤且满足门槛
     */
    public static boolean isFilterItemAndAccomplish(Integer code) {
        return FILTER_ITEM_AND_ACCOMPLISH.getCode().equals(code);
    }

    /**
     * 满足门槛且含指定商品
     */
    public static boolean isAccomplishAndIncludeItem(Integer code) {
        return ACCOMPLISH_AND_INCLUDE_ITEM.getCode().equals(code);
    }

    /**
     * 非全部
     */
    public static boolean isAll(Integer code) {
        return ALL.getCode().equals(code);
    }
}

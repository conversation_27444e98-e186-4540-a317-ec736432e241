package org.springcenter.box.activity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcenter.box.activity.domain.box.BoxDetails;
import org.springcenter.box.activity.enums.UltimaBoxDetailsStatusEnum;
import org.springcenter.box.activity.mapper.box.BoxDetailsMapper;
import org.springcenter.box.activity.service.IBoxDetailsService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BoxDetailsServiceImpl extends ServiceImpl<BoxDetailsMapper, BoxDetails> implements IBoxDetailsService {
    @Override
    public List<BoxDetails> getUnDealGoodsList(List<String> boxIds) {
        LambdaQueryWrapper<BoxDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BoxDetails::getBoxId, boxIds);
        queryWrapper.notIn(BoxDetails::getStatus, UltimaBoxDetailsStatusEnum.PAY.getCode().longValue());
        return this.list(queryWrapper);
    }
}

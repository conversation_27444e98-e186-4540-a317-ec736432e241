package org.springcenter.box.activity.config.redis;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * @Auther: lwz
 * @Date: 2021/11/8 18:20
 * @Description: RedissonConfig
 * @Version 1.0.0
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private String port;

    @Value("${spring.redis.password:#{null}}")
    private String password;

    @Bean
    public RedissonClient getRedisson(){
        Config config = new Config();
        config.useSingleServer().setAddress("redis://" + host + ":" + port);
        if (!StringUtils.isEmpty(password)){
            config.useSingleServer().setPassword(password);
        }
        return Redisson.create(config);
    }

    private static final String REDISSON_KEY_PRE = "box_activity:";
    /**
     * 获取任务更新锁KEY
     */
    public static String getLockKeyUpdateTask(String id) {
        return REDISSON_KEY_PRE + "update_task:" + id;
    }

    public static String getLockKeyJoinRecordCreate(String id) {
        return REDISSON_KEY_PRE + "join_record_create:" + id;
    }

    public static String getLockKeyJoinRecordUpdateStatus(String id) {
        return REDISSON_KEY_PRE + "join_record_update_status:" + id;
    }

    public static String getLockKeyJoinRecordChangeAddress(String id) {
        return REDISSON_KEY_PRE + "join_record_change_address:" + id;
    }

    public static String getLockKeyJoinRecordDelivery(String id) {
        return REDISSON_KEY_PRE + "join_record_delivery:" + id;
    }


    public static String getLockKeyCustomerId(String id) {
        return REDISSON_KEY_PRE + "customerId:" + id;
    }



    public static String getLockKeyCreateJstPushOrder(String id) {
        return REDISSON_KEY_PRE + "createJstPushOrder:" + id;
    }
}

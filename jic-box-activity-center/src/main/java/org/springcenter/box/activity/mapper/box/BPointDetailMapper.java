package org.springcenter.box.activity.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.box.activity.domain.box.BPointDetail;

import java.math.BigDecimal;
import java.util.List;


/**
 * @Author: lwz
 * @Date: 2024-12-04 16:52:52
 * @Description: Mapper
 */
public interface BPointDetailMapper extends BaseMapper<BPointDetail> {

    /**
     *
     * @param unionId
     * @return
     */
    List<BPointDetail> getListByUnionId(@Param("unionId") String unionId);

    /**
     *
     * @param customerId
     * @return
     */
    List<BPointDetail> getListByCustomerId(@Param("customerId") String customerId);



    List<BPointDetail> getListByOutId(@Param("outId") String outId);


    BigDecimal getTotalPoint(@Param("customerId") String customerId);


    /**
     * 获取失效时间大于当前时间
     * @return
     */
    List<BPointDetail> getActiveSubTimeList();


    /**
     * 获取在指定时间的 点数
     * @param days
     * @param customerId
     * @return
     */
    BigDecimal getExpirePointInDays(@Param("days") Integer days, @Param("customerId") String customerId);

    /**
     * 根据订阅id 查询
     * @param unionId
     * @return
     */
    List<BPointDetail> getAllListByUnionId(@Param("unionId") String unionId);


    /**
     * 查询未来多少天内的详细  （大于当前时间小于当前时间+days）
     * @param unionId
     * @param days
     * @return
     */
    List<BPointDetail> getListByUnionIdAndTime(@Param("unionId") String unionId,@Param("days") Integer days);


    /**
     * 查询过期的订阅id 两天内
     * @return
     */
    List<String> getExpireSubIdsInTwoDays();


    /**
     * 查询所有过期的订阅id
     * @return
     */
    List<String> getAllExpireSubIds();


    /**
     * 获取周期内 点数总值
     * @param subId
     * @return
     */
    BigDecimal getShowPointBySubId(@Param("subId") String subId);


    List<BPointDetail> getListBySubId(@Param("subId") String subId);
}

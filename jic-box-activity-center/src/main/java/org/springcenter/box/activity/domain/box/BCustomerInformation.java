package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: wangchun
 * @Date: 2021-09-14 16:26:18
 * @Description: 
 */
@TableName("B_CUSTOMER_INFORMATION")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BCustomerInformation对象", description="")
public class BCustomerInformation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户id")
    @TableId(value = "USER_ID", type = IdType.UUID)
    private String userId;

    @ApiModelProperty(value = "用户分类ID(对应attr_value)")
    @TableField("B_CATEGORY_ID")
    private Long bCategoryId;

    @ApiModelProperty(value = "最近一次加入分类原因ID")
    @TableField("JOIN_CATEGORY_REASON_ID")
    private Long joinCategoryReasonId;

    @ApiModelProperty(value = "最近一次加入分类原因")
    @TableField("JOIN_CATEGORY_REASON")
    private String joinCategoryReason;

    @ApiModelProperty(value = "最近一次加入分类时间")
    @TableField("JOIN_CATEGORY_TIME")
    private Date joinCategoryTime;

    @TableField("CREATE_TIME")
    private Date createTime;

    @TableField("UPDATE_TIME")
    private Date updateTime;

    @TableField("DATA_EXPAND")
    private String dataExpand;

    @TableField("last_fashioner_id")
    private String lastFashionerId;

    @TableField("last_fashioner_name")
    private String lastFashionerName;

    public String getLastFashionerId() {
        return lastFashionerId;
    }

    public void setLastFashionerId(String lastFashionerId) {
        this.lastFashionerId = lastFashionerId;
    }

    public String getLastFashionerName() {
        return lastFashionerName;
    }

    public void setLastFashionerName(String lastFashionerName) {
        this.lastFashionerName = lastFashionerName;
    }

    public String getDataExpand() {
        return dataExpand;
    }

    public void setDataExpand(String dataExpand) {
        this.dataExpand = dataExpand;
    }

    public Date getJoinCategoryTime() {
        return joinCategoryTime;
    }

    public void setJoinCategoryTime(Date joinCategoryTime) {
        this.joinCategoryTime = joinCategoryTime;
    }

    public String getUserId() {
            return userId;
            }

    public BCustomerInformation setUserId(String userId) {
        this.userId = userId;
        return this;
    }

    public Long getbCategoryId() {
            return bCategoryId;
            }

    public BCustomerInformation setbCategoryId(Long bCategoryId) {
        this.bCategoryId = bCategoryId;
        return this;
    }

    public Long getJoinCategoryReasonId() {
            return joinCategoryReasonId;
            }

    public BCustomerInformation setJoinCategoryReasonId(Long joinCategoryReasonId) {
        this.joinCategoryReasonId = joinCategoryReasonId;
        return this;
    }

    public String getJoinCategoryReason() {
            return joinCategoryReason;
            }

    public BCustomerInformation setJoinCategoryReason(String joinCategoryReason) {
        this.joinCategoryReason = joinCategoryReason;
        return this;
    }

    public Date getCreateTime() {
            return createTime;
            }

    public BCustomerInformation setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
            return updateTime;
            }

    public BCustomerInformation setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return "BCustomerInformationModel{" +
                "userId=" + userId +
                ", bCategoryId=" + bCategoryId +
                ", joinCategoryReasonId=" + joinCategoryReasonId +
                ", joinCategoryReason=" + joinCategoryReason +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
        "}";
    }
}

package org.springcenter.box.activity.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.killbill.CreatorName;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.BusEvent;
import org.springcenter.box.activity.domain.box.BusEvents;
import org.springcenter.box.activity.mapper.box.BusEventsMapper;
import org.springcenter.box.activity.service.IBusEventsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @auther yuanxiaozhong
 * @create 2022-03-22 15:24:26
 * @describe 事件表服务实现类
 */
@Service
@Slf4j
public class BusEventsServiceImpl extends ServiceImpl<BusEventsMapper, BusEvents> implements IBusEventsService {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Override
    public void createBusEvent(BusEvent event) {
        String json;
        try {
            json = persistentBus.getObjectMapper().writeValueAsString(event);
        } catch (JsonProcessingException var6) {
            return;
        }
        BusEvents entry = new BusEvents(CreatorName.get(), persistentBus.getClock().getUTCNow().toDate(), event.getClass().getName(), json, event.getUserToken().toString(), event.getSearchKey1(), event.getSearchKey2());
        this.save(entry);
    }
}

package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-10-10 10:13:18
 * @Description: 
 */
@TableName("LOGISTICS")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="Logistics对象", description="")
public class Logistics implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "box服务单id")
    @TableField("BOX_ID")
    private String boxId;
    @ApiModelProperty(value = "box还货单id")
    @TableField("BOX_RETURN_ID")
    private String boxReturnId;
    @ApiModelProperty(value = "物流单号")
    @TableField("LOG_SN")
    private String logSn;
    @ApiModelProperty(value = "物流公司")
    @TableField("LOG_NAME")
    private String logName;
    @ApiModelProperty(value = "收件人姓名")
    @TableField("NAME")
    private String name;
    @ApiModelProperty(value = "收件人手机")
    @TableField("PHONE")
    private String phone;
    @ApiModelProperty(value = "收件人邮编")
    @TableField("CODE")
    private String code;
    @ApiModelProperty(value = "省")
    @TableField("PROVINCE")
    private String province;
    @ApiModelProperty(value = "市")
    @TableField("CITY")
    private String city;
    @ApiModelProperty(value = "区")
    @TableField("DISTRICT")
    private String district;
    @ApiModelProperty(value = "地址")
    @TableField("ADDRESS")
    private String address;
    @ApiModelProperty(value = "状态(0:未发货;1:已发货;2:已签收;3:退货中;4:已退货)")
    @TableField("STATUS")
    private Long status;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "取件时间")
    @TableField("GET_DATE")
    private String getDate;
    @ApiModelProperty(value = "用户物流信息id")
    @TableField("CUSTOMER_LOGISTICS_ID")
    private String customerLogisticsId;
    @ApiModelProperty(value = "0-box发货物流 1-box还货物流 2-box退货物流  3 手动预约  4 售后单")
    @TableField("TYPE")
    private Long type;
    @ApiModelProperty(value = "box退货单id")
    @TableField("REFUND_ID")
    private String refundId;
    @ApiModelProperty(value = "快递员工号")
    @TableField("EMP_CODE")
    private String empCode;
    @ApiModelProperty(value = "快递员手机号")
    @TableField("EMP_PHONE")
    private String empPhone;
    @ApiModelProperty(value = "操作方式：0自提，1预约快递，2自行寄回")
    @TableField("SEND_TYPE")
    private BigDecimal sendType;


    public String getId() {
        return id;
    }

    public Logistics setId(String id) {
        this.id = id;
        return this;
    }

    public String getBoxId() {
        return boxId;
    }

    public Logistics setBoxId(String boxId) {
        this.boxId = boxId;
        return this;
    }

    public String getBoxReturnId() {
        return boxReturnId;
    }

    public Logistics setBoxReturnId(String boxReturnId) {
        this.boxReturnId = boxReturnId;
        return this;
    }

    public String getLogSn() {
        return logSn;
    }

    public Logistics setLogSn(String logSn) {
        this.logSn = logSn;
        return this;
    }

    public String getLogName() {
        return logName;
    }

    public Logistics setLogName(String logName) {
        this.logName = logName;
        return this;
    }

    public String getName() {
        return name;
    }

    public Logistics setName(String name) {
        this.name = name;
        return this;
    }

    public String getPhone() {
        return phone;
    }

    public Logistics setPhone(String phone) {
        this.phone = phone;
        return this;
    }

    public String getCode() {
        return code;
    }

    public Logistics setCode(String code) {
        this.code = code;
        return this;
    }

    public String getProvince() {
        return province;
    }

    public Logistics setProvince(String province) {
        this.province = province;
        return this;
    }

    public String getCity() {
        return city;
    }

    public Logistics setCity(String city) {
        this.city = city;
        return this;
    }

    public String getDistrict() {
        return district;
    }

    public Logistics setDistrict(String district) {
        this.district = district;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public Logistics setAddress(String address) {
        this.address = address;
        return this;
    }

    public Long getStatus() {
        return status;
    }

    public Logistics setStatus(Long status) {
        this.status = status;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public Logistics setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public Logistics setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public String getGetDate() {
        return getDate;
    }

    public Logistics setGetDate(String getDate) {
        this.getDate = getDate;
        return this;
    }

    public String getCustomerLogisticsId() {
        return customerLogisticsId;
    }

    public Logistics setCustomerLogisticsId(String customerLogisticsId) {
        this.customerLogisticsId = customerLogisticsId;
        return this;
    }

    public Long getType() {
        return type;
    }

    public Logistics setType(Long type) {
        this.type = type;
        return this;
    }

    public String getRefundId() {
        return refundId;
    }

    public Logistics setRefundId(String refundId) {
        this.refundId = refundId;
        return this;
    }

    public String getEmpCode() {
        return empCode;
    }

    public Logistics setEmpCode(String empCode) {
        this.empCode = empCode;
        return this;
    }

    public String getEmpPhone() {
        return empPhone;
    }

    public Logistics setEmpPhone(String empPhone) {
        this.empPhone = empPhone;
        return this;
    }

    public BigDecimal getSendType() {
        return sendType;
    }

    public Logistics setSendType(BigDecimal sendType) {
        this.sendType = sendType;
        return this;
    }

    @Override
    public String toString() {
        return "LogisticsModel{" +
            "id=" + id +
            ", boxId=" + boxId +
            ", boxReturnId=" + boxReturnId +
            ", logSn=" + logSn +
            ", logName=" + logName +
            ", name=" + name +
            ", phone=" + phone +
            ", code=" + code +
            ", province=" + province +
            ", city=" + city +
            ", district=" + district +
            ", address=" + address +
            ", status=" + status +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", getDate=" + getDate +
            ", customerLogisticsId=" + customerLogisticsId +
            ", type=" + type +
            ", refundId=" + refundId +
            ", empCode=" + empCode +
            ", empPhone=" + empPhone +
            ", sendType=" + sendType +
            "}";
    }
}

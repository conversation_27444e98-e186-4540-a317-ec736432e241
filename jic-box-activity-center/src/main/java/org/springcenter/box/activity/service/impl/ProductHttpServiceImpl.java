package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.service.IProductHttpService;
import org.springcenter.product.api.IProductPackageApi;
import org.springcenter.product.api.dto.background.SearchProductIdInPacReq;
import org.springcenter.product.api.dto.background.SearchProductIdIsExistResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductHttpServiceImpl implements IProductHttpService {
    @Resource
    private IProductPackageApi iProductPackageApi;

    public List<SearchProductIdIsExistResp> judgeProductInPackage(List<String> pacIds, List<Long> productIds) {
        CommonRequest<SearchProductIdInPacReq> request = new CommonRequest<>();
        SearchProductIdInPacReq req = new SearchProductIdInPacReq();
        req.setPacIds(pacIds);
        req.setProductIds(productIds);
        request.setRequestData(req);
        log.info("检查商品包, request={}", JSON.toJSONString(request));
        ResponseResult<List<SearchProductIdIsExistResp>> responseResult = null;
        try {
            responseResult = iProductPackageApi.getPackageIsExistProductIdsForc(request);
            log.info("检查商品包, response={}", JSON.toJSONString(responseResult));
        } catch (Exception e) {
            log.error("检查商品包 异常", e);
            throw new BoxActivityException("商品包服务异常");
        }
        if (responseResult != null && responseResult.getData() != null && !responseResult.getData().isEmpty()) {
            return responseResult.getData();
        } else {
            return null;
        }
    }

    @Override
    public Map<Long, Integer> getProductId2ExistMap(List<String> pacIds, List<Long> productIds) {
        List<SearchProductIdIsExistResp> productRsp = judgeProductInPackage(pacIds, productIds);
        if (CollectionUtils.isEmpty(productRsp)) {
            throw new BoxActivityException("需要过滤商品，但是商品服务异常");
        }
        // 商品ID-是否存在
        Map<Long, Integer> productId2Exist = productRsp.stream().collect(
                Collectors.toMap(SearchProductIdIsExistResp::getProductId, SearchProductIdIsExistResp::getIsExist));
        return productId2Exist;
    }
}

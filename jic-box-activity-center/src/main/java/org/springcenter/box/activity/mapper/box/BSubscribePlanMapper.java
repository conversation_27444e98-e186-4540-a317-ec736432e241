package org.springcenter.box.activity.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.box.activity.domain.box.BSubscribePlan;

import java.util.Date;
import java.util.List;


/**
 * @Author: lwz
 * @Date: 2023-09-07 17:01:45
 * @Description: Mapper
 */
public interface BSubscribePlanMapper extends BaseMapper<BSubscribePlan> {
    /**
     * 获取未完结订阅节点
     */
    List<BSubscribePlan> getUnCompleteSubscribePlanListBySubIds(@Param("subIds") List<String> subIds);
}

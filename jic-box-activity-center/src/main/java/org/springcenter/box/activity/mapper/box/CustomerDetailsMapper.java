package org.springcenter.box.activity.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springcenter.box.activity.api.dto.SalesCustomerListResp;
import org.springcenter.box.activity.api.dto.SalesCustomerReq;
import org.springcenter.box.activity.domain.box.CustomerDetails;

import java.util.List;


/**
 * @Author: CodeGenerator
 * @Date: 2024-10-11 13:44:16
 * @Description: Mapper
 */
public interface CustomerDetailsMapper extends BaseMapper<CustomerDetails> {

    /**
     * 查询导购订阅周期内的会员列表
     * @param salesCustomerReq
     * @return
     */
    List<SalesCustomerListResp> salesSubscribeCustomerList(SalesCustomerReq salesCustomerReq);

}

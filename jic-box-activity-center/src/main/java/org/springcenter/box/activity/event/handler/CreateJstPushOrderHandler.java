package org.springcenter.box.activity.event.handler;


import com.alibaba.fastjson.JSONObject;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.event.CreateJstPushOrderEvent;
import org.springcenter.box.activity.service.ICombineJstPushOrderService;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CreateJstPushOrderHandler {

    @Resource
    private ICombineJstPushOrderService iCombineJstPushOrderService;


    @AllowConcurrentEvents
    @Subscribe
    public void processMyEvent(final CreateJstPushOrderEvent event) {
        log.info("创建聚水潭推送订单userToken:{} event:{}", event.getUserToken(), JSONObject.toJSONString(event));
        iCombineJstPushOrderService.createJstPushOrder(event.getOrderId());
    }


}

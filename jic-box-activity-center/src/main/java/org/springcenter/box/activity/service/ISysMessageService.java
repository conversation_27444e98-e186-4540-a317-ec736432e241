package org.springcenter.box.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;

import com.jnbyframework.boot.common.api.dto.message.TemplateMessageDTO;
import org.springcenter.box.activity.domain.box.SysMessage;



/**
 * @Description: 消息
 * @Author: jeecg-boot
 * @Date:  2019-04-09
 * @Version: V1.0
 */
public interface ISysMessageService extends IService<SysMessage> {

    /**
     * 通过模版发送消息
     * @param message
     */
    String sendMessageByTemplate(TemplateMessageDTO message);
}

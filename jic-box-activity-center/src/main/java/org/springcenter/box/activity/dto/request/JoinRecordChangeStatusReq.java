package org.springcenter.box.activity.dto.request;

import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel("修改状态入参")
@Data
public class JoinRecordChangeStatusReq {
    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空")
    private String optPerson;
    @ApiModelProperty(value = "操作备注", required = true)
    @NotBlank(message = "操作备注不能为空")
    private String optRemark;
    @ApiModelProperty(value = "记录ID", required = true)
    @NotBlank(message = "记录ID不能为空")
    private String id;
    @ApiModelProperty(value = "操作：0=不发,1=可发", required = true)
    @NotNull(message = "操作不能为空")
    private Integer status;
    public void check() {
        Preconditions.checkArgument(StringUtils.isNotBlank(id), "记录ID不能为空");
    }
}

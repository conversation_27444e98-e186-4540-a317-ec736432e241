package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.util.QiniuUtil;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.api.dto.JoinRecordInfoResp;
import org.springcenter.box.activity.api.dto.JoinRecordQueryReq;
import org.springcenter.box.activity.api.dto.JoinRecordQueryResp;
import org.springcenter.box.activity.config.IdConfig;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.convert.JoinRecordConvertor;
import org.springcenter.box.activity.domain.box.*;
import org.springcenter.box.activity.dto.JoinRecordExchangeGiftDto;
import org.springcenter.box.activity.dto.LogDto;
import org.springcenter.box.activity.dto.OrderDto;
import org.springcenter.box.activity.dto.request.*;
import org.springcenter.box.activity.dto.response.ExportJoinRecordExcelDto;
import org.springcenter.box.activity.enums.*;
import org.springcenter.box.activity.mapper.box.BoxMapper;
import org.springcenter.box.activity.mapper.box.CActivityJoinRecordMapper;
import org.springcenter.box.activity.mapper.box.LogisticsMapper;
import org.springcenter.box.activity.service.*;
import org.springcenter.box.activity.util.DateUtil;
import org.springcenter.box.activity.util.FileUtil;
import org.springcenter.retail.api.dto.resp.QueryOrder2JSTRespDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.springcenter.box.activity.mq.FileAnalysisListener.TAG_EXPORT_C_ACTIVITY_JOIN_RECORD;

@Slf4j
@Service
public class JoinRecordServiceImpl extends ServiceImpl<CActivityJoinRecordMapper, CActivityJoinRecord> implements IJoinRecordService {
    @Resource(name = "boxTransactionTemplate")
    private TransactionTemplate template;
    @Resource
    private CActivityJoinRecordMapper joinRecordMapper;
    @Resource
    private JoinRecordConvertor joinRecordConvertor;
    @Resource
    private ICActivityService activityService;
    @Resource
    private IOrderService orderService;
    @Resource
    private BoxMapper boxMapper;
    @Resource
    private LogisticsMapper logisticsMapper;
    @Resource
    private IdConfig idConfig;
    @Resource
    private QiniuUtil qiniuUtil;
    @Resource
    private IExpressHttpService expressHttpService;
    @Resource
    private ILogService logService;
    @Resource
    private IGiftService giftService;

    @Override
    public List<JoinRecordInfoResp> listJoinRecord(JoinRecordListReq requestData, Page page) {
        log.info("明细列表,入参:{}", JSON.toJSONString(requestData));
        LambdaQueryWrapper<CActivityJoinRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CActivityJoinRecord::getIsDelete, 0);
        queryWrapper.eq(StringUtils.isNotBlank(requestData.getActivityId()), CActivityJoinRecord::getActivityId, requestData.getActivityId());
        queryWrapper.eq(StringUtils.isNotBlank(requestData.getBoxSn()), CActivityJoinRecord::getBoxSn, requestData.getBoxSn());
        queryWrapper.eq(StringUtils.isNotBlank(requestData.getBoxId()), CActivityJoinRecord::getBoxId, requestData.getBoxId());
        queryWrapper.eq(StringUtils.isNotBlank(requestData.getLogisticsNo()), CActivityJoinRecord::getLogisticsNo, requestData.getLogisticsNo());
        // 可发状态
        queryWrapper.eq(requestData.getAccomplishStatus() != null,
                CActivityJoinRecord::getAccomplishStatus, requestData.getAccomplishStatus());
        // 搭配师
        queryWrapper.eq(StringUtils.isNotBlank(requestData.getFashionerId()),
                CActivityJoinRecord::getFashionerId, requestData.getFashionerId());
        // 待发放
        queryWrapper.eq(requestData.getGiftSendStatus() != null,
                CActivityJoinRecord::getGiftSendStatus, requestData.getGiftSendStatus());
        // TODO 服务单后台查询仅包含boxSnList
        if (CollectionUtils.isNotEmpty(requestData.getBoxSnList())) {
            queryWrapper.in(CActivityJoinRecord::getBoxSn, requestData.getBoxSnList());
        }
        if (CollectionUtils.isNotEmpty(requestData.getBoxIdList())) {
            queryWrapper.in(CActivityJoinRecord::getBoxId, requestData.getBoxIdList());
        }
        queryWrapper.eq(StringUtils.isNotBlank(requestData.getUnionId()), CActivityJoinRecord::getUnionId, requestData.getUnionId());
        // 如果时间指定，则代表小程序来的，需要查询指定时间之后的数据  CREATE_TIME>=
        queryWrapper.ge(requestData.getIsolationTime() != null, CActivityJoinRecord::getCreateTime, requestData.getIsolationTime());

        com.github.pagehelper.Page<CActivityJoinRecord> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<CActivityJoinRecord> joinRecordList = joinRecordMapper.selectList(queryWrapper);
        log.info("明细列表,DB数据:{}", JSON.toJSONString(joinRecordList));

        List<JoinRecordInfoResp> respList = Optional.ofNullable(joinRecordList).orElse(Lists.newArrayList()).stream()
                .map(joinRecordConvertor::domain2Resp).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(respList)) {
            return respList;
        }
        // 查询地址
        if (Boolean.TRUE.equals(requestData.getNeedQueryLogistics())) {
            List<String> logisticsNoList = respList.stream().map(JoinRecordInfoResp::getAddressId).collect(Collectors.toList());
            Map<String, CustomerLogistics> stringCustomerLogisticsMap = expressHttpService.listLogisticsInfo(logisticsNoList);
            for (JoinRecordInfoResp joinRecordInfoResp : respList) {
                // 如果地址为空，需要从数据库里取值
                if (StringUtils.isBlank(joinRecordInfoResp.getAddressProvince())) {
                    CustomerLogistics customerLogistics = stringCustomerLogisticsMap.get(joinRecordInfoResp.getAddressId());
                    if (customerLogistics != null) {
                        joinRecordInfoResp.setAddressProvince(customerLogistics.getProvince());
                        joinRecordInfoResp.setAddressCity(customerLogistics.getCity());
                        joinRecordInfoResp.setAddressDistrict(customerLogistics.getDistrict());
                        joinRecordInfoResp.setAddress(customerLogistics.getAddress());
                    }
                }
            }
        }

        PageInfo<JoinRecordInfoResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(respList);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    @Override
    public void joinRecordChangeAddress(JoinRecordChangeAddressReq requestData) {
        CActivityJoinRecord joinRecord = this.getById(requestData.getId());
        if (joinRecord == null) {
            throw new BoxActivityException("参与记录不存在");
        }
        // 活动不是待发奖状态，不可操作
        CActivityInfoResp activityInfo = activityService.activityInfo(joinRecord.getActivityId(), false);
        if (!ActivityGiftSendTaskStatusEnum.isWaitCalc(activityInfo.getGiftSendTaskStatus())) {
            throw new BoxActivityException("赠品正在操作发放，不可再修改地址");
        }
        CActivityJoinRecord record = new CActivityJoinRecord();
        record.setId(requestData.getId());
        record.setAddressId(requestData.getAddressId());
        record.setUpdateTime(new Date());
        record.setOptPerson(requestData.getOptPerson());
        template.execute(action -> {
            this.updateById(record);
            logService.saveLog(joinRecord.getActivityId(), OptTypeEnum.JOIN_RECORD_UPDATE_ADDRESS, requestData.getOptPerson(), new LogDto(joinRecord), new LogDto(record), requestData.getId());
            return true;
        });
    }

    @Override
    public void joinRecordChangeStatus(JoinRecordChangeStatusReq requestData) {
        CActivityJoinRecord joinRecord = this.getById(requestData.getId());
        if (joinRecord == null) {
            throw new BoxActivityException("参与记录不存在");
        }
        // 活动不是待发奖状态，不可操作
        CActivityInfoResp activityInfo = activityService.activityInfo(joinRecord.getActivityId(), false);
        if (!FrontActivityStatusEnum.canSend(activityInfo.getStatus())) {
            throw new BoxActivityException("活动不是待发奖状态，不可操作");
        }
        Integer optStatus = requestData.getStatus();
        OptTypeEnum optTypeEnum = null;
        if (optStatus == 0) {
            optTypeEnum = OptTypeEnum.JOIN_RECORD_NOT_ALLOW_SEND;
            if (!AccomplishStatusEnum.isAccomplish(joinRecord.getAccomplishStatus())) {
                throw new BoxActivityException("当前记录不是可发放状态，无法操作不发，请刷新页面确认");
            }
        }
        if (optStatus == 1) {
            optTypeEnum = OptTypeEnum.JOIN_RECORD_ALLOW_SEND;
            if (AccomplishStatusEnum.isAccomplish(joinRecord.getAccomplishStatus())) {
                throw new BoxActivityException("当前记录不是不可发放状态，无法操作可发，请刷新页面确认");
            }
        }
        CActivityJoinRecord record = new CActivityJoinRecord();
        record.setId(requestData.getId());
        record.setAccomplishStatus(requestData.getStatus());
        record.setUpdateTime(new Date());
        record.setOptPerson(requestData.getOptPerson());
        record.setOptRemark(requestData.getOptRemark());
        record.setOptTime(new Date());
        OptTypeEnum finalOptTypeEnum = optTypeEnum;
        template.execute(action -> {
            this.updateById(record);
            // 0=不发,1=可发
            if (optStatus == 1) {
                giftService.deductInventory(joinRecord.getGiftId());
            } else if (optStatus == 0) {
                giftService.returnInventory(joinRecord.getGiftId());
            }
            logService.saveLog(joinRecord.getActivityId(), finalOptTypeEnum, requestData.getOptPerson(), new LogDto(joinRecord), new LogDto(record), requestData.getId());
            return true;
        });
    }

    @Override
    public void joinRecordCreated(JoinRecordAddReq requestData) {
        // 根据box单号查询box
        LambdaQueryWrapper<Box> boxQuery = new LambdaQueryWrapper<>();
        boxQuery.eq(Box::getBoxSn, requestData.getBoxSn());
        Box box = boxMapper.selectOne(boxQuery);
        if (box == null) {
            throw new BoxActivityException("BOX单不存在，无法添加");
        }
        // 根据活动ID查询活动
        CActivityInfoResp activity = activityService.activityInfo(requestData.getActivityId(), false);
        if (activity == null) {
            throw new BoxActivityException("活动不存在，无法添加");
        }
        // 验证BOX单是否已添加
        LambdaQueryWrapper<CActivityJoinRecord> joinRecordQuery = new LambdaQueryWrapper<>();
        joinRecordQuery.eq(CActivityJoinRecord::getBoxSn, requestData.getBoxSn());
        joinRecordQuery.eq(CActivityJoinRecord::getActivityId, requestData.getActivityId());
        joinRecordQuery.eq(CActivityJoinRecord::getIsDelete, 0);
        CActivityJoinRecord joinRecord = joinRecordMapper.selectOne(joinRecordQuery);
        if (joinRecord != null) {
            throw new BoxActivityException("BOX单单号已存在，无法添加");
        }
        // 根据box单查询所有DD单和实付金额
        OrderReq orderReq = new OrderReq();
        orderReq.setBoxSn(requestData.getBoxSn());
        orderReq.setCreateStartTime(box.getCreateTime());
        orderReq.setCreateEndTime(new Date());
        if (ItemRangeEnum.isFilterItemAndAccomplish(activity.getBoxBrandType())) {
            // 获取商品包
            orderReq.setPacIds(Splitter.on(",").splitToList(activity.getBoxBrandList()));
            orderReq.setNeedFilterItem(true);
        }
        List<OrderDto> orderDtoList = orderService.listOrderDto(orderReq);
        if (CollectionUtils.isEmpty(orderDtoList)) {
            throw new BoxActivityException("该BOX单下没有付款成功的订单");
        }
        BigDecimal payment = orderDtoList.stream().map(OrderDto::getPayment).reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer quantity = orderDtoList.stream().map(OrderDto::getQuantity).reduce(0, Integer::sum);
        if (payment.compareTo(BigDecimal.ZERO) <= 0 || quantity <= 0) {
            throw new BoxActivityException("该BOX单下没有实付金额或实付件数");
        }
        String orderNoList = StringUtils.join(orderDtoList.stream().map(OrderDto::getOrderSn).collect(Collectors.toList()), ",");
        CActivityJoinRecord record = buildJoinRecord(AccomplishStatusEnum.ACCOMPLISH, box, activity, orderNoList, payment, quantity);
        record.setOptRemark(requestData.getOptRemark());
        record.setOptPerson(requestData.getOptPerson());
        log.info("人工添加参与记录：{}", JSON.toJSONString(record));
        template.execute(action -> {
            boolean saveResult = this.save(record);
            giftService.deductInventory(activity.getGift().getId());
            logService.saveLog(activity.getId(), OptTypeEnum.JOIN_RECORD_MANUAL_CREATE, requestData.getOptPerson(), null, new LogDto(record), record.getId());
            log.info("人工添加参与记录是否成功:{}", saveResult);
            return true;
        });
    }

    @Override
    public void joinRecordCancel(String activityId, String optPerson) {
        Preconditions.checkArgument(StringUtils.isNotBlank(activityId), "活动id不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(optPerson), "操作人不能为空");
        CActivityJoinRecord updateRecord = new CActivityJoinRecord();
        updateRecord.setIsDelete(1);
        updateRecord.setUpdateTime(new Date());
        updateRecord.setOptRemark("活动作废关联删除");
        updateRecord.setOptPerson(optPerson);
        updateRecord.setOptTime(new Date());
        LambdaQueryWrapper<CActivityJoinRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CActivityJoinRecord::getActivityId, activityId);
        int updated = joinRecordMapper.update(updateRecord, queryWrapper);
        log.info("活动作废 关联删除参与记录[{}]条，活动id[{}]", updated, activityId);
    }

    @Override
    public JoinRecordQueryResp queryJoinRecordByBoxSn(JoinRecordQueryReq requestData, Boolean miniApp) {
        String boxSn = requestData.getBoxSn();
        String boxId = requestData.getBoxId();
        String unionId = requestData.getUnionId();
        JoinRecordListReq req = new JoinRecordListReq();
        req.setBoxId(boxId);
        req.setUnionId(unionId);
        req.setBoxSn(boxSn);
        if (miniApp) {
            req.setIsolationTime(DateUtil.getStrToTime("2025-03-27 18:00:00"));
        }
        List<JoinRecordInfoResp> joinRecordInfoResps = listJoinRecord(req, new Page(1, 10));
        if (CollectionUtils.isEmpty(joinRecordInfoResps) || joinRecordInfoResps.get(0) == null) {
            log.info("根据box单号查询参与记录为空，不处理");
            return null;
        }
        JoinRecordInfoResp joinRecord = joinRecordInfoResps.get(0);

        // 根据达成和未达成状态判断错误原因
        if (!AccomplishStatusEnum.isAccomplish(joinRecord.getAccomplishStatus())) {
            log.info("当前参与记录未达成，判断是否人工操作");
            if (StringUtils.isNotBlank(joinRecord.getOptRemark())) {
                log.info("存在人工操作，替换错误原因。系统错误原因:[{}]，人工操作的备注:[{}]", joinRecord.getGiftSendErrorMsg(), joinRecord.getOptRemark());
                joinRecord.setGiftSendErrorMsg(joinRecord.getOptRemark());
            } else {
                log.info("已退款，不满足活动门槛，赠品无法发放。");
                joinRecord.setGiftSendErrorMsg("已退款，不满足活动门槛，赠品无法发放。");
            }
        }

        JoinRecordQueryResp rsp = new JoinRecordQueryResp();
        rsp.setJoinRecord(joinRecord);

        String activityId = joinRecord.getActivityId();
        CActivityInfoResp activityInfo = activityService.activityInfo(activityId, false);
        rsp.setActivityInfo(activityInfo);
        // 兑换卡只有小程序端支持查看
        if (!Boolean.TRUE.equals(miniApp)
                && activityInfo.getGift() != null
                && GiftTypeEnum.isExchangeGift(activityInfo.getGift().getGiftType())) {
            rsp.getJoinRecord().setExchangeUrl(null);
            rsp.getJoinRecord().setExchangeNumber(null);
            rsp.getJoinRecord().setExchangeSecret(null);
        }
        return rsp;
    }

    @Override
    public String exportJoinRecord(JoinRecordListReq req) {
        List<ExportJoinRecordExcelDto> rspList = new ArrayList<>();

        // 分页获取达成的记录
        Page page = new Page(1, 100);
        req.setAccomplishStatus(AccomplishStatusEnum.ACCOMPLISH.getCode());
        List<JoinRecordInfoResp> joinRecordInfoResps = listJoinRecord(req, page);
        if (CollectionUtils.isEmpty(joinRecordInfoResps)) {
            throw new BoxActivityException("没有达成的数据，无法处理");
        }
        int pages = page.getPages();
        for (int i = 1; i <= pages; i++) {
            if (i > 1) {
                page.setPageNo(i);
                joinRecordInfoResps = listJoinRecord(req, page);
                if (CollectionUtils.isEmpty(joinRecordInfoResps)) {
                    break;
                }
            }
            for (JoinRecordInfoResp joinRecord : joinRecordInfoResps) {
                rspList.add(joinRecordConvertor.joinRecord2ExportDto(joinRecord));
            }
        }
        if (CollectionUtils.isEmpty(rspList)) {
            throw new BoxActivityException("没有达成的数据，无法处理");
        }

        String fileName = TAG_EXPORT_C_ACTIVITY_JOIN_RECORD + "_" + System.currentTimeMillis() + ".xlsx";
        // 理论上这个数据不大，一次性写入没啥问题
        EasyExcelUtil.write(fileName, ExportJoinRecordExcelDto.class, () -> rspList);
        log.info("写入数据在本地文件:{}", fileName);
        File file = new File(fileName);
        String exportUrl = qiniuUtil.upload(file.getPath(), fileName);
        log.info("上传文件至七牛，返回结果:{}", exportUrl);
        file.delete();
        return exportUrl;
    }

    @Override
    public CActivityJoinRecord buildJoinRecord(AccomplishStatusEnum accomplish, Box box, CActivityInfoResp activity, String orderNoList, BigDecimal payment, Integer quantity) {
        CActivityJoinRecord joinRecord;
        joinRecord = new CActivityJoinRecord();
        joinRecord.setId(idConfig.getJoinRecordId());
        joinRecord.setIsDelete(0);
        joinRecord.setActivityId(activity.getId());
        joinRecord.setBoxSn(box.getBoxSn());
        joinRecord.setBoxId(box.getId());
        joinRecord.setCreateTime(new Date());
        joinRecord.setUpdateTime(new Date());
        joinRecord.setAccomplishStatus(accomplish.getCode());
        joinRecord.setGiftId(activity.getGift().getId());
        joinRecord.setGiftSendStatus(GiftSendStatusEnum.UN_SEND.getCode());
        joinRecord.setFashionerId(box.getCreateFasId());
        joinRecord.setUnionId(box.getUnionid());
        joinRecord.setPayment(payment);
        joinRecord.setQuantity(quantity);
        joinRecord.setOrderNoList(orderNoList);
        String addressId = null;
        Logistics logistics = logisticsMapper.selectById(box.getLogisticsId());
        log.info("搭盒物流信息:{}", JSON.toJSONString(logistics));
        if (logistics != null && StringUtils.isNotBlank(logistics.getCustomerLogisticsId())) {
            addressId = logistics.getCustomerLogisticsId();
        }
        joinRecord.setAddressId(addressId);
        log.info("初始化参与记录:{}", JSON.toJSONString(joinRecord));
        return joinRecord;
    }

    @Override
    public Boolean doDelivery(CActivity activity, CActivityGift gift) {
        JoinRecordListReq req = new JoinRecordListReq();
        req.setActivityId(activity.getId());
        req.setAccomplishStatus(AccomplishStatusEnum.ACCOMPLISH.getCode());
        req.setGiftSendStatus(GiftSendStatusEnum.UN_SEND.getCode());
        // 分页获取记录循环处理
        Page page = new Page(1, 20);
        List<JoinRecordInfoResp> joinRecordInfoResps = listJoinRecord(req, page);
        if (CollectionUtils.isEmpty(joinRecordInfoResps)) {
            log.info("没有可发状态的参与记录，本轮调度执行结束，活动状态更新为结束");
            return false;
        }
        do {
            for (JoinRecordInfoResp joinRecord : joinRecordInfoResps) {
                expressHttpService.sendExpress(gift, joinRecord);
            }
            joinRecordInfoResps = listJoinRecord(req, page);
        } while (CollectionUtils.isNotEmpty(joinRecordInfoResps));
        return true;
    }


    @Override
    public void deliveryCallbackJob() {
        JoinRecordListReq req = new JoinRecordListReq();
        req.setGiftSendStatus(GiftSendStatusEnum.WAIT_RESULT.getCode());
        // 分页获取记录循环处理
        Page page = new Page(1, 20);
        List<JoinRecordInfoResp> joinRecordInfoResps = listJoinRecord(req, page);
        if (CollectionUtils.isEmpty(joinRecordInfoResps)) {
            log.info("没有等待结果状态的参与记录，本轮调度执行结束.");
            return;
        }
        int pages = page.getPages();
        for (int i = 1; i <= pages; i++) {
            if (i > 1) {
                page.setPageNo(i);
                joinRecordInfoResps = listJoinRecord(req, page);
                if (CollectionUtils.isEmpty(joinRecordInfoResps)) {
                    break;
                }
            }
            List<QueryOrder2JSTRespDTO> jstInfo = expressHttpService.queryExpress(joinRecordInfoResps);
            if (CollectionUtils.isNotEmpty(jstInfo)) {
                Map<String, List<QueryOrder2JSTRespDTO>> groupBySourceCode = jstInfo.stream().collect(Collectors.groupingBy(r -> r.getSoId()));
                for (JoinRecordInfoResp joinRecord : joinRecordInfoResps) {
                    List<QueryOrder2JSTRespDTO> materialBySourceCodesContexts = groupBySourceCode.get(joinRecord.getSoId());
                    if (CollectionUtils.isNotEmpty(materialBySourceCodesContexts) && StringUtils.isNotBlank(materialBySourceCodesContexts.get(0).getLId())) {
                        CActivityJoinRecord updateRecord = new CActivityJoinRecord();
                        updateRecord.setId(joinRecord.getId());
                        updateRecord.setUpdateTime(new Date());
                        updateRecord.setLogisticsNo(materialBySourceCodesContexts.get(0).getLId());
                        updateRecord.setLogistics(materialBySourceCodesContexts.get(0).getLogisticsCompany());
                        updateRecord.setGiftSendStatus(GiftSendStatusEnum.SUCCESS.getCode());
                        log.info("回查到状态变更，更新参与记录的物流单号:{}", JSONObject.toJSONString(updateRecord));
                        joinRecordMapper.updateById(updateRecord);
                    }
                }
            }
        }
    }

    @Override
    public ResponseResult importExchangeGift(JoinRecordImportExcelReq req) {
        String url = req.getExcelUrl();
        String activityId = req.getActivityId();
        String optPerson = req.getOptPerson();
        Preconditions.checkArgument(StringUtils.isNotBlank(url), "文件地址不能为空，请刷新后重新上传");
        Preconditions.checkArgument(StringUtils.isNotBlank(activityId), "活动ID不能为空，请刷新后重新上传");
        Preconditions.checkArgument(StringUtils.isNotBlank(optPerson), "操作人不能为空，请刷新后重新上传");
        // 活动校验
        CActivityInfoResp activity = activityService.activityInfo(activityId, false);
        Preconditions.checkArgument(activity != null, "活动不存在，请刷新后重新上传");
        Preconditions.checkArgument(FrontActivityStatusEnum.canSend(activity.getStatus()), "活动已发放，无法再次发放");
        Preconditions.checkArgument(GiftTypeEnum.isExchangeGift(activity.getGift().getGiftType()), "赠品类型不是兑换卡，不可操作");
        // 下载
        String filePath = FileUtil.downLoadExcel(url);
        // 解析
        List<JoinRecordExchangeGiftDto> excelDataList = parseFile(filePath);
        log.info("解析文件数据:{}", JSON.toJSONString(excelDataList));
        // 数据比对
        List<CActivityJoinRecord> joinRecordList = getJoinRecordList(excelDataList, activityId);
        // 真实发放
        List<CActivityJoinRecord> updateList = buildUpdateList(joinRecordList, excelDataList, activity);
        template.execute(action -> {
            giftService.batchSend(activity.getGift().getGiftType(), updateList);
            activityService.activityEndByWaitSend(activity.getId(), optPerson);
            return true;
        });
        return ResponseResult.success();
    }

    private static List<JoinRecordExchangeGiftDto> parseFile(String filePath) {
        List<JoinRecordExchangeGiftDto> list = Lists.newArrayList();
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {
            @Override
            protected void saveData() {
                for (Map<Integer, String> map : this.cachedDataList) {
                    JoinRecordExchangeGiftDto dto = new JoinRecordExchangeGiftDto();
                    dto.setBoxSn(map.get(0));
                    if (StringUtils.isBlank(dto.getBoxSn())) {
                        continue;
                    }
                    dto.setExchangeNumber(map.get(4));
                    dto.setExchangeSecret(map.get(5));
                    if (StringUtils.isBlank(dto.getExchangeNumber()) && StringUtils.isBlank(dto.getExchangeSecret())) {
                        throw new BoxActivityException("卡号卡密必填其一，不可同时为空，请检查后重新上传");
                    }
                    list.add(dto);
                }
            }
        });
        return list;
    }

    private List<CActivityJoinRecord> getJoinRecordList(List<JoinRecordExchangeGiftDto> list, String activityId) {
        if (list.size() == 0 || list.size() > 1000) {
            throw new BoxActivityException("只能上传1-1000条数据，请检查后重新上传");
        }
        List<String> boxSnList = list.stream().map(JoinRecordExchangeGiftDto::getBoxSn).distinct().collect(Collectors.toList());
        if (list.size() != boxSnList.size()) {
            throw new BoxActivityException("上传数据存在重复单号，请检查后重新上传");
        }
        LambdaQueryWrapper<CActivityJoinRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CActivityJoinRecord::getIsDelete, 0);
        queryWrapper.eq(CActivityJoinRecord::getActivityId, activityId);
        queryWrapper.eq(CActivityJoinRecord::getAccomplishStatus, AccomplishStatusEnum.ACCOMPLISH.getCode());
        queryWrapper.eq(CActivityJoinRecord::getGiftSendStatus, GiftSendStatusEnum.UN_SEND.getCode());
        // 获取所有可发赠品信息
//        queryWrapper.in(CActivityJoinRecord::getBoxSn, boxSnList);
        List<CActivityJoinRecord> joinRecordList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(joinRecordList)) {
            throw new BoxActivityException("参与记录中无可发放数据，请检查后重新上传");
        }
        if (list.size() != joinRecordList.size()) {
            throw new BoxActivityException(String.format("参与记录中可发放数量为[%s]条，上传的有效数据为[%s]条，请检查后重新上传", joinRecordList.size(), list.size()));
        }
        return joinRecordList;
    }

    private static List<CActivityJoinRecord> buildUpdateList(List<CActivityJoinRecord> joinRecordList, List<JoinRecordExchangeGiftDto> list, CActivityInfoResp activity) {
        Map<String, JoinRecordExchangeGiftDto> map = list.stream().collect(Collectors.toMap(JoinRecordExchangeGiftDto::getBoxSn, Function.identity(), (o1, o2) -> o1));
        List<CActivityJoinRecord> updateList = new ArrayList<>();
        for (CActivityJoinRecord joinRecord : joinRecordList) {
            JoinRecordExchangeGiftDto dto = map.get(joinRecord.getBoxSn());
            if (dto == null) {
                throw new BoxActivityException(String.format("单号[%s]未匹配到卡密信息，请检查后重新上传", joinRecord.getBoxSn()));
            }
            CActivityJoinRecord update = new CActivityJoinRecord();
            updateList.add(update);
            update.setId(joinRecord.getId());
            update.setBoxSn(joinRecord.getBoxSn());
            update.setUpdateTime(new Date());
            update.setExchangeNumber(dto.getExchangeNumber());
            update.setExchangeSecret(dto.getExchangeSecret());
            update.setExchangeUrl(activity.getGift().getVoucherId());
        }
        return updateList;
    }

}

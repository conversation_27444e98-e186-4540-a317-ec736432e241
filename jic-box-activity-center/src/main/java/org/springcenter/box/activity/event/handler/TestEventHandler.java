package org.springcenter.box.activity.event.handler;


import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.event.TestEvent;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TestEventHandler {


    @AllowConcurrentEvents
    @Subscribe
    public void processMyEvent(final TestEvent event) {
        log.info("开始测试事件");
    }
}

package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: lwz
 * @Date: 2024-12-10 17:14:01
 * @Description: 
 */
@TableName("B_POINT_GOODS_SKU")

@ApiModel(value="BPointGoodsSku对象", description="")
public class BPointGoodsSku implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "skuId")
    @TableId(value = "ID")
    private String id;

    @ApiModelProperty(value = "SKU_NO")
    @TableField("SKU_NO")
    private String skuNo;

    @TableField("IMG_URL")
    private String imgUrl;

    @TableField("STOCK")
    private Long stock;

    @TableField("POINT")
    private BigDecimal point;

    @TableField("SPU_ID")
    private String spuId;

    @TableField("SPU_NO")
    private String spuNo;

    @ApiModelProperty(value = "商品吊牌价格")
    @TableField("PRICE")
    private BigDecimal price;

    @ApiModelProperty(value = "商品名字")
    @TableField("GOODS_NAME")
    private String goodsName;


    @ApiModelProperty(value = "颜色")
    @TableField("COLOR_NAME")
    private String colorName;


    @ApiModelProperty(value = "颜色")
    @TableField("COLOR_NO")
    private String colorNo;


    @ApiModelProperty(value = "规格")
    @TableField("SIZE_NAME")
    private String sizeName;

    @ApiModelProperty(value = "规格")
    @TableField("SIZE_NO")
    private String sizeNo;


    @TableField("CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField("DEL_FLAG")
    private Long delFlag;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public Long getStock() {
        return stock;
    }

    public void setStock(Long stock) {
        this.stock = stock;
    }

    public BigDecimal getPoint() {
        return point;
    }

    public void setPoint(BigDecimal point) {
        this.point = point;
    }

    public String getSpuId() {
        return spuId;
    }

    public void setSpuId(String spuId) {
        this.spuId = spuId;
    }

    public String getSpuNo() {
        return spuNo;
    }

    public void setSpuNo(String spuNo) {
        this.spuNo = spuNo;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Long delFlag) {
        this.delFlag = delFlag;
    }

    public String getColorName() {
        return colorName;
    }

    public void setColorName(String colorName) {
        this.colorName = colorName;
    }

    public String getColorNo() {
        return colorNo;
    }

    public void setColorNo(String colorNo) {
        this.colorNo = colorNo;
    }

    public String getSizeName() {
        return sizeName;
    }

    public void setSizeName(String sizeName) {
        this.sizeName = sizeName;
    }

    public String getSizeNo() {
        return sizeNo;
    }

    public void setSizeNo(String sizeNo) {
        this.sizeNo = sizeNo;
    }

    @Override
    public String toString() {
        return "BPointGoodsSku{" +
                "id='" + id + '\'' +
                ", skuNo='" + skuNo + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", stock=" + stock +
                ", point=" + point +
                ", spuId='" + spuId + '\'' +
                ", spuNo='" + spuNo + '\'' +
                ", price=" + price +
                ", goodsName='" + goodsName + '\'' +
                ", colorName='" + colorName + '\'' +
                ", colorNo='" + colorNo + '\'' +
                ", sizeName='" + sizeName + '\'' +
                ", sizeNo='" + sizeNo + '\'' +
                ", createTime=" + createTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                '}';
    }
}

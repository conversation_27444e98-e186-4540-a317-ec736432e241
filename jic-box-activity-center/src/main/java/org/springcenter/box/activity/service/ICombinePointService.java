package org.springcenter.box.activity.service;

import com.jnby.common.Page;
import org.springcenter.box.activity.api.dto.AddPointByAdminReqDto;
import org.springcenter.box.activity.api.dto.AddPointByOmReqDto;
import org.springcenter.box.activity.api.dto.AddPointReqDto;
import org.springcenter.box.activity.api.dto.SubtractPointReqDto;
import org.springcenter.box.activity.domain.box.BPointDetail;
import org.springcenter.box.activity.domain.box.BSubscribeInfo;
import org.springcenter.box.activity.domain.box.BUserPointAccount;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ICombinePointService {



    /**
     * 获取流水
     * @param customerId
     * @return
     */
    List<BPointDetail> getPointDetailListByCustomerId(String customerId);


    List<BPointDetail> getPointDetailListByUnionIdAndPage(String unionId, Page page);


    List<BSubscribeInfo> selectPointSubInfoByUnionIdAndTime(String unionId, Page page);

    List<BSubscribeInfo> getSubListByIds(List<String> ids);

    /**
     * 增加点数
     * @param addPointReqDto
     */
    void addPoint(AddPointReqDto addPointReqDto);


    /**
     * 增加点数(后台)
     * @param addPointByAdminReqDto
     */
    void addPointByAdmin(AddPointByAdminReqDto addPointByAdminReqDto);


    /**
     * 增加点数(后台)
     * @param addPointByOmReqDto
     */
    BPointDetail addPointByOm(AddPointByOmReqDto addPointByOmReqDto);

    /**
     * 减去点数
     * @param subtractPointReqDto
     */
    void subtractPoint(SubtractPointReqDto subtractPointReqDto);
}

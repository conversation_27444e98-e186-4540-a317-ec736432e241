package org.springcenter.box.activity.enums;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/11/9 10:50
 */
public enum UltimaBoxDetailsStatusEnum {
    UNSEND(0, "待发货"),
    RETURNING(1, "还货中"),
    RETURNED(2, "已还货"),
    PAY(3, "已购买"),
    CANCEL(7, "已取消"),
    SEND(8, "已发货"),
    SIGNED(9, "已签收"),
    UNRETURN(10, "待还货"),
    UNPUTSTORAGE(11, "待入库"),
    DELETE(12, "已删款"),
    ;

    private Integer code;
    private String desc;

    UltimaBoxDetailsStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}

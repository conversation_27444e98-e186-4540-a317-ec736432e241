package org.springcenter.box.activity.service.crowd;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 人群过滤器
 */
@Service
public class CrowdFilterService {
    @Resource
    private CrowdFilterHandler filterChain;

    public boolean applyFilters(CrowdFilterContext request) {
        // 过滤顺序在 FilterChainConfig 中实例化
        return filterChain.handleRequest(request);
    }
}

package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@ApiModel("满赠活动赠品创建实体")
@Data
public class CActivityGiftCreateReq {
    @ApiModelProperty(value = "赠品名称", required = true)
    @NotBlank(message = "赠品名称不能为空")
    private String giftName;
    @ApiModelProperty(value = "赠品类型：1=外部实物，2=内部实物，3=兑换卡", required = true)
    @NotNull(message = "赠品类型不能为空")
    private Integer giftType;
    @ApiModelProperty(value = "凭证ID：sku、券号、外部兑换地址", required = true)
    @NotBlank(message = "凭证ID不能为空")
    private String voucherId;
    @ApiModelProperty(value = "价值", required = true)
    @NotNull(message = "价值不能为空")
    @Max(value = 100000, message = "价值不能超过100000")
    private BigDecimal price;
    @ApiModelProperty(value = "总数", required = true)
    @NotNull(message = "总数不能为空")
    private Long totalNum;
    @ApiModelProperty(value = "图片地址：多个英文逗号分割", required = true)
    @NotBlank(message = "图片地址不能为空")
    private String picList;
}

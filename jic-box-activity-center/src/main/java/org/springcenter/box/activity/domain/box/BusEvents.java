package org.springcenter.box.activity.domain.box;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;
import org.killbill.queue.api.PersistentQueueEntryLifecycleState;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: yuanxiaozhong
 * @Date: 2022-03-22 15:24:26
 * @Description: 事件表
 */
@TableName("ACTIVITY_BUS_EVENTS")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BusEvents对象", description="事件表")
public class BusEvents implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "记录ID")
    @TableId(value = "RECORD_ID")
    private Long recordId;
    @ApiModelProperty(value = "类名")
    @TableField("CLASS_NAME")
    private String className;
    @ApiModelProperty(value = "识别态")
    @TableField("USER_TOKEN")
    private String userToken;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_DATE")
    private Date createdDate;
    @ApiModelProperty(value = "创建者")
    @TableField("CREATING_OWNER")
    private String creatingOwner;
    @TableField("PROCESSING_OWNER")
    private String processingOwner;
    @TableField("PROCESSING_AVAILABLE_DATE")
    private Date processingAvailableDate;
    @TableField("ERROR_COUNT")
    private Long errorCount;
    @TableField("PROCESSING_STATE")
    private String processingState;
    @TableField("SEARCH_KEY1")
    private Long searchKey1;
    @TableField("SEARCH_KEY2")
    private Long searchKey2;
    @TableField("EVENT_JSON")
    private String eventJson;


    public BusEvents() { /* DAO mapper */ }

    public BusEvents(final Long recordId, final String createdOwner, final String owner, final Date createdDate, final Date nextAvailable,
                     final PersistentQueueEntryLifecycleState processingState, final String busEventClass, final String busEventJson, final Long errorCount,
                     final String userToken, final Long searchKey1, final Long searchKey2) {
        this.recordId = recordId;
        this.creatingOwner = createdOwner;
        this.processingOwner = owner;
        this.createdDate = createdDate;
        this.processingAvailableDate = nextAvailable;
        this.processingState = processingState.name();
        this.className = busEventClass;
        this.errorCount = errorCount;
        this.eventJson = busEventJson;
        this.userToken = userToken;
        this.searchKey1 = searchKey1;
        this.searchKey2 = searchKey2;
    }

    public BusEvents(final String createdOwner, final Date createdDate, final String busEventClass, final String busEventJson,
                     final String userToken, final Long searchKey1, final Long searchKey2) {
        this(-1L, createdOwner, null, createdDate, null, PersistentQueueEntryLifecycleState.AVAILABLE, busEventClass, busEventJson, 0L, userToken, searchKey1, searchKey2);
    }

    public BusEvents(final BusEvents in, final String owner, final Date nextAvailable, final PersistentQueueEntryLifecycleState state) {
        this(in.getRecordId(), in.getCreatingOwner(), owner, in.getCreatedDate(), nextAvailable, state, in.getClassName(), in.getEventJson(), in.getErrorCount(), in.getUserToken(), in.getSearchKey1(), in.getSearchKey2());
    }

    public BusEvents(final BusEvents in, final String owner, final Date nextAvailable, final PersistentQueueEntryLifecycleState state, final Long errorCount) {
        this(in.getRecordId(), in.getCreatingOwner(), owner, in.getCreatedDate(), nextAvailable, state, in.getClassName(), in.getEventJson(), errorCount, in.getUserToken(), in.getSearchKey1(), in.getSearchKey2());
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getUserToken() {
        return userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatingOwner() {
        return creatingOwner;
    }

    public void setCreatingOwner(String creatingOwner) {
        this.creatingOwner = creatingOwner;
    }

    public String getProcessingOwner() {
        return processingOwner;
    }

    public void setProcessingOwner(String processingOwner) {
        this.processingOwner = processingOwner;
    }

    public Date getProcessingAvailableDate() {
        return processingAvailableDate;
    }

    public void setProcessingAvailableDate(Date processingAvailableDate) {
        this.processingAvailableDate = processingAvailableDate;
    }

    public Long getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Long errorCount) {
        this.errorCount = errorCount;
    }

    public String getProcessingState() {
        return processingState;
    }

    public void setProcessingState(String processingState) {
        this.processingState = processingState;
    }

    public Long getSearchKey1() {
        return searchKey1;
    }

    public void setSearchKey1(Long searchKey1) {
        this.searchKey1 = searchKey1;
    }

    public Long getSearchKey2() {
        return searchKey2;
    }

    public void setSearchKey2(Long searchKey2) {
        this.searchKey2 = searchKey2;
    }

    public String getEventJson() {
        return eventJson;
    }

    public void setEventJson(String eventJson) {
        this.eventJson = eventJson;
    }


    @Override
    public String toString() {
        return "BusEventsModel{" +
            "recordId=" + recordId +
            ", className=" + className +
            ", userToken=" + userToken +
            ", createdDate=" + createdDate +
            ", creatingOwner=" + creatingOwner +
            ", processingOwner=" + processingOwner +
            ", processingAvailableDate=" + processingAvailableDate +
            ", errorCount=" + errorCount +
            ", processingState=" + processingState +
            ", searchKey1=" + searchKey1 +
            ", searchKey2=" + searchKey2 +
            ", eventJson=" + eventJson +
            "}";
    }
}

package org.springcenter.box.activity.job.subscribe;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.box.activity.config.trace.XxlJobTaskLog;
import org.springcenter.box.activity.domain.box.BUserPointAccount;
import org.springcenter.box.activity.mapper.box.BUserPointAccountMapper;
import org.springcenter.box.activity.service.ICombinePointDetailService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserAccountPointJob extends IJobHandler {

    @Resource
    private BUserPointAccountMapper bUserPointAccountMapper;


    @Resource
    private ICombinePointDetailService iCombinePointDetailService;


    /**
     * 用户账户的点数
     *
     * @throws Exception
     */
    @Override
    @XxlJob("UserAccountPointJob")
    public void execute() throws Exception {
        XxlJobTaskLog.traceLog("处理用户账户的点数开始");
        handleUserAccountPoint();
        XxlJobTaskLog.traceLog("处理用户账户的点数结束");
    }


    /**
     * 处理用户账户点数
     */
    void handleUserAccountPoint() {
        int totalPage = 1;
        Page page = new Page(1, 500);

        List<BUserPointAccount> result = new ArrayList<>();
        while (page.getPageNo() <= totalPage) {
            List<BUserPointAccount> bPointDetailListTemp = getNeedAllUserAccount(page);
            if (CollectionUtils.isEmpty(bPointDetailListTemp)) {
                break;
            } else {
                result.addAll(bPointDetailListTemp);
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
        }

        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        result.forEach(x -> {
            try {
                iCombinePointDetailService.rulesUserPoint(x.getId());
            } catch (Exception e) {
                log.error("规则点数异常id:{} msg:{}",x.getId(),e.getMessage());
            }
        });
    }


    /**
     * 所有账户
     *
     * @param page
     * @return
     */
    public List<BUserPointAccount> getNeedAllUserAccount(Page page) {
        com.github.pagehelper.Page<BUserPointAccount> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bUserPointAccountMapper.getAllUserAccount();
        PageInfo<BUserPointAccount> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }
}

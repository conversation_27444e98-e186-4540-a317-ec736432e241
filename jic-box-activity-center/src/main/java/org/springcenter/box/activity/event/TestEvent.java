package org.springcenter.box.activity.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.killbill.bus.api.BusEvent;

import java.util.UUID;

public class TestEvent implements BusEvent {

    private String say;
    private String type;
    private final Long searchKey1;
    private final Long searchKey2;
    private final UUID userToken;


    @JsonCreator
    public TestEvent(
            @JsonProperty("say") String say,
            @JsonProperty("type") final String type,
            @JsonProperty("searchKey1") final Long searchKey1,
            @JsonProperty("searchKey2") final Long searchKey2,
            @JsonProperty("userToken") final UUID userToken) {
        this.say = say;
        this.searchKey1 = searchKey1;
        this.searchKey2 = searchKey2;
        this.userToken = userToken;
    }

    @Override
    public Long getSearchKey1() {
        return this.searchKey1;
    }

    @Override
    public Long getSearchKey2() {
        return searchKey2;
    }

    @Override
    public UUID getUserToken() {
        return this.userToken;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSay() {
        return say;
    }

    public void setSay(String say) {
        this.say = say;
    }
}

package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-09-27 10:58:42
 * @Description: 
 */
@Data
@TableName("FASHIONER")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="Fashioner对象", description="")
public class Fashioner implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "账号id")
    @TableField("USER_ID")
    private String userId;
    @ApiModelProperty(value = "搭配师微信openid")
    @TableField("OPENID")
    private String openid;
    @ApiModelProperty(value = "名称")
    @TableField("NAME")
    private String name;
    @ApiModelProperty(value = "性别(0:未知;1:男;2:女)")
    @TableField("SEX")
    private Long sex;
    @ApiModelProperty(value = "手机号")
    @TableField("PHONE")
    private String phone;
    @ApiModelProperty(value = "描述")
    @TableField("MEMO")
    private String memo;
    @ApiModelProperty(value = "照片")
    @TableField("PHOTO")
    private String photo;
    @ApiModelProperty(value = "状态(0:停用;1:启用)")
    @TableField("STATUS")
    private Long status;
    @ApiModelProperty(value = "评分")
    @TableField("SCORE")
    private String score;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "修改时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @TableField("QRCODE")
    private String qrcode;
    @ApiModelProperty(value = "生成二维码链接")
    @TableField("QRURL")
    private String qrurl;
    @ApiModelProperty(value = "擅长搭配 10-男装 20-女装 30-通用")
    @TableField("EXPERT")
    private String expert;
    @ApiModelProperty(value = "邮箱")
    @TableField("EMAIL")
    private String email;
    @ApiModelProperty(value = "udesk对应的客服id")
    @TableField("UDESK_ID")
    private BigDecimal udeskId;
    @ApiModelProperty(value = "客服组id字符串")
    @TableField("GROUP_ID")
    private String groupId;
    @ApiModelProperty(value = "小程序码")
    @TableField("MIN_QRCODE")
    private String minQrcode;
    @ApiModelProperty(value = "小程序码场景值")
    @TableField("MIN_SCENE")
    private String minScene;
    @ApiModelProperty(value = "oaid")
    @TableField("OA_ID")
    private BigDecimal oaId;
    @ApiModelProperty(value = "是否导购")
    @TableField("IS_SALES")
    private Long isSales;
    @ApiModelProperty(value = "是否走促销价格(0:否 1:是)")
    @TableField("IS_PROMO")
    private Long isPromo;
    @ApiModelProperty(value = "星座")
    @TableField("CONSTELLATION")
    private String constellation;
    @ApiModelProperty(value = "客户说")
    @TableField("EVALUATION")
    private String evaluation;
    @ApiModelProperty(value = "标签")
    @TableField("TAGS")
    private String tags;
    @ApiModelProperty(value = "优先级")
    @TableField("PRIORITY")
    private Long priority;
    @ApiModelProperty(value = "搭配师微信二维码")
    @TableField("WECHAT_URL")
    private String wechatUrl;
    @ApiModelProperty(value = "微信号")
    @TableField("WECHAT")
    private String wechat;
    @ApiModelProperty(value = "打印头像")
    @TableField("PRINTHEADIMG")
    private String printheadimg;
    @ApiModelProperty(value = "是否用于小程序绑定搭配师选择")
    @TableField("IF_USE_MINIAPP")
    private Integer ifUseMiniapp;
    @ApiModelProperty(value = "伯俊表hr_employee的id")
    @TableField("HR_EMP_ID")
    private String hrEmpId;
    @ApiModelProperty(value = "伯俊表 c_store的id")
    @TableField("C_STORE_ID")
    private String cStoreId;
    @ApiModelProperty(value = "搭配师分类管理id")
    @TableField("FASHIONER_TYPE_ID")
    private String fashionerTypeId;
    @ApiModelProperty(value = "门店类型（0指定门店 1排除门店）")
    @TableField("STORE_TYPE")
    private Long storeType;
    @ApiModelProperty(value = "门店ids")
    @TableField("STORE_IDS")
    private String storeIds;


}

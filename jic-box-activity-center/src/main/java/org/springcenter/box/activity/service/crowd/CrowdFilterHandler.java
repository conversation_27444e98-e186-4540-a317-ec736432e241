package org.springcenter.box.activity.service.crowd;

import org.springcenter.box.activity.config.exception.BoxActivityException;

/**
 * 人群条件过滤器
 */
public interface CrowdFilterHandler {
    /**
     * 过滤器名称
     */
    String filterName();
    /**
     * 声明一个变量来引用链中的下一个处理者
     */
    CrowdFilterHandler getNext();

    /**
     * 设置链中的下一个处理者
     */
    void setNext(CrowdFilterHandler next);

    /**
     * 判断请求是否通过当前处理者
     */
    boolean filter(CrowdFilterContext context);

    void startLog();
    void endLog();

    /**
     * 处理请求，默认会调用下一个处理者
     */
    default boolean handleRequest(CrowdFilterContext context) {
        if (context == null) {
            throw new BoxActivityException("活动信息为空，无法判断门槛条件");
        }
        startLog();
        boolean filter = this.filter(context);
        endLog();
        if (filter) {
            if (this.getNext() == null) {
                return true;
            }
            return getNext().handleRequest(context);
        }

        return false;
    }
}

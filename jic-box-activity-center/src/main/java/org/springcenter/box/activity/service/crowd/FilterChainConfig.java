package org.springcenter.box.activity.service.crowd;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterChainConfig {
    /**
     * 盒子类型
     */
    @Bean
    public CrowdFilterHandler boxTypeFilter() {
        return new BoxSubTypeFilter();
    }

    /**
     * 品牌商品
     */
    @Bean
    public CrowdFilterHandler productFilter() {
        return new ProductFilter();
    }

    /**
     * 搭盒人员
     */
    @Bean
    public CrowdFilterHandler boxBuilderFilter() {
        return new BoxBuilderFilter();
    }

    /**
     * 新客/老客
     */
    @Bean
    public CrowdFilterHandler customerFilter() {
        return new CustomerFilter();
    }
    /**
     * 线下满赠
     */
    @Bean
    public CrowdFilterHandler offlineActivityFilter() {
        return new OfflineActivityFilter();
    }

    @Bean
    public CrowdFilterHandler repeatActivityFilter() {
        return new BoxActivityFilter();
    }

    @Bean
    public CrowdFilterHandler filterChain() {
        CrowdFilterHandler repeatActivityFilter = repeatActivityFilter();
        CrowdFilterHandler offlineActivityFilter = offlineActivityFilter();
        CrowdFilterHandler boxBuilderFilter = boxBuilderFilter();
        CrowdFilterHandler customerFilter = customerFilter();
        CrowdFilterHandler boxTypeFilter = boxTypeFilter();
        CrowdFilterHandler productFilter = productFilter();

        repeatActivityFilter.setNext(offlineActivityFilter);
        offlineActivityFilter.setNext(boxBuilderFilter);
        boxBuilderFilter.setNext(customerFilter);
        customerFilter.setNext(boxTypeFilter);
        boxTypeFilter.setNext(productFilter);

        return repeatActivityFilter;
    }
}

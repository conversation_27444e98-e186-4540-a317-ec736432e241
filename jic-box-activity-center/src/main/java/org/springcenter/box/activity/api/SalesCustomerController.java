package org.springcenter.box.activity.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.api.dto.SalesCustomerListResp;
import org.springcenter.box.activity.api.dto.SalesCustomerReq;
import org.springcenter.box.activity.api.dto.UIimtedBoxUserReq;
import org.springcenter.box.activity.api.dto.UIimtedBoxUserResp;
import org.springcenter.box.activity.domain.box.Box;
import org.springcenter.box.activity.domain.box.BoxDetails;
import org.springcenter.box.activity.service.IBoxDetailsService;
import org.springcenter.box.activity.service.IBoxService;
import org.springcenter.box.activity.service.ICustomerDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/corp/customer")
@RestController
@Api(tags = "导购会员")
@Slf4j
public class SalesCustomerController {

    @Autowired
    private ICustomerDetailService icustomerDetailService;

    @Autowired
    private IBoxDetailsService iboxDetailsService;

    @Autowired
    private IBoxService iboxService;

    @ApiOperation(value = "获取导购订阅周期内的会员列表")
    @ResponseBody
    @RequestMapping(value = "/getBoxSubscribeCustomerList", method = RequestMethod.POST)
    public ResponseResult<List<SalesCustomerListResp>> getBoxSubscribeCustomerList(@Validated @RequestBody CommonRequest<SalesCustomerReq> request){
        SalesCustomerReq salesCustomerReq = request.getRequestData();
        Page page = request.getPage();
        List<SalesCustomerListResp> resps = icustomerDetailService.querySalesBoxSubscribeMember(salesCustomerReq, page);
        return ResponseResult.success(resps, page);
    }

    /**
     * 获取发盒未支付的订阅订单
     */
    @ApiOperation(value = "获取发盒未支付的订阅订单")
    @ResponseBody
    @RequestMapping(value = "/getBoxSubscribeOrderList", method = RequestMethod.POST)
    public ResponseResult<List<UIimtedBoxUserResp>> getBoxSubscribeOrderList(@Validated @RequestBody UIimtedBoxUserReq request){
        List<Box> resps = iboxService.getUnFinishBoxList(request.getUnionId());
        if(resps == null || resps.size() == 0){
            return ResponseResult.success(new ArrayList<>());
        }
        List<String> boxIds = resps.stream().map(box -> box.getId()).collect(Collectors.toList());
        List<BoxDetails> dealGoodsList = iboxDetailsService.getUnDealGoodsList(boxIds);
        if(dealGoodsList == null || dealGoodsList.size() == 0){
            return ResponseResult.success(new ArrayList<>());
        }
        return ResponseResult.success(dealGoodsList.stream().map(boxDetails -> {
            UIimtedBoxUserResp uiimtedBoxUserResp = new UIimtedBoxUserResp();
            uiimtedBoxUserResp.setProductId(Long.valueOf(boxDetails.getOutId()));
            uiimtedBoxUserResp.setProductCode(boxDetails.getProductCode());
            uiimtedBoxUserResp.setSkuId(Long.valueOf(boxDetails.getProductId()));
            uiimtedBoxUserResp.setSkuCode(boxDetails.getSku());
            return uiimtedBoxUserResp;
        }).collect(Collectors.toList()));
    }
}

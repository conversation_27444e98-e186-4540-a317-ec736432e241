package org.springcenter.box.activity.enums;

import org.springcenter.box.activity.config.exception.BoxActivityException;
import lombok.Getter;

/**
 * 前端页面枚举
 */
@Getter
public enum FrontActivityStatusEnum {
    ALL(0, "全部"),
    NOT_START(1, "未开始"),
    RUNNING(2, "进行中"),
    ACCOUNTING(3, "核算中"),
    WAITING(4, "待发放"),
    ENDING(5, "已结束"),
    DOWN(6, "已下架"),
    ;

    private final Integer code;
    private final String msg;

    FrontActivityStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static FrontActivityStatusEnum getByCode(Integer code) {
        for (FrontActivityStatusEnum frontActivityStatusEnum : FrontActivityStatusEnum.values()) {
            if (frontActivityStatusEnum.getCode().equals(code)) {
                return frontActivityStatusEnum;
            }
        }
        throw new BoxActivityException("活动枚举状态不匹配，请检查后再重试");
    }

    /**
     * 能否计算
     * @param code
     * @return
     */
    public static boolean canUpdate(Integer code) {
        if (code == null) {
            return false;
        }
        if (RUNNING.getCode().equals(code)) {
            return true;
        }
        if (ACCOUNTING.getCode().equals(code)) {
            return true;
        }
        return false;
    }

    /**
     * 待发放
     * @param code
     * @return
     */
    public static boolean canSend(Integer code) {
        return WAITING.getCode().equals(code);
    }

    /**
     * 可以新增奖品库存
     * @param code
     * @return
     */
    public static boolean canAddGiftStock(Integer code) {
        if (NOT_START.getCode().equals(code)) {
            return true;
        }
        if (RUNNING.getCode().equals(code)) {
            return true;
        }
        return false;
    }
}

package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CreatePointOrderReqDto {

    @ApiModelProperty(value = "unionId")
    @NotBlank(message = "unionId不能为空")
    private String unionId;



    @ApiModelProperty(value = "customerId")
    @NotBlank(message = "customerId不能为空")
    private String customerId;


    @ApiModelProperty(value = "skuList")
    @NotEmpty(message = "skuList不能为空")
    private List<Sku> skuList;


    @ApiModelProperty(value = "使用点数")
    @NotNull(message = "使用点数不能为空")
    private BigDecimal usePoint;

    @ApiModelProperty(value = "客户地址LogisticsId")
    @NotBlank(message = "客户地址LogisticsId不能为空")
    private String customerLogisticsId;

    @Data
    public static class Sku{
        @ApiModelProperty(value = "skuId")
        @NotBlank(message = "skuId不能为空")
        private String skuId;


        @ApiModelProperty(value = "skuNo")
        @NotBlank(message = "skuNo不能为空")
        private String skuNo;

        @ApiModelProperty(value = "spuId")
        @NotBlank(message = "spuId不能为空")
        private String spuId;


        @ApiModelProperty(value = "spuNo")
        @NotBlank(message = "spuNo不能为空")
        private String spuNo;
    }



}

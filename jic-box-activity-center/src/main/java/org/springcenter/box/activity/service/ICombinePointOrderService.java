package org.springcenter.box.activity.service;


import com.jnby.common.Page;
import org.springcenter.box.activity.domain.box.BLogisticsSnapshot;
import org.springcenter.box.activity.domain.box.BUserPointOrderDetail;
import org.springcenter.box.activity.domain.box.UserPointOrderWithDetail;
import org.springcenter.box.activity.dto.request.AllUserOrderDetailListReqDto;
import org.springcenter.box.activity.dto.request.CreatePointOrderReqDto;
import org.springcenter.box.activity.dto.request.GetCustomerLogisticReqDto;

import java.util.List;

public interface ICombinePointOrderService {


    List<BUserPointOrderDetail>  allUserOrderDetailList(AllUserOrderDetailListReqDto allUserOrderDetailListReqDto, Page page) ;

    List<UserPointOrderWithDetail> userOrderList(String unionId, Page page) ;

    UserPointOrderWithDetail userOrderInfo(String orderId) ;


    /**
     * 下单
     * @param createPointOrderReqDto
     */
    void createPointOrder(CreatePointOrderReqDto createPointOrderReqDto);


    /**
     *
     */
    BLogisticsSnapshot getCustomerLogistic(GetCustomerLogisticReqDto getCustomerLogisticReqDto);
}

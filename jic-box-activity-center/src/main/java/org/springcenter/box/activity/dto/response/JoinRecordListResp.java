package org.springcenter.box.activity.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("达成赠品列表回参")
@Data
public class JoinRecordListResp {
    @ApiModelProperty(value = "记录ID")
    private String id;
    @ApiModelProperty(value = "BOX单号")
    private String boxNo;
    @ApiModelProperty(value = "BOX记录Id")
    private String boxId;
    @ApiModelProperty(value = "用户unionId")
    private String unionId;
    @ApiModelProperty(value = "消费者Id")
    private String customerId;
}

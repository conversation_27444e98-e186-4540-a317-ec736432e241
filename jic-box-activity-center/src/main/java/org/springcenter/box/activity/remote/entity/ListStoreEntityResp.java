package org.springcenter.box.activity.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * [{
 * "id": 416034,
 * "code": "8KA37702",
 * "name": "(8KA377)龙口福祺(HOME)",
 * "tel": "15063870899",
 * "city": "龙口市",
 * "address": "山东省龙口市隆基路TIMEFREEZE福祺",
 * "openTime": "",
 * "longitude": "120.333549",
 * "latitude": "37.656273",
 * "storeDistance": "",
 * "distance": -1,
 * "sort": 7,
 * "mainImage": "https://picttype1.jnby.com/20200715-webpos-jnbyh.png"
 * }]
 */
@Data
public class ListStoreEntityResp {

    @ApiModelProperty("门店ID")
    private Long id;

    @ApiModelProperty("门店编码")
    private String code;

    @ApiModelProperty("门店名称")
    private String name;

    @ApiModelProperty("门店电话")
    private String tel;

    @ApiModelProperty("门店城市")
    private String city;

    @ApiModelProperty("门店地址")
    private String address;

    @ApiModelProperty("门店营业时间")
    private String openTime;

    @ApiModelProperty("门店经度")
    private String longitude;

    @ApiModelProperty("门店纬度")
    private String latitude;

    @ApiModelProperty("门店距离")
    private String storeDistance;

    @ApiModelProperty("门店距离")
    private String distance;

    @ApiModelProperty("门店排序")
    private Integer sort;

    @ApiModelProperty("门店主图")
    private String mainImage;

}

package org.springcenter.box.activity.event.bus;

import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.box.activity.event.RulesUserPointEvent;
import org.springcenter.box.activity.event.handler.ModifySpuStockHandler;
import org.springcenter.box.activity.event.handler.RulesUserPointHandler;
import org.springcenter.box.activity.service.BaseEventBus;
import org.springcenter.box.activity.service.IBusEventsService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
@Slf4j
public class RulesUserPointEventBus implements InitializingBean, BaseEventBus<RulesUserPointEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IBusEventsService iBusEventsService;

    @Resource
    private RulesUserPointHandler rulesUserPointHandler;

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(rulesUserPointHandler);
    }

    @Override
    public void post(RulesUserPointEvent rulesUserPointEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(rulesUserPointEvent);
    }
}

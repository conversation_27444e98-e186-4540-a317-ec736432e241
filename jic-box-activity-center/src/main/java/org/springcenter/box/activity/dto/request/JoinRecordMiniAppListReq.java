package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel("小程序端查赠品明细入参")
@Data
public class JoinRecordMiniAppListReq {
    @ApiModelProperty(value = "消费者ID", required = true)
    @NotBlank(message = "消费者ID不能为空")
    private String unionId;
    @ApiModelProperty(value = "boxId数组")
    @NotEmpty(message = "boxId不能为空")
    private List<String> boxIdList;
}

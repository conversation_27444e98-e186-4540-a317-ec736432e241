package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@ApiModel("人工新增入参")
@Data
public class JoinRecordAddReq {
    @ApiModelProperty(value = "操作人", required = true)
    @NotBlank(message = "操作人不能为空")
    private String optPerson;
    @ApiModelProperty(value = "操作备注", required = true)
    @NotBlank(message = "操作备注不能为空")
    private String optRemark;
    @ApiModelProperty(value = "活动Id", required = true)
    @NotBlank(message = "活动Id不能为空")
    private String activityId;
    @ApiModelProperty(value = "Box单号", required = true)
    @NotBlank(message = "box单号不能为空")
    private String boxSn;
}

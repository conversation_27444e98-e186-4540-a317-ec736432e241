package org.springcenter.box.activity.api;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.DateTime;
import org.springcenter.box.activity.api.dto.AddPointReqDto;
import org.springcenter.box.activity.api.dto.SubtractPointReqDto;
import org.springcenter.box.activity.domain.box.*;
import org.springcenter.box.activity.dto.request.DetailListReqDto;
import org.springcenter.box.activity.dto.request.FillAccountReqDto;
import org.springcenter.box.activity.dto.request.FuturePointInDaysReqDto;
import org.springcenter.box.activity.dto.request.GetAccountReqDto;
import org.springcenter.box.activity.dto.response.DetailListRespDto;
import org.springcenter.box.activity.mapper.box.BPointDetailMapper;
import org.springcenter.box.activity.mapper.box.BPointGoodsSpuMapper;
import org.springcenter.box.activity.mapper.box.BSubscribeInfoMapper;
import org.springcenter.box.activity.service.ICombineAccountService;
import org.springcenter.box.activity.service.ICombinePointDetailService;
import org.springcenter.box.activity.service.ICombinePointService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequestMapping("/user/point")
@RestController
@Api(tags = "用户点数")
@Slf4j
public class UserPointController {

    @Resource
    private ICombinePointService iCombinePointService;

    @Resource
    private ICombineAccountService iCombineAccountService;


    @Resource
    private ICombinePointDetailService iCombinePointDetailService;

    @Resource
    private BPointDetailMapper bPointDetailMapper;


    @Resource
    private BPointGoodsSpuMapper bPointGoodsSpuMapper;


    @ApiOperation(value = "获取账户信息", notes = "获取账户信息")
    @PostMapping("/account")
    public ResponseResult<BUserPointAccount> account(@Validated @RequestBody CommonRequest<GetAccountReqDto> request) {
        GetAccountReqDto getAccount = Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        return ResponseResult.success(iCombineAccountService.getAccountByUnionId(getAccount.getUnionId()));
    }


    @ApiOperation(value = "补充账户信息", notes = "补充账户信息")
    @PostMapping("/fillAccount")
    public ResponseResult fillAccount(@Validated @RequestBody CommonRequest<FillAccountReqDto> commonRequest){
        FillAccountReqDto fillAccountReqDto = Preconditions.checkNotNull(commonRequest.getRequestData(),"入参不能为空");
        fillAccountReqDto.getCustomerIdList().forEach(e->{
            try {
                iCombineAccountService.getAccountByCustomerId(e);
            }catch (Exception x){
                log.error("补充账户信息异常id:{} msg:{}",e,x.getMessage());
            }
        });
        return ResponseResult.success();
    }



    @ApiOperation(value = "获取账户信息列表", notes = "获取账户信息列表")
    @PostMapping("/accountList")
    public ResponseResult< List<BUserPointAccount>> accountList(@Validated @RequestBody CommonRequest<List<GetAccountReqDto>> request) {
        List<GetAccountReqDto> getAccountList = request.getRequestData();
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(getAccountList),"入参不能为空");
        return ResponseResult.success(iCombineAccountService.getAccountByUnionIds(getAccountList.stream().map(GetAccountReqDto::getUnionId).collect(Collectors.toList())));
    }


    @ApiOperation(value = "流水列表", notes = "流水列表")
    @PostMapping("/detailList")
    public ResponseResult<List<DetailListRespDto>> detailList(@Validated @RequestBody CommonRequest<DetailListReqDto> request) {
        DetailListReqDto detailListReqDto = Preconditions.checkNotNull(request.getRequestData(), "入参不能为空");
        Page page = request.getPage();
        List<BSubscribeInfo> bSubscribeInfos = iCombinePointService.selectPointSubInfoByUnionIdAndTime(detailListReqDto.getUnionId(), page);
        List<DetailListRespDto> detailListRespDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(bSubscribeInfos)) {
            return ResponseResult.success(detailListRespDtoList, page);
        }

        // 查询点数流水
        List<BPointDetail> bPointDetailList = bPointDetailMapper.getAllListByUnionId(detailListReqDto.getUnionId());
        bSubscribeInfos.forEach(x -> {
            DetailListRespDto detailListRespDto = new DetailListRespDto();
            detailListRespDto.setSubId(x.getId());
            detailListRespDto.setBPointDetailList(bPointDetailList.stream().filter(e -> e.getSubId().equals(x.getId())).sorted(Comparator.comparing(BPointDetail::getCreateTime).reversed()).collect(Collectors.toList()));
            detailListRespDto.setSubStartTime(x.getStartTime());
            detailListRespDto.setSubEndTime(x.getEndTime());
            if (CollectionUtils.isNotEmpty(detailListRespDto.getBPointDetailList())) {
                detailListRespDto.setSubEndTime(detailListRespDto.getBPointDetailList().get(0).getExpireTime());
            } else {
                detailListRespDto.setSubEndTime(new DateTime(x.getEndTime()).plusDays(30).toDate());
                if (Long.valueOf(3).equals(x.getStatus())) {
                    detailListRespDto.setSubEndTime(new DateTime(x.getUnsubTime()).plusDays(30).toDate());
                }
            }
            detailListRespDtoList.add(detailListRespDto);
        });
        return ResponseResult.success(detailListRespDtoList, page);
    }


    @ApiOperation(value = "增加点数", notes = "增加点数")
    @PostMapping("/addPoint")
    public ResponseResult addPoint(@Validated @RequestBody CommonRequest<AddPointReqDto> request) {
        AddPointReqDto addPointReqDto = Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        log.info("增加点数request:{}", JSONObject.toJSONString(request));
        iCombineAccountService.getAccountByCustomerId(addPointReqDto.getCustomerId());
        iCombinePointService.addPoint(addPointReqDto);
        return ResponseResult.success();
    }


    @ApiOperation(value = "减去点数", notes = "减去点数")
    @PostMapping("/subtractPoint")
    public ResponseResult subtractPoint(@Validated @RequestBody CommonRequest<SubtractPointReqDto> request) {
        SubtractPointReqDto subtractPointReqDto = Preconditions.checkNotNull(request.getRequestData(),"入参不能为空");
        log.info("减去点数request:{}", JSONObject.toJSONString(request));
        iCombineAccountService.getAccountByCustomerId(subtractPointReqDto.getCustomerId());
        iCombinePointService.subtractPoint(subtractPointReqDto);
        return ResponseResult.success();
    }





    @ApiOperation(value = "重组点数集合", notes = "重组点数集合")
    @RequestMapping(value = "/getPointDetailWithSub/{customerId}", method = RequestMethod.POST)
    public ResponseResult<List<PointDetailWithSub>> getPointDetailWithSub(@PathVariable("customerId") String customerId){
        return ResponseResult.success(iCombinePointDetailService.getPointDetailWithSub(customerId));
    }


    @ApiOperation(value = "实时点数", notes = "实时点数")
    @RequestMapping(value = "/realTimePointByCustomerId/{customerId}", method = RequestMethod.POST)
    public ResponseResult realTimePointByCustomerId(@PathVariable("customerId") String customerId){
        return ResponseResult.success(iCombinePointDetailService.realTimePointByCustomerId(customerId));
    }



    @ApiOperation(value = "规则用户点数", notes = "规则用户点数")
    @RequestMapping(value = "/rulesUserPoint/{customerId}", method = RequestMethod.POST)
    public ResponseResult rulesUserPoint(@PathVariable("customerId") String customerId){
        return ResponseResult.success(iCombinePointDetailService.rulesUserPoint(customerId));
    }


    @ApiOperation(value = "用户即将30天失效点数", notes = "用户即将30天失效点数")
    @RequestMapping(value = "/futurePointInDays", method = RequestMethod.POST)
    public ResponseResult<FuturePointInDaysEntity> futurePointInDays(@Validated @RequestBody CommonRequest<FuturePointInDaysReqDto> commonRequest){
        FuturePointInDaysReqDto futurePointInDaysReqDto = Preconditions.checkNotNull(commonRequest.getRequestData(), "入参不能为空");
        return ResponseResult.success(iCombinePointDetailService.getFuturePointInDays(futurePointInDaysReqDto.getUnionId(),30));
    }


    @ApiOperation(value = "查询最低点数的spu信息", notes = "查询最低点数的spu")
    @RequestMapping(value = "/getLastLowPointGoodsSpu", method = RequestMethod.POST)
    public ResponseResult<BPointGoodsSpu> getLastLowPointGoodsSpu() {
        BPointGoodsSpu bPointGoodsSpu = bPointGoodsSpuMapper.getLastLowPointGoodsSpu();
        return ResponseResult.success(bPointGoodsSpu);
    }

}

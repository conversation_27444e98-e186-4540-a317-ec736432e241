package org.springcenter.box.activity.job.jst;


import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.config.trace.XxlJobTaskLog;
import org.springcenter.box.activity.service.ICombineJstPushOrderService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PushJstOrdersJob extends IJobHandler{

    @Resource
    private ICombineJstPushOrderService iCombineJstPushOrderService;

    /**
     * 推送聚水潭 单据
     * @throws Exception
     */
    @Override
    @XxlJob("PushJstOrdersJob")
    public void execute() throws Exception {
        XxlJobTaskLog.traceLog("推送聚水潭开始");
        iCombineJstPushOrderService.pushJstOrders();
        XxlJobTaskLog.traceLog("推送聚水潭结束");

    }
}

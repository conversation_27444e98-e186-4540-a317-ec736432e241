package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.domain.box.CActivity;
import org.springcenter.box.activity.domain.box.CActivityGift;
import org.springcenter.box.activity.domain.box.CActivityJoinRecord;
import org.springcenter.box.activity.enums.GiftSendStatusEnum;
import org.springcenter.box.activity.mapper.box.CActivityGiftMapper;
import org.springcenter.box.activity.mapper.box.CActivityJoinRecordMapper;
import org.springcenter.box.activity.service.IGiftService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class GiftServiceImpl implements IGiftService {

    @Resource
    private CActivityGiftMapper cActivityGiftMapper;

    @Resource
    private CActivityJoinRecordMapper joinRecordMapper;

    @Override
    public Boolean deductInventory(String giftId) {
        CActivityGift cActivityGift = cActivityGiftMapper.selectById(giftId);
        log.info("扣减赠品库存-获取赠品:{}", JSON.toJSONString(cActivityGift));
        if (cActivityGift == null) {
            throw new BoxActivityException("赠品不存在，请检查");
        }
        if (cActivityGift.getRemainNum() == 0) {
            throw new BoxActivityException("库存不足，无法操作");
        }
        CActivityGift update = new CActivityGift();
        update.setUpdateTime(new Date());
        update.setRemainNum(cActivityGift.getRemainNum() - 1);
        update.setVersionNumber(cActivityGift.getVersionNumber() + 1);
        LambdaQueryWrapper<CActivityGift> whereCondition = new LambdaQueryWrapper<>();
        whereCondition.eq(CActivityGift::getId, giftId);
        whereCondition.eq(CActivityGift::getRemainNum, cActivityGift.getRemainNum());
        whereCondition.eq(CActivityGift::getVersionNumber, cActivityGift.getVersionNumber());
        log.info("扣减赠品库存-update值:{}, where条件:{}, giftId:{}, remainNum:{}, versionNumber:{}",
                JSON.toJSONString(update), JSON.toJSONString(whereCondition.getTargetSql()), giftId, cActivityGift.getRemainNum(), cActivityGift.getVersionNumber());
        int result = cActivityGiftMapper.update(update, whereCondition);
        log.info("扣减赠品库存-结果:{}", result > 0);
        if (result == 0) {
            throw new BoxActivityException("库存不足");
        }
        return result > 0;
    }

    @Override
    public Boolean returnInventory(String giftId) {
        CActivityGift cActivityGift = cActivityGiftMapper.selectById(giftId);
        log.info("撤回赠品库存-获取赠品:{}", JSON.toJSONString(cActivityGift));
        if (cActivityGift == null) {
            throw new BoxActivityException("赠品不存在");
        }
        CActivityGift update = new CActivityGift();
        update.setUpdateTime(new Date());
        update.setRemainNum(cActivityGift.getRemainNum() + 1);
        update.setVersionNumber(cActivityGift.getVersionNumber() + 1);
        LambdaQueryWrapper<CActivityGift> whereCondition = new LambdaQueryWrapper<>();
        whereCondition.eq(CActivityGift::getId, giftId);
        whereCondition.eq(CActivityGift::getRemainNum, cActivityGift.getRemainNum());
        whereCondition.eq(CActivityGift::getVersionNumber, cActivityGift.getVersionNumber());

        log.info("撤回赠品库存-update值:{}, where条件:{}, giftId:{}, remainNum:{}, versionNumber:{}",
                JSON.toJSONString(update), JSON.toJSONString(whereCondition.getTargetSql()), giftId, cActivityGift.getRemainNum(), cActivityGift.getVersionNumber());
        int result = cActivityGiftMapper.update(update, whereCondition);
        log.info("撤回赠品库存-结果:{}", result > 0);
        if (result == 0) {
            throw new BoxActivityException("返还库存失败");
        }
        return result > 0;
    }

    @Override
    public Boolean batchSend(Integer giftType, List<CActivityJoinRecord> needUpdateList) {
        log.info("批量发放 类型:{}, 参与记录:{}", giftType, JSON.toJSONString(needUpdateList));
        for (CActivityJoinRecord joinRecord : needUpdateList) {
            Preconditions.checkArgument(StringUtils.isNotBlank(joinRecord.getId()), "参与记录id不能为空");
            CActivityJoinRecord update = new CActivityJoinRecord();
            update.setUpdateTime(new Date());
            update.setGiftSendStatus(GiftSendStatusEnum.SUCCESS.getCode());
            update.setExchangeUrl(joinRecord.getExchangeUrl());
            update.setExchangeNumber(joinRecord.getExchangeNumber());
            update.setExchangeSecret(joinRecord.getExchangeSecret());
            LambdaQueryWrapper<CActivityJoinRecord> whereCondition = new LambdaQueryWrapper<>();
            whereCondition.eq(CActivityJoinRecord::getId, joinRecord.getId());
            whereCondition.eq(CActivityJoinRecord::getGiftSendStatus, GiftSendStatusEnum.UN_SEND.getCode());
            int updateCount = joinRecordMapper.update(update, whereCondition);
            if (updateCount == 0) {
                throw new BoxActivityException(String.format("发放失败，单号%s", joinRecord.getBoxSn()));
            }
        }
        log.info("批量发放 完成");
        return true;
    }
}

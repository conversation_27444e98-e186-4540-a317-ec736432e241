package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springcenter.box.activity.api.dto.BoxBuilderReq;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.api.dto.CActivityUpdateTaskReq;
import org.springcenter.box.activity.config.IdConfig;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.convert.CActivityConvertor;
import org.springcenter.box.activity.domain.box.*;
import org.springcenter.box.activity.dto.LogDto;
import org.springcenter.box.activity.dto.OrderDto;
import org.springcenter.box.activity.dto.SubOrderDto;
import org.springcenter.box.activity.dto.request.*;
import org.springcenter.box.activity.enums.*;
import org.springcenter.box.activity.mapper.box.*;
import org.springcenter.box.activity.service.*;
import org.springcenter.box.activity.service.crowd.CrowdFilterContext;
import org.springcenter.box.activity.service.crowd.CrowdFilterService;
import org.springcenter.box.activity.util.DateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.springcenter.box.activity.enums.ActivityStatusEnum.EFFECTIVE;
import static org.springcenter.box.activity.enums.ActivityStatusEnum.END;

@Slf4j
@Service
public class CActivityServiceImpl implements ICActivityService {
    @Resource
    private CActivityConvertor cActivityConvertor;
    @Resource(name = "boxTransactionTemplate")
    private TransactionTemplate template;
    @Resource
    private IdConfig idConfig;
    @Resource
    private CActivityMapper cActivityMapper;
    @Resource
    private CActivityGiftMapper cActivityGiftMapper;
    @Resource
    private FashionerMapper fashionerMapper;
    @Resource
    private IStoreCenterHttpService storeCenterHttpService;
    @Resource
    private CrowdFilterService crowdFilterService;
    @Resource
    private BoxMapper boxMapper;
    @Resource
    private CActivityJoinRecordMapper cActivityJoinRecordMapper;
    @Resource
    private IOrderService orderService;
    @Resource
    private IGiftService giftService;
    @Resource
    private IJoinRecordService joinRecordService;
    @Resource
    private ILogService logService;
    @Resource
    private IProductHttpService iProductHttpService;
    @Resource
    private IStoreCenterHttpService iStoreCenterHttpService;


    @Override
    public String activityCreated(CActivityCreateReq requestData) {
        String activityId = idConfig.getActivityId();
        String giftId = idConfig.getGiftId();
        CActivity activity = cActivityConvertor.req2Activity(requestData, activityId);
        Date now = new Date();
        activity.setCreateTime(now);
        activity.setUpdateTime(now);
        activity.setStatus(1);
        CActivityGift gift = cActivityConvertor.req2Gift(requestData.getGift(), giftId);
        gift.setActivityId(activityId);
        gift.setRemainNum(gift.getTotalNum());
        gift.setCreateTime(now);
        gift.setUpdateTime(now);
        log.info("创建ID为的[{}]新活动:{}", activityId, JSON.toJSONString(activity));
        log.info("创建ID为的[{}]新赠品:{}", giftId, JSON.toJSONString(gift));
        template.execute(action -> {
            cActivityMapper.insert(activity);
            cActivityGiftMapper.insert(gift);
//            logService.saveLog(activityId, OptTypeEnum.ACTIVITY_CREATE, null, null, new LogDto(activity));
            return true;
        });
        return activityId;
    }

    @Override
    public List<CActivityInfoResp> activityList(CActivityListReq requestData, Page page) {
        // 活动状态判断
        FrontActivityStatusEnum statusEnum = FrontActivityStatusEnum.getByCode(requestData.getActivityStatus());

        LambdaQueryWrapper<CActivity> queryWrapper = new LambdaQueryWrapper<>();
        if (!requestData.isAdminRequest()) {
            queryWrapper.eq(CActivity::getIsDelete, 0);
        }
        queryWrapper.like(StringUtils.isNotBlank(requestData.getActivityNameLike()), CActivity::getActivityName, requestData.getActivityNameLike());
        queryWrapper.orderByDesc(CActivity::getCreateTime);

        Date now = new Date();
        switch (statusEnum) {
            case NOT_START:
                queryWrapper.eq(CActivity::getStatus, EFFECTIVE.getCode());
                queryWrapper.ge(CActivity::getStartTime, now);
                queryWrapper.eq(CActivity::getIsDelete, 0);
                break;
            case RUNNING:
                queryWrapper.eq(CActivity::getStatus, EFFECTIVE.getCode());
                queryWrapper.lt(CActivity::getStartTime, now);
                queryWrapper.gt(CActivity::getEndTime, now);
                queryWrapper.eq(CActivity::getIsDelete, 0);
                break;
            case ACCOUNTING:
                queryWrapper.eq(CActivity::getStatus, EFFECTIVE.getCode());
                queryWrapper.lt(CActivity::getEndTime, now);
                queryWrapper.gt(CActivity::getEvaluateTime, now);
                queryWrapper.eq(CActivity::getIsDelete, 0);
                break;
            case WAITING:
                queryWrapper.eq(CActivity::getStatus, EFFECTIVE.getCode());
                queryWrapper.lt(CActivity::getEvaluateTime, now);
                queryWrapper.eq(CActivity::getIsDelete, 0);
                break;
            case ENDING:
                queryWrapper.eq(CActivity::getStatus, END.getCode());
                queryWrapper.eq(CActivity::getIsDelete, 0);
                break;
            case DOWN:
                queryWrapper.eq(CActivity::getIsDelete, 1);
                break;
            default:
                break;
        }

        // 导购/搭配师
        BoxBuilderReq boxBuilderReq = requestData.getBoxBuilderReq();
        Integer builderType = null;
        String guiderId;
        String cStoreId = null;
        if (boxBuilderReq != null) {
            builderType = boxBuilderReq.getBuilderType();
            guiderId = String.valueOf(boxBuilderReq.getFashionerId());
            if (BoxBuiderEnum.isFashioner(builderType)) {
                // 搭配师
                queryWrapper.eq(CActivity::getBoxBuilderFashioner, true);
            } else if (BoxBuiderEnum.isGuider(builderType)) {
                // 导购 - 查询门店包
                queryWrapper.eq(CActivity::getBoxBuilderGuide, true);
                cStoreId = getStoreIdByFashionerId(guiderId);
            }
        }

        log.info("活动信息查询 DB入参:{}", JSON.toJSONString(queryWrapper));
        com.github.pagehelper.Page<CActivity> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        List<CActivity> activityList = cActivityMapper.selectList(queryWrapper);
        log.info("活动信息查询 DB结果:{}", JSON.toJSONString(activityList));

        // 根据活动ID查询所有赠品
        List<String> activityIds = activityList.stream().map(CActivity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activityIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CActivityGift> giftQueryWrapper = new LambdaQueryWrapper<>();
        giftQueryWrapper.in(CActivityGift::getActivityId, activityIds);
        List<CActivityGift> giftList = cActivityGiftMapper.selectList(giftQueryWrapper);
        log.info("赠品信息查询 DB结果:{}", JSON.toJSONString(giftList));
        Map<String, CActivityGift> giftMap = giftList.stream()
                .collect(Collectors.toMap(CActivityGift::getActivityId, gift -> gift,
                        (CActivityGift o1, CActivityGift o2) -> o1));

        String finalCStoreId = cStoreId;
        Integer finalBuilderType = builderType;
        List<CActivityInfoResp> respList = Optional.ofNullable(activityList).orElse(Lists.newArrayList())
                .stream().filter(activity -> filterBuilderType(finalBuilderType, activity, finalCStoreId))
                .map(a -> cActivityConvertor.activity2InfoResp(a, giftMap.get(a.getId())))
                .filter(a -> filterZeroStock(requestData, a))
                .collect(Collectors.toList());

        PageInfo<CActivityInfoResp> pageInfo = new PageInfo(hPage);
        pageInfo.setList(respList);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();
    }

    private static boolean filterZeroStock(CActivityListReq requestData, CActivityInfoResp a) {
        if (!requestData.isFilterZeroStock()) {
            return true;
        } else {
            if (a.getGift() != null && a.getGift().getRemainNum() != null && a.getGift().getRemainNum() > 0) {
                return true;
            }
        }
        log.info("活动ID[{}]因库存不足不返回", a.getId());
        return false;
    }

    @Override
    public CActivityInfoResp activityInfo(String activityId, Boolean isAdmin) {
        Preconditions.checkArgument(StringUtils.isNotBlank(activityId), "活动ID不能为空");

        LambdaQueryWrapper<CActivity> activityQuery = new LambdaQueryWrapper<CActivity>();
        activityQuery.eq(CActivity::getId, activityId);
        if (!Boolean.TRUE.equals(isAdmin)) {
            activityQuery.eq(CActivity::getIsDelete, 0);
        }
        CActivity activity = cActivityMapper.selectOne(activityQuery);
        Preconditions.checkArgument(activity != null, "活动已失效");
        log.info("从DB查询活动详情:{}", JSON.toJSONString(activity));

        LambdaQueryWrapper<CActivityGift> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CActivityGift::getActivityId, activityId);
        CActivityGift cActivityGift = cActivityGiftMapper.selectOne(queryWrapper);
        log.info("从DB查询赠品详情:{}", JSON.toJSONString(cActivityGift));
        return cActivityConvertor.activity2InfoResp(activity, cActivityGift);
    }

    @Override
    public void activityCancel(String activityId, String optPerson) {
        Preconditions.checkArgument(StringUtils.isNotBlank(activityId), "活动ID不能为空");
        // 修改活动状态
        CActivity activity = new CActivity();
        activity.setId(activityId);
        activity.setStatus(END.getCode());
        activity.setUpdateTime(new Date());
        template.execute(action -> {
            cActivityMapper.updateById(activity);
            joinRecordService.joinRecordCancel(activityId, optPerson);
            logService.saveLog(activityId, OptTypeEnum.ACTIVITY_INVALID, optPerson, null, new LogDto(activity));
            return true;
        });
    }

    @Override
    public void activityDown(String activityId, String optPerson) {
        Preconditions.checkArgument(StringUtils.isNotBlank(activityId), "活动ID不能为空");
        // 修改活动状态
        CActivity activity = new CActivity();
        activity.setId(activityId);
        activity.setIsDelete(1);
        activity.setUpdateTime(new Date());
        template.execute(action -> {
            cActivityMapper.updateById(activity);
            logService.saveLog(activityId, OptTypeEnum.ACTIVITY_DOWN, optPerson, null, new LogDto(activity));
            return true;
        });
    }

    @Override
    public Boolean checkInventory(String activityId) {
        CActivityInfoResp cActivityInfoResp = activityInfo(activityId, false);
        if (cActivityInfoResp == null || cActivityInfoResp.getGift() == null || cActivityInfoResp.getGift().getRemainNum() == null) {
            return false;
        }
        return cActivityInfoResp.getGift().getRemainNum() > 0;
    }

    @Override
    public List<CActivityInfoResp> effectActivity(CActivityEffectReq requestData) {
        // 过滤0元商品
        List<CActivityEffectReq.ProductReq> filterItems = requestData.getProductList().stream().filter(p -> BigDecimal.ZERO.compareTo(p.getPayment()) < 0).collect(Collectors.toList());
        log.info("处理过滤0元商品后的商品数据:{}", JSON.toJSONString(filterItems));
        requestData.setProductList(filterItems);
        String boxNo = requestData.getBoxNo();
        String unionId = requestData.getUnionId();
        String boxId = requestData.getBoxId();
        LambdaQueryWrapper<Box> boxQuery = new LambdaQueryWrapper<Box>()
                .eq(Box::getBoxSn, boxNo).eq(Box::getUnionid, unionId).in(Box::getType, Lists.newArrayList(10,20));
        if (StringUtils.isNotBlank(boxId)) {
            boxQuery = new LambdaQueryWrapper<Box>().eq(Box::getId, boxId).eq(Box::getUnionid, unionId);
        }
        // 是否有BOX单对应的参与记录，有则不能参加活动
        Box box = boxMapper.selectOne(boxQuery);
        if (box == null) {
            throw new BoxActivityException("该BOX单不存在");
        }
        // 同一个BOX单参加多个活动的情况
        checkOneBoxTwoActivity(box.getBoxSn());

        // 判断当前导购所属门店
        BoxBuilderReq boxBuilderReq = requestData.getBoxBuilderReq();
        String cStoreId = null;
        if (boxBuilderReq != null) {
            Integer builderType = boxBuilderReq.getBuilderType();
            String guiderId = boxBuilderReq.getFashionerId();
            if (BoxBuiderEnum.isGuider(builderType)) {
                cStoreId = getStoreIdByFashionerId(guiderId);
            }
        }

        // 进行中的有效活动(默认查询 50 个活动)
        CActivityListReq listReq = new CActivityListReq();
        listReq.setActivityStatus(FrontActivityStatusEnum.RUNNING.getCode());
        List<CActivityInfoResp> rspList = activityList(listReq, new Page(1, 50));
        if (CollectionUtils.isEmpty(rspList)) {
            log.info("当前没有进行中的活动");
            return Collections.emptyList();
        }

        // 是否存在DD单（新老客判断）存在交易行为，退款也算为交易行为）ToDo：11.12
        Order order = orderService.getOrderByUnionId(box.getUnionid());

        List<String> orderIds = orderService.listOrderIdByBoxSn(box.getBoxSn());

        Iterator<CActivityInfoResp> iterator = rspList.iterator();
        while (iterator.hasNext()) {
            CActivityInfoResp activity = iterator.next();
            log.info("开始判断活动[{}-{}]", activity.getId(), activity.getActivityName());
            // 活动条件过滤器
            CrowdFilterContext context = new CrowdFilterContext();
            context.setActivityInfo(activity);
            context.setBox(box);
            context.setOrderIds(orderIds);
            context.setHasOrder(order != null);
            context.setBoxBuilderReq(requestData.getBoxBuilderReq());
            context.setGuiderStoreId(cStoreId);
            // 深拷贝数据，因为在商品过滤的时候需要对原数据进行过滤，如果不拷贝数据的话，过滤的将是传入的数据，会导致空指针问题。
            List<CActivityEffectReq.ProductReq> productIds = new ArrayList<>(requestData.getProductList());
            context.setProductList(productIds);
            context.setIProductHttpService(iProductHttpService);
            context.setIStoreCenterHttpService(iStoreCenterHttpService);
            if (!crowdFilterService.applyFilters(context)) {
                log.info("活动[{}]-[{}], 不满足条件，错误原因[{}]，跳过执行", activity.getId(),
                        activity.getActivityName(), context.getErrorMsg());
                iterator.remove();
                continue;
            }

            // 实付计算
            OrderReq orderReq = new OrderReq();
            orderReq.setBoxSn(box.getBoxSn());
            orderReq.setCreateStartTime(DateUtil.getStrToTime(activity.getStartTime()));
            orderReq.setCreateEndTime(DateUtil.getStrToTime(activity.getEndTime()));
            // 历史符合商品条件的累计金额+当前符合商品条件的金额>门槛
            if (ItemRangeEnum.isFilterItemAndAccomplish(activity.getBoxBrandType())) {
                // 获取商品包
                orderReq.setPacIds(Splitter.on(",").splitToList(activity.getBoxBrandList()));
                orderReq.setNeedFilterItem(true);
            }

            List<OrderDto> orderDtos = orderService.listOrderDto(orderReq);
            // 计算实付金额
            BigDecimal historyAmount = orderDtos.stream().map(OrderDto::getPayment).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 如果是指定商品过滤，在productFilter里已过滤不符合的商品，如果是全部商品，则直接是前端传入的预计实付
            BigDecimal filterPayment = context.getProductList().stream().map(CActivityEffectReq.ProductReq::getPayment).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalAmount = filterPayment.add(historyAmount);
            BigDecimal threshold = activity.getThreshold();

            // 计算实付件数
            Integer historyQuantity = orderDtos.stream().map(OrderDto::getQuantity).reduce(0, Integer::sum);
            Integer filterQuantity = context.getProductList().stream().map(CActivityEffectReq.ProductReq::getQuantity).reduce(0, Integer::sum);
            Integer totalQuantity = filterQuantity + historyQuantity;

            log.info("活动[{}]-[{}], 本次预计实付金额[{}], 历史实付[{}], 累计实付[{}], 本次预计实付件数[{}], 历史实付件数[{}], 累计实付件数[{}]",
            activity.getId(), activity.getActivityName(), filterPayment, historyAmount, totalAmount, filterQuantity, historyQuantity, totalQuantity);

            // 是否达成
            Boolean accomplish = calcThreshold(activity.getUnit(), activity.getThreshold(), totalAmount, totalQuantity);

            // 是否满足门槛后符合其中之一的商品条件
            if (ItemRangeEnum.isAccomplishAndIncludeItem(activity.getBoxBrandType()) && accomplish) {
                log.info("满足门槛且含指定商品，需要校验有效订单中是否包含任意一个指定商品");
                List<Long> reqSpuId = context.getProductList().stream().map(CActivityEffectReq.ProductReq::getSpuId).distinct().collect(Collectors.toList());
                accomplish = calcThresholdByAccomplishAndIncludeItem(activity, orderDtos, reqSpuId);
            }

            if (!accomplish) {
                log.info("活动[{}]-[{}], 门槛[{}]，当前累计实付[{}]，不满足", activity.getId(), activity.getActivityName(), threshold, totalAmount);
                iterator.remove();
            }
            log.info("活动[{}]-[{}], 满足条件", activity.getId(), activity.getActivityName());
        }

        // 按门槛倒序
        if (CollectionUtils.isNotEmpty(rspList)) {
            rspList.sort(Comparator.comparing(CActivityInfoResp::getThreshold).reversed());
        }

        return rspList;
    }

    /**
     * 同一个BOX单参加多个活动的情况
     * @param boxNo 当前BOX单
     */
    private void checkOneBoxTwoActivity(String boxNo) {
        LambdaQueryWrapper<CActivityJoinRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CActivityJoinRecord::getBoxSn, boxNo);
        queryWrapper.eq(CActivityJoinRecord::getIsDelete, 0);
        queryWrapper.last("and rownum = 1");
        CActivityJoinRecord joinRecord = cActivityJoinRecordMapper.selectOne(queryWrapper);
        if (joinRecord != null) {
            throw new BoxActivityException("该BOX单已参与过活动:" + joinRecord.getActivityId());
        }
    }

    @Override
    public void updateTask(CActivityUpdateTaskReq requestData) {
        String boxNo = requestData.getBoxNo();
        String unionId = requestData.getUnionId();
        Integer type = requestData.getType();
        String activityId = requestData.getActivityId();
        LambdaQueryWrapper<Box> boxQuery = new LambdaQueryWrapper<Box>()
                .eq(Box::getBoxSn, boxNo).in(Box::getType, Lists.newArrayList(10,20));
        if (StringUtils.isNotBlank(unionId)) {
            boxQuery.eq(Box::getUnionid, unionId);
        }

        Box box = boxMapper.selectOne(boxQuery);
        log.info("BOX服务单:{}", JSON.toJSONString(box));
        if (box == null || StringUtils.isBlank(box.getUnionid())) {
            log.info("该BOX单不存在，不处理");
            return;
        }
        log.info("触发类型为:[{}]", (type != null && type == 1) ? "订单支付" : "订单退款");
        // 如果没有活动ID，则代表可能是退款订单，或者重新下单的订单，需要先考虑是否有参与记录，如果没有，则不处理
        if (StringUtils.isBlank(activityId)) {
            LambdaQueryWrapper<CActivityJoinRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CActivityJoinRecord::getBoxSn, box.getBoxSn());
            queryWrapper.eq(CActivityJoinRecord::getIsDelete, 0);
            CActivityJoinRecord joinRecord = cActivityJoinRecordMapper.selectOne(queryWrapper);
            log.info("没有活动ID的情况根据BOX单号获取历史活动参与记录:{}", JSON.toJSONString(joinRecord));
            if (joinRecord == null) {
                log.info("没有活动ID的情况校验参与记录为空，不处理");
                return;
            }
            activityId = joinRecord.getActivityId();
        }
        if (StringUtils.isBlank(activityId)) {
            log.info("活动ID不存在，不处理");
            return;
        }

        // 活动有效性校验
        CActivityInfoResp activity = activityInfo(activityId, false);
        log.info("查询活动详情:{}", JSON.toJSONString(activity));
        if (activity == null || !FrontActivityStatusEnum.canUpdate(activity.getStatus())) {
            log.info("活动不存在或活动已结束，不处理");
            return;
        }
        // 人群再次核算（在有效活动页展示活动的时候其实就已经符合活动了，所以这里不需要不校验人群）

        // BOX单下的实付金额
        OrderReq orderReq = new OrderReq();
        orderReq.setBoxSn(box.getBoxSn());
        orderReq.setCreateStartTime(DateUtil.getStrToTime(activity.getStartTime()));
        orderReq.setCreateEndTime(DateUtil.getStrToTime(activity.getEndTime()));
        if (ItemRangeEnum.isFilterItemAndAccomplish(activity.getBoxBrandType())) {
            // 获取商品包
            orderReq.setPacIds(Splitter.on(",").splitToList(activity.getBoxBrandList()));
            orderReq.setNeedFilterItem(true);
        }
        List<OrderDto> orderDtoList = orderService.listOrderDto(orderReq);
        BigDecimal payment = orderDtoList.stream().map(OrderDto::getPayment).reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer quantity = orderDtoList.stream().map(OrderDto::getQuantity).reduce(0, Integer::sum);
        String orderNoList = StringUtils.join(Optional.ofNullable(orderDtoList).orElse(Collections.emptyList())
                .stream().map(OrderDto::getOrderSn).collect(Collectors.toList()), ",");

        log.info("当前累计实付金额:[{}], 累计件数:[{}], 累计订单编号:[{}]", payment, quantity, orderNoList);

        // 根据BOX单ID查询参与记录
        LambdaQueryWrapper<CActivityJoinRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CActivityJoinRecord::getBoxSn, box.getBoxSn());
        queryWrapper.eq(CActivityJoinRecord::getActivityId, activityId);
        queryWrapper.eq(CActivityJoinRecord::getIsDelete, 0);
        CActivityJoinRecord joinRecord = cActivityJoinRecordMapper.selectOne(queryWrapper);
        log.info("当前活动参与记录:{}", JSON.toJSONString(joinRecord));
        boolean first;
        if (joinRecord == null) {
            // 判断同一个BOX单参与多个活动的情况
            checkOneBoxTwoActivity(box.getBoxSn());
            // 判断同一个人是否有多个BOX单参与了该活动
            if (checkOnePersonTwoBoxInActivity(box.getUnionid(), activityId, box.getBoxSn())) {
                log.info("同一个消费者只能参与一次本活动");
                return;
            }
            first = true;
            joinRecord = joinRecordService.buildJoinRecord(AccomplishStatusEnum.UN_ACCOMPLISH, box, activity, orderNoList, payment, quantity);
        } else {
            first = false;
            // 覆盖实付金额、订单号
            joinRecord.setPayment(payment);
            joinRecord.setQuantity(quantity);
            joinRecord.setOrderNoList(orderNoList);
        }
        // 是否达成
        Boolean accomplish = calcThreshold(activity.getUnit(), activity.getThreshold(), payment, quantity);
        // 是否满足门槛后符合其中之一的商品条件
        if (ItemRangeEnum.isAccomplishAndIncludeItem(activity.getBoxBrandType()) && accomplish) {
            log.info("满足门槛且含指定商品，需要校验有效订单中是否包含任意一个指定商品");
            accomplish = calcThresholdByAccomplishAndIncludeItem(activity, orderDtoList, null);
            if (!accomplish) {
                joinRecord.setPayment(BigDecimal.ZERO);
                joinRecord.setQuantity(0);
                joinRecord.setOrderNoList("");
                log.info("清空有效订单");
            }
        }

        CActivityJoinRecord finalJoinRecord = joinRecord;
        Boolean finalAccomplish = accomplish;
        template.execute(action -> {
            update(finalAccomplish, activity, finalJoinRecord, first);
            return true;
        });
    }

    private Boolean calcThresholdByAccomplishAndIncludeItem(CActivityInfoResp activity, List<OrderDto> orderDtoList, List<Long> reqSpuId) {
        // 活动指定的商品包
        List<String> pacIds = Splitter.on(",").splitToList(activity.getBoxBrandList());
        // 从订单列表中的子单列表取出商品id
        List<Long> productIds = orderDtoList.stream().map(OrderDto::getSubOrders).flatMap(Collection::stream)
                .map(SubOrderDto::getProductId).map(Long::parseLong).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reqSpuId)) {
            productIds.addAll(reqSpuId);
        }
        // 获取所有选择的商品是否命中商品包
        Map<Long, Integer> productId2Exist = iProductHttpService.getProductId2ExistMap(pacIds, productIds);

        boolean present = productIds.stream().filter(pid -> productId2Exist.get(pid) == 1).findAny().isPresent();
        if (!present) {
            log.info("指定商品都已退款");
            return false;
        }
        return true;
    }

    private static Boolean calcThreshold(Integer unit, BigDecimal threshold, BigDecimal payment, Integer quantity) {
        Boolean accomplish;
        UnitEnum unitEnum = UnitEnum.getUnitEnum(unit);
        switch (unitEnum) {
            case AMOUNT:
                if (payment.compareTo(threshold) >= 0) {
                    accomplish = true;
                } else {
                    accomplish = false;
                }
                break;
            case QUANTITY:
                if (quantity >= threshold.intValue()) {
                    accomplish = true;
                } else {
                    accomplish = false;
                }
                break;
            default:
                throw new BoxActivityException("活动单位不支持");
        }
        log.info("计算阶梯 是否达成:{} 配置条件:满[{}]{}, 当前金额:[{}], 当前件数[{}] ",
                accomplish, threshold, unitEnum.getMsg(), payment, quantity);
        return accomplish;
    }

    /**
     * 判断同一个人是否有多个BOX单参与了该活动
     *
     * @param unionId    消费者
     * @param activityId 当前活动ID
     * @param boxNo      当前BOX单
     * @return
     */
    private boolean checkOnePersonTwoBoxInActivity(String unionId, String activityId, String boxNo) {
        LambdaQueryWrapper<CActivityJoinRecord> unionQuery = new LambdaQueryWrapper<>();
        unionQuery.eq(CActivityJoinRecord::getUnionId, unionId);
        unionQuery.eq(CActivityJoinRecord::getActivityId, activityId);
        unionQuery.ne(CActivityJoinRecord::getBoxSn, boxNo);
        unionQuery.eq(CActivityJoinRecord::getIsDelete, 0);
        unionQuery.last("and rownum = 1");
        CActivityJoinRecord unionRecord = cActivityJoinRecordMapper.selectOne(unionQuery);
        if (unionRecord != null) {
            return true;
        }
        return false;
    }

    @Override
    public void deliveryJobSubmit(CActivityUpdateReq req) {
        String activityId = req.getActivityId();
        String optPerson = req.getOptPerson();
        CActivityInfoResp activity = activityInfo(activityId, false);
        if (activity == null) {
            throw new BoxActivityException("活动不存在");
        }
        if (!FrontActivityStatusEnum.canSend(activity.getStatus())) {
            throw new BoxActivityException("活动不是待发放状态");
        }
        if (!ActivityGiftSendTaskStatusEnum.isWaitCalc(activity.getGiftSendTaskStatus())) {
            throw new BoxActivityException("已经触发过一键发货，请勿重复提交");
        }
        Date evaluateTime = DateUtil.formatToDate(activity.getEvaluateTime(), DateUtil.DATE_FORMAT_YMDHM);
        Date date = new Date();
        if (date.before(evaluateTime)) {
            throw new BoxActivityException("活动时间不在待发放阶段");
        }
        CActivity cActivity = new CActivity();
        cActivity.setId(activityId);
        cActivity.setGiftSendTaskStatus(ActivityGiftSendTaskStatusEnum.WAIT_SEND.getCode());
        cActivity.setUpdateTime(date);
        log.info("提交一键发货任务，等待任务调度。");
        template.execute(action -> {
            cActivityMapper.updateById(cActivity);
            logService.saveLog(activityId, OptTypeEnum.JOIN_RECORD_SEND_TASK, optPerson, null, new LogDto(cActivity));
            return true;
        });
    }

    @Override
    public void deliveryJobExecute() {
        // 获取待执行的活动
        LambdaQueryWrapper<CActivity> activityQueryWrapper = new LambdaQueryWrapper<>();
        activityQueryWrapper.eq(CActivity::getIsDelete, 0);
        activityQueryWrapper.eq(CActivity::getGiftSendTaskStatus, ActivityGiftSendTaskStatusEnum.WAIT_SEND.getCode());
        activityQueryWrapper.eq(CActivity::getStatus, EFFECTIVE.getCode());
        activityQueryWrapper.last("and rownum = 1");
        CActivity activity = cActivityMapper.selectOne(activityQueryWrapper);
        if (Objects.isNull(activity)) {
            log.info("没有待执行的任务，本轮调度执行结束");
            return;
        }

        // 乐观锁更新成功
        String activityId = activity.getId();
        CActivity updateActivity = new CActivity();
        updateActivity.setGiftSendTaskStatus(ActivityGiftSendTaskStatusEnum.SEND_ING.getCode());
        updateActivity.setUpdateTime(new Date());
        LambdaQueryWrapper<CActivity> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(CActivity::getId, activityId);
        updateWrapper.eq(CActivity::getGiftSendTaskStatus, ActivityGiftSendTaskStatusEnum.WAIT_SEND.getCode());
        boolean updateResult = cActivityMapper.update(updateActivity, updateWrapper) > 0;
        if (!updateResult) {
            log.info("乐观锁更新失败，本轮调度执行结束，activityId[{}]", activityId);
            return;
        }
        log.info("乐观锁更新成功，activityId[{}]，活动详情:{}", activityId, JSON.toJSONString(activity));

        // 获取赠品
        LambdaQueryWrapper<CActivityGift> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CActivityGift::getActivityId, activityId);
        CActivityGift gift = cActivityGiftMapper.selectOne(queryWrapper);
        log.info("活动赠品:{}", JSON.toJSONString(gift));

        // 获取所有可发状态的参与记录
        if (!joinRecordService.doDelivery(activity, gift)) {
            CActivity endActivity = new CActivity();
            endActivity.setId(activity.getId());
            endActivity.setStatus(ActivityStatusEnum.END.getCode());
            endActivity.setUpdateTime(new Date());
            int endResult = cActivityMapper.updateById(endActivity);
            log.info("活动状态更新为结束,endResult[{}]", endResult);
            return;
        }
        CActivity endActivity = new CActivity();
        endActivity.setId(activityId);
        endActivity.setStatus(ActivityStatusEnum.END.getCode());
        endActivity.setUpdateTime(new Date());
        int endResult = cActivityMapper.updateById(endActivity);
        log.info("活动状态更新为结束,endResult[{}]", endResult);
    }

    @Override
    public void activityEndByWaitSend(String activityId, String optPerson) {
        Preconditions.checkArgument(StringUtils.isNotBlank(activityId), "活动id不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(optPerson), "操作人不能为空");
        CActivity dbActivity = cActivityMapper.selectById(activityId);
        Preconditions.checkArgument(Objects.nonNull(dbActivity), "活动不存在");
        CActivity activity = new CActivity();
        activity.setId(activityId);
        activity.setStatus(END.getCode());
        activity.setUpdateTime(new Date());
        activity.setGiftSendTaskStatus(ActivityGiftSendTaskStatusEnum.SEND_ING.getCode());
        cActivityMapper.updateById(activity);
        logService.saveLog(activityId, OptTypeEnum.JOIN_RECORD_SEND_TASK, optPerson, new LogDto(dbActivity), new LogDto(activity));

    }

    private void update(Boolean accomplish, CActivityInfoResp activity, CActivityJoinRecord finalJoinRecord, boolean first) {
        if (accomplish) {
            // 达成
            if (AccomplishStatusEnum.isAccomplish(finalJoinRecord.getAccomplishStatus())) {
                log.info("历史参与记录已达成，本次计算达成状态是已达成，仅更新发放记录:{}", JSON.toJSONString(finalJoinRecord));
                finalJoinRecord.setUpdateTime(new Date());
                cActivityJoinRecordMapper.updateById(finalJoinRecord);
            } else {
                log.info("历史参与记录未达成，本次计算达成状态是已达成，发放奖品。发放记录:{}", JSON.toJSONString(finalJoinRecord));
                // 发放奖品
                giftService.deductInventory(activity.getGift().getId());
                finalJoinRecord.setAccomplishStatus(AccomplishStatusEnum.ACCOMPLISH.getCode());
                finalJoinRecord.setUpdateTime(new Date());
                // 首次达成，新增记录
                if (first) {
                    cActivityJoinRecordMapper.insert(finalJoinRecord);
                } else {
                    cActivityJoinRecordMapper.updateById(finalJoinRecord);
                }
                logService.saveLog(activity.getId(), OptTypeEnum.TASK_ACCOMPLISH, null, null, new LogDto(finalJoinRecord), finalJoinRecord.getId());
            }
        } else {
            // 未达成
            if (AccomplishStatusEnum.isAccomplish(finalJoinRecord.getAccomplishStatus())) {
                log.info("历史参与记录已达成，本次计算达成状态是未达成，撤回奖品。发放记录:{}", JSON.toJSONString(finalJoinRecord));
                // 撤回奖品
                giftService.returnInventory(activity.getGift().getId());
                finalJoinRecord.setAccomplishStatus(AccomplishStatusEnum.UN_ACCOMPLISH.getCode());
                finalJoinRecord.setUpdateTime(new Date());
                cActivityJoinRecordMapper.updateById(finalJoinRecord);
                logService.saveLog(activity.getId(), OptTypeEnum.TASK_BACK, null, null, new LogDto(finalJoinRecord), finalJoinRecord.getId());
            } else {
                log.info("历史参与记录未达成，本次计算达成状态是未达成，仅更新发放记录:{}", JSON.toJSONString(finalJoinRecord));
                finalJoinRecord.setUpdateTime(new Date());
                cActivityJoinRecordMapper.updateById(finalJoinRecord);
            }
        }
    }

    private String getStoreIdByFashionerId(String fashionerId) {
        // 导购 - 查询门店包
        LambdaQueryWrapper<Fashioner> fashionerQueryWrapper = new LambdaQueryWrapper<>();
        fashionerQueryWrapper.eq(Fashioner::getId, fashionerId);
        Fashioner fashioner = fashionerMapper.selectOne(fashionerQueryWrapper);
        if (fashioner == null || StringUtils.isBlank(fashioner.getCStoreId())) {
            throw new BoxActivityException("导购不存在");
        }
        return fashioner.getCStoreId();
    }

    private boolean filterBuilderType(Integer builderType, CActivity activity, String finalCStoreId) {
        log.info("搭盒人员过滤活动，activityId:{},builderType:{},finalCStoreId:{},活动所选择的门店包:{}",
                activity.getId(), builderType, finalCStoreId, activity.getBoxBuilderGuideStore());
        if (builderType == null || builderType != 2) {
            // 如果builderType不是2，或者为null，则不进行过滤
            return true;
        }
        String boxBuilderGuideStore = activity.getBoxBuilderGuideStore();
        if (StringUtils.isBlank(boxBuilderGuideStore)) {
            throw new BoxActivityException("导购类型未选择门店包，无法判断");
        }

        Boolean isMatch = storeCenterHttpService.hitStore(Long.valueOf(boxBuilderGuideStore), Long.valueOf(finalCStoreId));
        log.info("搭盒人员过滤活动，activityId:{}，是否匹配门店包:{}", activity.getId(), isMatch);
        return isMatch;
    }

    @Override
    public boolean addGiftStock(String giftId, Integer addNum, String optPerson) {
        // 参数校验
        if (StringUtils.isBlank(giftId)) {
            throw new BoxActivityException("赠品ID不能为空");
        }
        if (addNum == null || addNum <= 0) {
            throw new BoxActivityException("更新数量必须大于0");
        }

        // 查询活动赠品
        CActivityGift gift = cActivityGiftMapper.selectById(giftId);
        if (gift == null || gift.getIsDelete() != 0) {
            throw new BoxActivityException("赠品不存在");
        }
        log.info("赠品原信息,totalNum[{}],remainNum[{}],versionNumber[{}]", gift.getTotalNum(), gift.getRemainNum(), gift.getVersionNumber());

        // 校验活动状态 - 只有未开始和进行中可以修改库存
        CActivityInfoResp activityInfo = activityInfo(gift.getActivityId(), false);
        if (activityInfo == null) {
            throw new BoxActivityException("活动不存在");
        }
        
        // 活动状态校验
        Integer status = activityInfo.getStatus();
        if (!FrontActivityStatusEnum.canAddGiftStock(status)) {
            throw new BoxActivityException("活动状态已变更,不能再修改库存");
        }

        // 乐观锁更新
        CActivityGift updateGift = new CActivityGift();
        updateGift.setId(giftId);
        updateGift.setTotalNum(gift.getTotalNum() + addNum);
        updateGift.setRemainNum(gift.getRemainNum() + addNum);
        updateGift.setVersionNumber(gift.getVersionNumber() + 1);
        updateGift.setUpdateTime(new Date());

        boolean success = template.execute(action -> {
            int result = cActivityGiftMapper.update(updateGift,
                    new LambdaQueryWrapper<CActivityGift>()
                            .eq(CActivityGift::getId, giftId)
                            .eq(CActivityGift::getTotalNum, gift.getTotalNum())
                            .eq(CActivityGift::getRemainNum, gift.getRemainNum())
                            .eq(CActivityGift::getVersionNumber, gift.getVersionNumber()));
            log.info("更新赠品库存,result[{}]", result);
            if (result > 0) {
                // 记录操作日志
                logService.saveLog(gift.getActivityId(), OptTypeEnum.GIFT_STOCK_ADD, optPerson, new LogDto(gift), new LogDto(updateGift));
                return true;
            }
            return false;
        });

        if (!success) {
            throw new BoxActivityException("更新失败,库存可能已变更,请重试");
        }
        
        return true;
    }

}

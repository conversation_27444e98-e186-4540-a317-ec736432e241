package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jnbyframework.boot.api.ISysBaseAPI;
import com.jnbyframework.boot.common.api.dto.message.MessageDTO;
import com.jnbyframework.boot.common.api.dto.message.TemplateMessageDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.domain.box.SysMessage;
import org.springcenter.box.activity.domain.box.SysMessageTemplate;
import org.springcenter.box.activity.enums.SendMsgStatusEnum;
import org.springcenter.box.activity.mapper.box.SysMessageMapper;
import org.springcenter.box.activity.service.ISysMessageService;
import org.springcenter.box.activity.service.ISysMessageTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 消息
 * @Author: jeecg-boot
 * @Date:  2019-04-09
 * @Version: V1.0
 */
@Service
public class SysMessageServiceImpl extends ServiceImpl<SysMessageMapper, SysMessage> implements ISysMessageService {

    private static final Logger logger = LoggerFactory.getLogger(SysMessageServiceImpl.class);

    @Autowired
    private ISysMessageTemplateService sysMessageTemplateService;


    @Override
    public String sendMessageByTemplate(TemplateMessageDTO message) {
        String templateCode = message.getTemplateCode();
        Map<String,String> map = message.getTemplateParam();

        List<SysMessageTemplate> sysSmsTemplates = sysMessageTemplateService.selectByCode(templateCode);
        if(sysSmsTemplates==null||sysSmsTemplates.size()==0){
            throw new BoxActivityException("消息模板不存在，模板编码："+templateCode);
        }
        SysMessageTemplate sysSmsTemplate = sysSmsTemplates.get(0);
        //模板内容
        String content = sysSmsTemplate.getTemplateContent();
        if(map!=null) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                String str = "${" + entry.getKey() + "}";
                content = content.replace(str, entry.getValue());
            }
        }
        SysMessage sysMessage = new SysMessage();
        sysMessage.setId(IdWorker.get32UUID());
        sysMessage.setEsReceiver(message.getToUser());
        sysMessage.setEsContent(content);
        sysMessage.setEsParam(JSON.toJSONString(map));
        sysMessage.setEsType(sysSmsTemplate.getTemplateType());
        sysMessage.setEsTitle(sysSmsTemplate.getTemplateCode());
        sysMessage.setEsSendTime(new Date());
        sysMessage.setEsSendStatus(SendMsgStatusEnum.WAIT.getCode());
        sysMessage.setEsSendNum(0);
        sysMessage.setCreateTime(new Date());
        sysMessage.setUpdateTime(new Date());
        this.save(sysMessage);
        return sysMessage.getId();
    }

}

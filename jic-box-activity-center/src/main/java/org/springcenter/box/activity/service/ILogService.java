package org.springcenter.box.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springcenter.box.activity.domain.box.CActivityOptLog;
import org.springcenter.box.activity.dto.LogDto;
import org.springcenter.box.activity.enums.OptTypeEnum;

public interface ILogService extends IService<CActivityOptLog> {

    /**
     * 保存日志
     * @param activityId 活动ID
     * @param optType 类型
     * @param optPerson 操作人
     * @param oldContent 旧内容
     * @param newContent 新内容
     */
    Boolean saveLog(String activityId, OptTypeEnum optType, String optPerson, LogDto oldContent, LogDto newContent);

    /**
     * 保存日志
     * @param activityId 活动ID
     * @param joinRecordId 参与记录ID
     * @param optType 类型
     * @param optPerson 操作人
     * @param oldContent 旧内容
     * @param newContent 新内容
     */
    Boolean saveLog(String activityId, OptTypeEnum optType, String optPerson, LogDto oldContent, LogDto newContent, String joinRecordId);
}

package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UpdateGoodsReqDto {

    @ApiModelProperty(value = "spuId")
    @NotBlank(message = "spuId不能为空")
    private String spuId;


    @ApiModelProperty(value = "spuImgUrl")
    private String spuImgUrl;

    @ApiModelProperty(value = "skuList")
    private List<UpdateSku> skuList;

    @ApiModelProperty(value = "修改人")
    @NotBlank(message = "修改人不能为空")
    private String updateBy;

    @ApiModelProperty(value = "上下架(0下架 1上架)")
    private Long status;


    @Data
    public static class UpdateSku {
        @ApiModelProperty(value = "skuId")
        @NotBlank(message = "skuId不能为空")
        private String skuId;


        @ApiModelProperty(value = "图片")
        private String imgUrl;


        @ApiModelProperty(value = "库存")
        private Long stock;


        @ApiModelProperty(value = "点数")
        private BigDecimal point;
    }


}

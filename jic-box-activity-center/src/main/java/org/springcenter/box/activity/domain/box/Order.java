package org.springcenter.box.activity.domain.box;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-10-07 11:25:06
 * @Description:
 */
@Data
@TableName("ORDER_")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value = "Order对象", description = "")
public class Order implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "box服务单号")
    @TableField("BOX_SN")
    private String boxSn;
    @ApiModelProperty(value = "订单编号")
    @TableField("ORDER_SN")
    private String orderSn;
    @ApiModelProperty(value = "业务订单号（JNBY生成的订单号）")
    @TableField("SERVICE_SN")
    private String serviceSn;
    @ApiModelProperty(value = "订单编号中流水号")
    @TableField("SERIAL_NUMBER")
    private String serialNumber;
    @ApiModelProperty(value = "订单标号中月份")
    @TableField("DAYSTR")
    private String daystr;
    @ApiModelProperty(value = "顾客id")
    @TableField("CUSTOMER_ID")
    private String customerId;
    @ApiModelProperty(value = "支付流水号")
    @TableField("TRADE_NO")
    private String tradeNo;
    @ApiModelProperty(value = "微信支付订单号")
    @TableField("WECHAT_TRANSACTION_ID")
    private String wechatTransactionId;
    @ApiModelProperty(value = "订单状态:0-未支付,1-已支付2-已发货,3-已完成,4-已取消,5-已退款,6 已签收,7 部分发货")
    @TableField("ORDER_STATUS")
    private Long orderStatus;
    @ApiModelProperty(value = "支付金额")
    @TableField("PAID_AMOUNT")
    private BigDecimal paidAmount;
    @ApiModelProperty(value = "商品总金额")
    @TableField("PRODUCT_TOTAL_PRICE")
    private BigDecimal productTotalPrice;
    @ApiModelProperty(value = "优惠券金额")
    @TableField("DISCOUNT_AMOUNT")
    private BigDecimal discountAmount;
    @ApiModelProperty(value = "商品优惠金额")
    @TableField("PRODUCT_DISCOUNT")
    private BigDecimal productDiscount;
    @ApiModelProperty(value = "会员优惠金额")
    @TableField("VIP_DISCOUNT")
    private BigDecimal vipDiscount;
    @ApiModelProperty(value = "商品数量")
    @TableField("PRODUCT_TOTAL_QUANTITY")
    private String productTotalQuantity;
    @ApiModelProperty(value = "支付方式")
    @TableField("PAYMENT_TYPE")
    private String paymentType;
    @ApiModelProperty(value = "收入金币")
    @TableField("COIN")
    private BigDecimal coin;
    @ApiModelProperty(value = "快递运单号")
    @TableField("TRACKING_NUMBER")
    private String trackingNumber;
    @TableField("CREATE_TIME")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @TableField("UPDATE_TIME")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    @ApiModelProperty(value = "优惠券编号")
    @TableField("VOUCHERS_NO")
    private String vouchersNo;
    @ApiModelProperty(value = "折扣券类型  1为抵扣券  <1为折扣券")
    @TableField("VOU_DIS")
    private Double vouDis;
    @ApiModelProperty(value = "是否有搭支付  0： 不是  1：是")
    @TableField("IS_YD")
    private Long isYd;
    @ApiModelProperty(value = "发货时间")
    @TableField("SEND_TIME")
    private Date sendTime;
    @ApiModelProperty(value = "使用积分")
    @TableField("INTEGRAL")
    private Long integral;
    @ApiModelProperty(value = "是否电商传入  0： 不是  1：是")
    @TableField("IS_DS")
    private Long isDs;
    @ApiModelProperty(value = "支付appid")
    @TableField("APPID")
    private String appid;
    @TableField("ISRETAIL")
    private String isretail;
    @ApiModelProperty(value = "订单类型 10:搭配师订单 20:导购订单 30:有搭订单 40:主题盒子订单")
    @TableField("TYPE")
    private Long type;
    @TableField("FLAG")
    private BigDecimal flag;
    @ApiModelProperty(value = "是否有内淘")
    @TableField("IS_EB")
    private String isEb;
    @ApiModelProperty(value = "是否包含未完成的退款单 1包含，0不包含")
    @TableField("IF_HAVE_UN_REFUND_ORDER")
    private BigDecimal ifHaveUnRefundOrder;
    @ApiModelProperty(value = "换码内淘物流是否同步完毕 1完成 0未完成")
    @TableField("EB_EXPRESS_SYNC_FINISH")
    private BigDecimal ebExpressSyncFinish;
    @ApiModelProperty(value = "支付渠道（3微信 2支付宝）默认3")
    @TableField("PAY_CHANNEL")
    private String payChannel;
    @ApiModelProperty(value = "收钱吧sn")
    @TableField("SQB_SN")
    private String sqbSn;
    @ApiModelProperty(value = "微盟order单号")
    @TableField("WEIMOB_ORDER_SN")
    private String weimobOrderSn;
    @ApiModelProperty(value = "appid")
    @TableField("APP_ID")
    private String appId;
    @ApiModelProperty(value = "价格计算id")
    @TableField("CALC_ID")
    private String calcId;
    @ApiModelProperty(value = "储值卡金额")
    @TableField("BALANCE_AMT")
    private BigDecimal balanceAmt;
    @ApiModelProperty(value = "商场代金券")
    @TableField("SHOP_VOU_AMT")
    private BigDecimal shopVouAmt;
    @ApiModelProperty(value = "商户id")
    @TableField("MERCHANT_CODE")
    private String merchantCode;
    @ApiModelProperty(value = "花呗分期数")
    @TableField("HB_FQ_NUM")
    private String hbFqNum;
    @ApiModelProperty(value = "是否使用微信代金券   1 使用了    null或者0 均为未使用")
    @TableField("IS_USE_WECHAT_COUPON")
    private Integer isUseWechatCoupon;
    @ApiModelProperty("满赠活动id")
    @TableField("FULL_ACTIVITY_ID")
    private String fullActivityId;
}

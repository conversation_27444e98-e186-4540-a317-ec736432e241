package org.springcenter.box.activity.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.jnby.common.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.weaver.ast.Or;
import org.joda.time.DateTime;
import org.springcenter.box.activity.api.dto.*;
import org.springcenter.box.activity.config.IdConfig;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.config.redis.RedissonUtil;
import org.springcenter.box.activity.domain.box.*;
import org.springcenter.box.activity.dto.OrderDto;
import org.springcenter.box.activity.enums.BoxRefundStatusEnum;
import org.springcenter.box.activity.enums.OrderStatusEnum;
import org.springcenter.box.activity.event.ModifySpuStockEvent;
import org.springcenter.box.activity.event.RulesUserPointEvent;
import org.springcenter.box.activity.event.bus.RulesUserPointEventBus;
import org.springcenter.box.activity.mapper.box.*;
import org.springcenter.box.activity.service.IBPointDetailService;
import org.springcenter.box.activity.service.ICombinePointService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CombinePointServiceImpl implements ICombinePointService {
    @Resource
    private BSubscribeInfoMapper bSubscribeInfoMapper;
    @Resource
    private BUserPointAccountMapper bUserPointAccountMapper;

    @Resource
    private BPointDetailMapper bPointDetailMapper;

    @Resource
    private IBPointDetailService ibPointDetailService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private IdConfig idConfig;

    @Resource(name = "boxTransactionTemplate")
    private TransactionTemplate template;

    @Resource
    private RedissonUtil redissonUtil;

    @Resource
    private BoxRefundMapper boxRefundMapper;

    @Resource
    private BoxRefundDetailsMapper boxRefundDetailsMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;


    @Resource
    private RulesUserPointEventBus rulesUserPointEventBus;



    @Override
    public List<BPointDetail> getPointDetailListByCustomerId(String customerId) {
        return bPointDetailMapper.getListByCustomerId(customerId);
    }


    @Override
    public List<BPointDetail> getPointDetailListByUnionIdAndPage(String unionId, Page page) {
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        LambdaQueryWrapper<BPointDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BPointDetail::getUnionId, unionId);
        queryWrapper.eq(BPointDetail::getDelFlag, 0);
        queryWrapper.orderByDesc(BPointDetail::getCreateTime);
        bPointDetailMapper.selectList(queryWrapper);
        PageInfo<BPointDetail> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<BPointDetail> list = pageInfo.getList();
        return list;
    }

    @Override
    public List<BSubscribeInfo> selectPointSubInfoByUnionIdAndTime(String unionId, Page page) {
        com.github.pagehelper.Page<Object> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        bSubscribeInfoMapper.selectPointSubInfoByUnionIdAndTime(unionId);
        PageInfo<BSubscribeInfo> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<BSubscribeInfo> list = pageInfo.getList();
        return list;
    }

    @Override
   public List<BSubscribeInfo> getSubListByIds(List<String> ids){
       return bSubscribeInfoMapper.selectInfosByIds(ids);
    }

    @Override
    public void addPoint(AddPointReqDto addPointReqDto) {
        String customerId = addPointReqDto.getCustomerId();
        String unionId = addPointReqDto.getUnionId();
        String orderId = addPointReqDto.getOrderId();
        String orderSn = addPointReqDto.getOrderSn();
        String subId = addPointReqDto.getSubId();

        log.info("增加点数addPoint入参unionId:{} addPointReqDto:{}", unionId, JSONObject.toJSONString(addPointReqDto));

        // 加锁 防止并发
        String key = "addPoint:" + addPointReqDto.getOrderId();
        if (!redissonUtil.tryLock(key)) {
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            List<BPointDetail> bPointDetailList = bPointDetailMapper.getListByOutId(orderId);
            if (CollectionUtils.isNotEmpty(bPointDetailList)) {
                log.info("增加点数已存在orderSn:{}", orderSn);
                return;
            }

            BSubscribeInfo bSubscribeInfo = Preconditions.checkNotNull(bSubscribeInfoMapper.selectById(subId), "订阅信息不存在");
            if (!Long.valueOf(1).equals(bSubscribeInfo.getCardType())) {
                log.info("非正式卡subId:{} 不进行处理", subId);
                return;
            }
            Order order = Preconditions.checkNotNull(orderMapper.selectById(orderId), "订单信息不能为空");

            Date activeTime = order.getCreateTime();
            Date expireTime = new DateTime(bSubscribeInfo.getEndTime()).plusDays(30).toDate();
            // 订阅结束 特殊处理
            if (Long.valueOf(3).equals(bSubscribeInfo.getStatus())) {
                expireTime = new DateTime(bSubscribeInfo.getUnsubTime()).plusDays(30).toDate();
            }

            BigDecimal addPoint = getAddPoint(order);
            if (BigDecimal.ZERO.compareTo(addPoint) == 0) {
                log.info("增加点数点数为空orderSn:{}", orderSn);
                return;
            }

            BPointDetail addPointDetail = new BPointDetail();
            addPointDetail.setId(idConfig.getPointDetailId());
            addPointDetail.setCustomerId(customerId);
            addPointDetail.setUnionId(unionId);
            addPointDetail.setOutId(orderId);
            addPointDetail.setOutSn(orderSn);
            addPointDetail.setSubId(subId);
            addPointDetail.setPoint(addPoint);
            addPointDetail.setCreateTime(new Date());
            addPointDetail.setUpdateTime(new Date());
            addPointDetail.setActiveTime(activeTime);
            addPointDetail.setExpireTime(expireTime);
            template.execute(action -> {
                ibPointDetailService.save(addPointDetail);
                rulesUserPoint(customerId);
                return true;
            });
        } finally {
            redissonUtil.unlock(key);
        }

    }


    @Override
    public void addPointByAdmin(AddPointByAdminReqDto addPointByAdminReqDto) {
        String customerId = addPointByAdminReqDto.getCustomerId();
        String unionId = addPointByAdminReqDto.getUnionId();
        String orderId = addPointByAdminReqDto.getOrderId();
        String orderSn = addPointByAdminReqDto.getOrderSn();
        String subId = addPointByAdminReqDto.getSubId();
        String createBy = addPointByAdminReqDto.getCreateBy();
        BigDecimal addPoint = addPointByAdminReqDto.getPoint();
        String memo = addPointByAdminReqDto.getMemo();

        log.info("后台增加点数addPoint入参unionId:{} addPointByAdminReqDto:{}", unionId, JSONObject.toJSONString(addPointByAdminReqDto));
        if (BigDecimal.ZERO.compareTo(addPoint) > 0) {
            log.info("增加点数点数非法orderSn:{}", orderSn);
            throw new BoxActivityException("增加点数点数非法");
        }

        // 加锁 防止并发
        String key = "addPointByAdmin:" + addPointByAdminReqDto.getOrderId();
        if (!redissonUtil.tryLock(key)) {
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            List<BPointDetail> bPointDetailList = bPointDetailMapper.getListByOutId(orderId);
            if (CollectionUtils.isNotEmpty(bPointDetailList)) {
                log.info("增加点数已存在orderSn:{}", orderSn);
                return;
            }

            BSubscribeInfo bSubscribeInfo = Preconditions.checkNotNull(bSubscribeInfoMapper.selectById(subId), "订阅信息不存在");
            if (!Long.valueOf(1).equals(bSubscribeInfo.getCardType())) {
                log.info("非正式卡subId:{} 不进行处理", subId);
                return;
            }

            Date activeTime = new Date();
            Date expireTime = new DateTime(bSubscribeInfo.getEndTime()).plusDays(30).toDate();
            // 订阅结束 特殊处理
            if (Long.valueOf(3).equals(bSubscribeInfo.getStatus())) {
                expireTime = new DateTime(bSubscribeInfo.getUnsubTime()).plusDays(30).toDate();
            }


            if (BigDecimal.ZERO.compareTo(addPoint) == 0) {
                log.info("增加点数点数为空orderSn:{}", orderSn);
                return;
            }

            BPointDetail addPointDetail = new BPointDetail();
            addPointDetail.setId(idConfig.getPointDetailId());
            addPointDetail.setMemo(memo);
            addPointDetail.setCustomerId(customerId);
            addPointDetail.setUnionId(unionId);
            addPointDetail.setOutId(orderId);
            addPointDetail.setOutSn(orderSn);
            addPointDetail.setSubId(subId);
            addPointDetail.setPoint(addPoint);
            addPointDetail.setCreateTime(new Date());
            addPointDetail.setUpdateTime(new Date());
            addPointDetail.setActiveTime(activeTime);
            addPointDetail.setExpireTime(expireTime);
            addPointDetail.setCreateBy(createBy);
            template.execute(action -> {
                ibPointDetailService.save(addPointDetail);
                rulesUserPoint(customerId);
                return true;
            });
        } finally {
            redissonUtil.unlock(key);
        }
    }

    @Override
    public BPointDetail addPointByOm(AddPointByOmReqDto addPointByOmReqDto) {
        String customerId = addPointByOmReqDto.getCustomerId();
        String unionId = addPointByOmReqDto.getUnionId();
        String orderId = idConfig.getPointDetailId();
        String orderSn = "OM" + orderId;
        String createBy = addPointByOmReqDto.getCreateBy();
        BigDecimal addPoint = addPointByOmReqDto.getPoint();
        String memo = addPointByOmReqDto.getMemo();

        log.info("后台增加点数addPoint入参unionId:{} addPointByOmReqDto:{}", unionId, JSONObject.toJSONString(addPointByOmReqDto));
        if (BigDecimal.ZERO.compareTo(addPoint) > 0) {
            log.info("增加点数点数非法orderSn:{}", orderSn);
            throw new BoxActivityException("增加点数点数非法");
        }

        // 加锁 防止并发
        String key = "addPointByOm:" + addPointByOmReqDto.getCustomerId();
        if (!redissonUtil.tryLock(key)) {
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            // 查询有效的订阅信息
            List<BSubscribeInfo> bSubscribeInfos = bSubscribeInfoMapper.selectUserEffectSubInfo(Arrays.asList(customerId));
            if (CollectionUtils.isEmpty(bSubscribeInfos)) {
                throw new RuntimeException("无有效订阅");
            }

            BSubscribeInfo bSubscribeInfo = bSubscribeInfos.get(0);
            String subId = bSubscribeInfo.getId();

            Date activeTime = new Date();
            Date expireTime = new DateTime(bSubscribeInfo.getEndTime()).plusDays(30).toDate();
            // 订阅结束 特殊处理
            if (Long.valueOf(3).equals(bSubscribeInfo.getStatus())) {
                expireTime = new DateTime(bSubscribeInfo.getUnsubTime()).plusDays(30).toDate();
            }


            if (BigDecimal.ZERO.compareTo(addPoint) == 0) {
                log.info("增加点数点数为空orderSn:{}", orderSn);
                throw new RuntimeException("增加点数点数为空");
            }

            BPointDetail addPointDetail = new BPointDetail();
            addPointDetail.setId(idConfig.getPointDetailId());
            addPointDetail.setMemo(memo);
            addPointDetail.setCustomerId(customerId);
            addPointDetail.setUnionId(unionId);
            addPointDetail.setOutId(orderId);
            addPointDetail.setOutSn(orderSn);
            addPointDetail.setSubId(subId);
            addPointDetail.setPoint(addPoint);
            addPointDetail.setCreateTime(new Date());
            addPointDetail.setUpdateTime(new Date());
            addPointDetail.setActiveTime(activeTime);
            addPointDetail.setExpireTime(expireTime);
            addPointDetail.setCreateBy(createBy);
            template.execute(action -> {
                ibPointDetailService.save(addPointDetail);
                rulesUserPoint(customerId);
                return true;
            });
            return addPointDetail;
        } finally {
            redissonUtil.unlock(key);
        }
    }
    BigDecimal getAddPoint(Order order) {
        BigDecimal balanceAmt = Optional.ofNullable(order.getBalanceAmt()).orElse(BigDecimal.ZERO);
        BigDecimal paidAmount =Optional.ofNullable(order.getPaidAmount()).orElse(BigDecimal.ZERO);

        if ((BigDecimal.ZERO.compareTo(paidAmount) < 0 || BigDecimal.ZERO.compareTo(balanceAmt) < 0)) {
            return (paidAmount.add(balanceAmt));
        }
        return BigDecimal.ZERO;
    }

    @Override
    public void subtractPoint(SubtractPointReqDto subtractPointReqDto) {
        String customerId = subtractPointReqDto.getCustomerId();
        String unionId = subtractPointReqDto.getUnionId();
        String refundId = subtractPointReqDto.getRefundId();
        String refundSn = subtractPointReqDto.getRefundSn();
        String subId = subtractPointReqDto.getSubId();

        log.info("减去点数subtractPoint入参unionId:{} subtractPointReqDto:{}", unionId, JSONObject.toJSONString(subtractPointReqDto));
        // 加锁 防止并发
        String key = "subtractPoint:" + subtractPointReqDto.getRefundId();
        if (!redissonUtil.tryLock(key)) {
            throw new BoxActivityException("请勿重复提交");
        }
        try {
            List<BPointDetail> bPointDetailList = bPointDetailMapper.getListByOutId(refundId);
            if (CollectionUtils.isNotEmpty(bPointDetailList)) {
                log.info("减去点数已存在refundSn:{}", refundSn);
                return;
            }
            BSubscribeInfo bSubscribeInfo = Preconditions.checkNotNull(bSubscribeInfoMapper.selectById(subId), "订阅信息不存在");
            BoxRefund boxRefund = Preconditions.checkNotNull(boxRefundMapper.selectById(refundId),"退款信息不存在");

            if (!Long.valueOf(1).equals(bSubscribeInfo.getCardType())) {
                log.info("非正式卡subId:{} 不进行处理", subId);
                return;
            }

            Date activeTime = new DateTime(boxRefund.getCreateTime()).toDate();
            Date expireTime = new DateTime(bSubscribeInfo.getEndTime()).plusDays(30).toDate();
            // 订阅结束 特殊处理
            if (Long.valueOf(3).equals(bSubscribeInfo.getStatus())) {
                expireTime = new DateTime(bSubscribeInfo.getUnsubTime()).plusDays(30).toDate();
            }
            // 负数
            BigDecimal subtractPoint = getSubtractPoint(boxRefund);;
            if (BigDecimal.ZERO.compareTo(subtractPoint) == 0) {
                log.info("减去点数-点数为空refundSn:{}", refundSn);
                return;
            }

            BPointDetail subtractPointDetail = new BPointDetail();
            subtractPointDetail.setId(idConfig.getLogId());
            subtractPointDetail.setCustomerId(customerId);
            subtractPointDetail.setUnionId(unionId);
            subtractPointDetail.setOutId(refundId);
            subtractPointDetail.setOutSn(refundSn);
            subtractPointDetail.setSubId(subId);
            subtractPointDetail.setPoint(subtractPoint.negate());
            subtractPointDetail.setType(1L);
            subtractPointDetail.setCreateTime(new Date());
            subtractPointDetail.setUpdateTime(new Date());
            subtractPointDetail.setActiveTime(activeTime);
            subtractPointDetail.setExpireTime(expireTime);
            template.execute(action -> {
                ibPointDetailService.save(subtractPointDetail);
                rulesUserPoint(customerId);
                return true;
            });
        } finally {
            redissonUtil.unlock(key);
        }
    }

    BigDecimal getSubtractPoint(BoxRefund boxRefund) {
        if (boxRefund.getRefundAmount().compareTo(BigDecimal.ZERO) > 0  || BigDecimal.ZERO.compareTo(boxRefund.getRefundBalance()) < 0) {
            BigDecimal refundBalance = Optional.ofNullable(boxRefund.getRefundBalance()).orElse(BigDecimal.ZERO);
            BigDecimal refundAmount = Optional.ofNullable(boxRefund.getRefundAmount()).orElse(BigDecimal.ZERO);

            // 查询订单商场代金券金额
            LambdaQueryWrapper<BoxRefundDetails> refundDetailsQuery = new LambdaQueryWrapper<>();
            refundDetailsQuery.eq(BoxRefundDetails::getBoxRefundId, boxRefund.getId());
            List<BoxRefundDetails> refundDetails = boxRefundDetailsMapper.selectList(refundDetailsQuery);

            List<String> orderDetailIds = refundDetails.stream().map(BoxRefundDetails::getOrderDetailId).collect(Collectors.toList());
            LambdaQueryWrapper<OrderDetail> orderDetailQuery = new LambdaQueryWrapper<>();
            orderDetailQuery.in(OrderDetail::getId, orderDetailIds);
            List<OrderDetail> orderDetailList = orderDetailMapper.selectList(orderDetailQuery);


            BigDecimal shopVouAmt = orderDetailList.stream().map(e -> Optional.ofNullable(e.getShopVouAmt()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totAmt = refundAmount.add(refundBalance).add(shopVouAmt);
            return totAmt;
        }
        return BigDecimal.ZERO;
    }


    void rulesUserPoint(String customerId) {
        log.info("规则用户点数customerId:{}", customerId);
        try {
            RulesUserPointEvent event = new RulesUserPointEvent(customerId,
                    RandomUtils.nextLong(),
                    RandomUtils.nextLong(),
                    UUID.randomUUID());
            log.info("规则用户点数userToken:{}", event.getUserToken());
            rulesUserPointEventBus.post(event);
        } catch (Exception e) {
            log.info("规则用户点数userToken:{}", customerId);
        }
    }

}



package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lwz
 * @Date: 2023-09-07 16:55:46
 * @Description:
 */
@TableName("B_SUBSCRIBE_INFO")
@Accessors(chain = true)
@ApiModel(value = "BSubscribeInfo对象", description = "")
@Data
public class BSubscribeInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;

    @ApiModelProperty(value = "应用id")
    @TableField("APP_ID")
    private String appId;

    @ApiModelProperty(value = "应用名字")
    @TableField("APP_NAME")
    private String appName;

    @TableField("CUST_ID")
    private String custId;


    @TableField("UNIONID")
    private String unionid;

    @TableField("SUB_ORDER_ID")
    @ApiModelProperty(value = "订阅订单id")
    private String subOrderId;


    @ApiModelProperty(value = "开始时间")
    @TableField("START_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @TableField("END_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


    /**
     * 1订阅中 2已结束 3已退订
     */
    @ApiModelProperty(value = "1订阅中 2已结束 3已退订")
    @TableField("STATUS")
    private Long status;


    /**
     * 支付方式(1微信支付 2积分支付 3组合支付 4免费)
     */
    @ApiModelProperty(value = "支付方式")
    @TableField("PAY_WAY")
    private Long payWay;


    @ApiModelProperty(value = "退订时间")
    @TableField("UNSUB_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date unsubTime;


    /**
     * 卡类型（1正式卡 2单次卡 3免费卡）
     */
    @ApiModelProperty(value = "卡类型（1正式卡 2单次卡 3免费卡）")
    @TableField("CARD_TYPE")
    private Long cardType;


    @ApiModelProperty(value = "用户卡等级（0银卡 1金卡 2铂金卡 3白金卡 4黑卡）")
    @TableField("USER_CARD_LEVEL")
    private Integer userCardLevel;


    @ApiModelProperty(value = "订阅周期内统计金额")
    @TableField("TOTAL_PRICE")
    private Double totalPrice;

    @ApiModelProperty(value = "卡id")
    @TableField("CARD_ID")
    private String cardId;

    @ApiModelProperty(value = "首次计划")
    @TableField("FIRST_RECOVER")
    private Integer firstRecover;

    @TableField("CHANNEL_ID")
    private String channelId;

    @ApiModelProperty(value = "外部单号")
    @TableField("OUT_NO")
    private String outNo;

    @ApiModelProperty(value = "绑定搭配师")
    @TableField("BIND_FASHIONER_ID")
    private String bindFashionerId;


    @ApiModelProperty(value = "推荐人")
    @TableField("RECOMMENDER")
    private String recommender;


    @ApiModelProperty(value = "是否续订（0否 1是）")
    @TableField("RENEW")
    private Long renew;

    @ApiModelProperty(value = "订阅方式（0正常订阅 ，1邀请订阅）")
    @TableField("TYPE")
    private Long type;

    @ApiModelProperty(value = "邀请人")
    @TableField("INVITE_PEOPLE")
    private String invitePeople;

    /**
     * 开卡类型（0不开卡 1开卡 2有卡补全信息）
     */
    @ApiModelProperty(value = "开卡类型（0不开卡 1开卡 2有卡补全信息）")
    @TableField("CREATE_CARD_TYPE")
    private Long createCardType;


    @ApiModelProperty(value = "开卡门店(cStoreId)")
    @TableField("CREATE_CARD_STORE_ID")
    private String createCardStoreId;


    @ApiModelProperty(value = "开卡品牌")
    @TableField("CREATE_CARD_BRAND_ID")
    private String createCardBrandId;


    @ApiModelProperty(value = "开卡人(hrId)")
    @TableField("CREATE_CARD_HR_ID")
    private String createCardHrId;


    @TableField("CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "删除（0否 1是）")
    @TableField("DEL_FLAG")
    private Long delFlag;


    @ApiModelProperty(value = "退订备注")
    @TableField("UNSUB_MEMO")
    private String unsubMemo;


}

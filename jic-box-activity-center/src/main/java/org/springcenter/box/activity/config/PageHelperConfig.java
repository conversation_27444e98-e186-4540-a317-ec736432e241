package org.springcenter.box.activity.config;

import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.github.pagehelper.PageHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <AUTHOR>
 * @Date:2023/4/18 11:05
 */
@Configuration
public class PageHelperConfig {

    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

    @Bean
    PageHelper pageInterceptor(){
        PageHelper pageInterceptor = new PageHelper();
        Properties properties = new Properties();
        properties.setProperty("helperDialect", "orcale");
        pageInterceptor.setProperties(properties);  // 由此可进入源码，
        return pageInterceptor;
    }
}

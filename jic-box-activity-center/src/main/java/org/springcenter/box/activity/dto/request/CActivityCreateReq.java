package org.springcenter.box.activity.dto.request;

import com.alibaba.excel.util.StringUtils;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.enums.ItemRangeEnum;
import org.springcenter.box.activity.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@ApiModel("满赠活动创建实体")
@Data
public class CActivityCreateReq {
    @ApiModelProperty(value = "活动名称", required = true)
    @NotBlank(message = "活动名称不能为空")
    private String activityName;
    @ApiModelProperty(value = "活动开始时间,格式：yyyy-MM-dd HH:mm:ss", required = true)
    @NotBlank(message = "活动开始时间不能为空")
    private String startTime;
    @ApiModelProperty(value = "活动结束时间,格式：yyyy-MM-dd HH:mm:ss", required = true)
    @NotBlank(message = "活动结束时间不能为空")
    private String endTime;
    @ApiModelProperty(value = "结算时间,格式：yyyy-MM-dd HH:mm:ss", required = true)
    @NotBlank(message = "结算时间不能为空")
    private String evaluateTime;

    @ApiModelProperty(value = "多选参与人群：1=新客+非自提盒子，2=新客+自提盒子，3=老客+非自提盒子，4=老客+自提盒子", required = true)
    @NotEmpty(message = "参与人群不能为空")
    private List<Integer> crowd;

    @ApiModelProperty(value = "搭盒人员-导购")
    private Boolean boxBuilderGuide;
    @ApiModelProperty(value = "搭盒人员-导购-门店包ID")
    private String boxBuilderGuideStore;
    @ApiModelProperty(value = "搭盒人员-搭配师")
    private Boolean boxBuilderFashioner;

    @ApiModelProperty(value = "盒子类型-订阅盒子")
    private Boolean boxTypeSubscription;
    @ApiModelProperty(value = "盒子类型-单次盒子")
    private Boolean boxTypeSingle;

    /**
     * @see org.springcenter.box.activity.enums.ItemRangeEnum
     */
    @ApiModelProperty(value = "商品范围：1=满赠门槛（不限商品），2=指定商品过滤且满足门槛，3=满足门槛且含指定商品", required = true)
    @NotNull(message = "商品范围不能为空")
    private Integer boxBrandType;
    @ApiModelProperty(value = "指定商品包ID")
    private String boxBrandList;
    @ApiModelProperty(value = "门槛单位：1=元，2=件", required = true)
    @NotNull(message = "门槛单位不能为空")
    private Integer unit;
    @ApiModelProperty(value = "达成门槛额", required = true)
    @NotNull(message = "达成门槛不能为空")
    private BigDecimal threshold;

    @ApiModelProperty(value = "赠品", required = true)
    @NotNull(message = "赠品不能为空")
    private CActivityGiftCreateReq gift;

    @ApiModelProperty(value = "创建人", required = true)
    @NotBlank(message = "创建人不能为空")
    private String createPerson;
    public void check() {
        if (this.boxBuilderGuide == null && this.boxBuilderFashioner == null) {
            throw new BoxActivityException("搭盒人员至少选择一个");
        }
        if (Boolean.TRUE.equals(this.boxBuilderGuide) && StringUtils.isBlank(this.boxBuilderGuideStore)) {
            throw new BoxActivityException("搭盒人员选择导购时请选择对应门店");
        }
        if (!ItemRangeEnum.isAll(this.boxBrandType) && StringUtils.isBlank(this.boxBrandList)) {
            throw new BoxActivityException("商品范围选择指定时请指定商品包");
        }

        if (endTime == null || evaluateTime == null) {
            throw new BoxActivityException("活动结束时间和结算时间不能为空");
        }
        if (DateUtil.getStrToTime(evaluateTime).before(DateUtil.getStrToTime(endTime))) {
            throw new BoxActivityException("结算时间不能早于结束时间");
        }
    }
}

package org.springcenter.box.activity.job;

import com.jnby.common.Page;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springcenter.box.activity.config.trace.XxlJobTaskLog;
import org.springcenter.box.activity.dto.JobParamDto;
import org.springcenter.box.activity.service.ICActivityService;
import org.springcenter.box.activity.service.IOrderService;
import org.springcenter.box.activity.util.DateUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 全量兜底计算任务
 * 单机执行，半个小时一次，每次执行查询近一个小时的ORDER订单执行
 * SELECT * FROM ORDER_ WHERE order_status IN (1,2,3,6) AND update_time BETWEEN to_date('2024-10-14 00:00:00', 'YYYY-MM-DD HH24:MI:SS') AND to_date('2024-10-16 00:00:00', 'YYYY-MM-DD HH24:MI:SS');
 * SELECT * FROM BOX_REFUND WHERE status=3 AND update_time BETWEEN to_date('2024-10-14 00:00:00', 'YYYY-MM-DD HH24:MI:SS') AND to_date('2024-10-16 00:00:00', 'YYYY-MM-DD HH24:MI:SS');
 */
@Component
@Slf4j
public class UpdateTaskJob extends IJobHandler {

    @Resource
    private IOrderService orderService;

    //    @Scheduled(cron = "0 */30 * * * ?")
    @XxlJob("UpdateTaskJob")
    @Override
    public void execute() {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobTaskLog.traceLog("全量兜底计算任务JOB,本次调度开始");
        Date endTime = new Date();
        Date startTime = DateUtil.addMinutes(endTime, -10);
        if (StringUtils.isNotBlank(jobParam)) {
            log.info("设置了参数:{}", jobParam);
            JobParamDto build = JobParamDto.build(jobParam);
            startTime = build.getFromDate();
            endTime = build.getToDate();
        }
        try {
            log.info("增量支付单 开始处理时间:[{} - {}]", DateUtil.getStrDateTime(startTime), DateUtil.getStrDateTime(endTime));
            orderService.scanIncOrder(startTime, endTime);
            log.info("增量支付单 处理完成");
        } catch (Exception e) {
            log.error("增量支付单 处理异常", e);
        }
        try {
            log.info("增量退款单 开始处理时间");
            orderService.scanIncRefund(startTime, endTime);
            log.info("增量退款单 处理完成");
        } catch (Exception e) {
            log.error("增量退款单 处理异常", e);
        }
        log.info("全量兜底计算任务JOB,本次调度结束");
    }
}


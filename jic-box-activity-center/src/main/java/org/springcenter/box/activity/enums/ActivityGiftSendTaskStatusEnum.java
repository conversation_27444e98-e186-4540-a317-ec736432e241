package org.springcenter.box.activity.enums;

import lombok.Getter;

/**
 * 活动发奖状态
 */
@Getter
public enum ActivityGiftSendTaskStatusEnum {
    WAIT_CALC(0, "待核算"),
    WAIT_SEND(1, "待发奖"),
    SEND_ING(2, "发奖中"),
    ;

    private final Integer code;
    private final String msg;

    ActivityGiftSendTaskStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 待计算状态
     * @param code
     * @return
     */
    public static boolean isWaitCalc(Integer code) {
        return WAIT_CALC.getCode().equals(code);
    }
}

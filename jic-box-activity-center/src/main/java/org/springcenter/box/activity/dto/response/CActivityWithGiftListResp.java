package org.springcenter.box.activity.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("活动详情回参")
@Data
public class CActivityWithGiftListResp {
    @ApiModelProperty(value = "活动ID")
    private String id;
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    @ApiModelProperty(value = "活动开始时间")
    private String startTime;
    @ApiModelProperty(value = "活动结束时间")
    private String endTime;
    @ApiModelProperty(value = "活动状态：1=未开始,2=进行中,3=核算中,4=待发放,5=已结束")
    private Integer status;
    @ApiModelProperty(value = "达成门槛：元")
    private BigDecimal threshold;
    @ApiModelProperty(value = "赠品")
    private CActivityGift gift;

    @Data
    public static class CActivityGift {
        @ApiModelProperty(value = "赠品名称")
        private String giftName;
        @ApiModelProperty(value = "赠品类型：1=外部实物，2=内部实物，3=兑换卡")
        private Integer giftType;
        @ApiModelProperty(value = "活动ID")
        private String activityId;
        @ApiModelProperty(value = "凭证ID：sku、券号等")
        private String voucherId;
        @ApiModelProperty(value = "价值")
        private BigDecimal price;
        @ApiModelProperty(value = "总数")
        private Long totalNum;
        @ApiModelProperty(value = "库存")
        private Long remainNum;
        @ApiModelProperty(value = "图片地址：多个英文逗号分割")
        private String picList;
    }
}

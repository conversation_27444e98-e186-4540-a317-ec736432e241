package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.springcenter.box.activity.api.dto.JoinRecordInfoResp;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.domain.box.CActivityGift;
import org.springcenter.box.activity.domain.box.CActivityJoinRecord;
import org.springcenter.box.activity.domain.box.CustomerLogistics;
import org.springcenter.box.activity.enums.GiftSendStatusEnum;
import org.springcenter.box.activity.mapper.box.CActivityJoinRecordMapper;
import org.springcenter.box.activity.mapper.box.CustomerLogisticsMapper;
import org.springcenter.box.activity.service.IExpressHttpService;
import org.springcenter.box.activity.util.DateUtil;
import com.jnby.common.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springcenter.retail.api.IRetailApi;
import org.springcenter.retail.api.dto.req.OrderPush2JSTDTO;
import org.springcenter.retail.api.dto.req.OrderQuery2JSTDTO;
import org.springcenter.retail.api.dto.resp.PushOrder2JSTRespDTO;
import org.springcenter.retail.api.dto.resp.QueryOrder2JSTRespDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class ExpressHttpServiceImpl implements IExpressHttpService {

    @Value("${jushuitan.shopid}")
    private String jstShopId;

    @Resource
    private IRetailApi retailApi;

    @Resource
    private CustomerLogisticsMapper customerLogisticsMapper;

    @Resource
    private CActivityJoinRecordMapper joinRecordMapper;

    @Override
    public void sendExpress(CActivityGift gift, JoinRecordInfoResp joinRecordInfoResp) {
        // 参与记录
        CActivityJoinRecord updateRecord = new CActivityJoinRecord();
        updateRecord.setId(joinRecordInfoResp.getId());
        try {
            if (gift == null || joinRecordInfoResp == null) {
                throw new BoxActivityException("参数异常");
            }
            // 批量获取地址信息
            CustomerLogistics customerLogistics = getLogisticsInfo(joinRecordInfoResp.getAddressId());
            if (customerLogistics == null) {
                throw new BoxActivityException("地址异常，无法提交发货");
            }
            updateRecord.setAddressProvince(customerLogistics.getProvince());
            updateRecord.setAddressCity(customerLogistics.getCity());
            updateRecord.setAddressDistrict(customerLogistics.getDistrict());
            updateRecord.setAddress(customerLogistics.getAddress());
            updateRecord.setAddressName(customerLogistics.getContactName());
            updateRecord.setAddressPhone(customerLogistics.getContactPhone());

            // 聚水潭发货单
            List<OrderPush2JSTDTO> orderPush2JSTDTOList = new ArrayList<>();
            // 发货单组装
            String soId = joinRecordInfoResp.getBoxNo() + gift.getVoucherId();
            updateRecord.setSoId(soId);

            OrderPush2JSTDTO orderPush2JSTDTO = new OrderPush2JSTDTO();
            orderPush2JSTDTOList.add(orderPush2JSTDTO);
            orderPush2JSTDTO.setOrderDate(DateUtil.formatToStr(new Date(), DateUtil.DATE_FORMAT_YMDHM));
            orderPush2JSTDTO.setOuterSoId(soId);
            orderPush2JSTDTO.setSoId(soId);
            orderPush2JSTDTO.setPayAmount(BigDecimal.ZERO);
            orderPush2JSTDTO.setReceiverState(customerLogistics.getProvince());
            orderPush2JSTDTO.setReceiverCity(customerLogistics.getCity());
            orderPush2JSTDTO.setReceiverDistrict(customerLogistics.getDistrict());
            orderPush2JSTDTO.setReceiverAddress(customerLogistics.getAddress());
            orderPush2JSTDTO.setReceiverName(customerLogistics.getContactName());
            orderPush2JSTDTO.setReceiverPhone(customerLogistics.getContactPhone());
            orderPush2JSTDTO.setRemark(joinRecordInfoResp.getBoxNo());
            orderPush2JSTDTO.setShopId(Integer.parseInt(jstShopId));
            List<OrderPush2JSTDTO.OrderPush2JSTDTOItem> items = new ArrayList<>();
            OrderPush2JSTDTO.OrderPush2JSTDTOItem item = new OrderPush2JSTDTO.OrderPush2JSTDTOItem();
            item.setAmount(BigDecimal.ZERO);
            item.setBasePrice(BigDecimal.ZERO);
            item.setName(gift.getGiftName());
            item.setOuterOiId(gift.getId());
            item.setQty(1);
            item.setShopSkuId(gift.getVoucherId());
            item.setSkuId(gift.getVoucherId());
            items.add(item);
            orderPush2JSTDTO.setItems(items);

            int retryCount = 0;
            boolean success = false;
            int retryTimes = 3;
            while (retryCount <= retryTimes && !success) {
                log.info("通过聚水潭进行发货  入参= {}", JSONObject.toJSONString(orderPush2JSTDTOList));
                ResponseResult<PushOrder2JSTRespDTO> pushOrder2JSTRespDTOResponseResult;
                // 调用接口异常
                try {
                    pushOrder2JSTRespDTOResponseResult = retailApi.jstPushOrder(orderPush2JSTDTOList);
                    log.info("通过聚水潭进行发货  返回= {}", JSONObject.toJSONString(pushOrder2JSTRespDTOResponseResult));
                } catch (Exception e) {
                    log.error("调用远程服务 异常:{}", e.getMessage(), e);
                    if (retryCount == retryTimes) {
                        updateRecord.setGiftSendStatus(GiftSendStatusEnum.ERROR.getCode());
                        updateRecord.setGiftSendErrorMsg(e.getMessage());
                    }
                    retryCount++;
                    log.info("通过聚水潭进行发货 重试第{}次", retryCount);
                    continue;
                }
                // 接口无异常判断
                if (pushOrder2JSTRespDTOResponseResult != null && pushOrder2JSTRespDTOResponseResult.getCode() == 0) {
                    if (pushOrder2JSTRespDTOResponseResult.getData() != null && CollectionUtils.isNotEmpty(pushOrder2JSTRespDTOResponseResult.getData().getDatas())) {
                        Boolean isOk = pushOrder2JSTRespDTOResponseResult.getData().getDatas().get(0).getIssuccess();
                        if (!isOk) {
                            updateRecord.setGiftSendStatus(GiftSendStatusEnum.ERROR.getCode());
                            updateRecord.setGiftSendErrorMsg(pushOrder2JSTRespDTOResponseResult.getData().getDatas().get(0).getMsg());
                        } else {
                            updateRecord.setGiftSendStatus(GiftSendStatusEnum.WAIT_RESULT.getCode());
                        }
                        success = true;
                    }
                } else {
                    try {
                        // 报错等待十秒：大概率是「调用频次超过限制!」、「调用太频繁，请稍后再试!」两类报错。
                        Thread.sleep(10000);
                    } catch (InterruptedException e) {
                        log.error("重试等待被中断", e);
                    }
                    if (retryCount == retryTimes) {
                        updateRecord.setGiftSendStatus(GiftSendStatusEnum.ERROR.getCode());
                        updateRecord.setGiftSendErrorMsg(pushOrder2JSTRespDTOResponseResult.getMsg());
                    }
                    retryCount++;
                    log.info("通过聚水潭进行发货 重试第{}次", retryCount);
                }
            }

        } catch (Exception e) {
            log.error("聚水潭 赠品发货提交失败:{}.详情:", e.getMessage(), e);
            if (e.getMessage().length() > 100) {
                updateRecord.setGiftSendErrorMsg(e.getMessage().substring(0, 100));
            } else {
                updateRecord.setGiftSendErrorMsg(e.getMessage());
            }
        } finally {
            updateRecord.setUpdateTime(new Date());
            log.info("更新参与记录为:{}", JSONObject.toJSONString(updateRecord));
            joinRecordMapper.updateById(updateRecord);
        }
    }

    @Override
    public CustomerLogistics getLogisticsInfo(String logisticsId) {
        if (StringUtils.isBlank(logisticsId)) {
            throw new BoxActivityException("地址ID不能为空");
        }
        return customerLogisticsMapper.selectById(logisticsId);
    }

    @Override
    public Map<String, CustomerLogistics> listLogisticsInfo(List<String> logisticsIdList) {
        if (CollectionUtils.isNotEmpty(logisticsIdList)) {
            List<CustomerLogistics> customerLogistics = customerLogisticsMapper.selectBatchIds(logisticsIdList);
            return Optional.ofNullable(customerLogistics).orElse(Lists.newArrayList()).stream()
                    .collect(Collectors.toMap(CustomerLogistics::getId, Function.identity(), (o1, o2) -> o1));
        }
        return Collections.emptyMap();
    }

    @Override
    public List<QueryOrder2JSTRespDTO> queryExpress(List<JoinRecordInfoResp> joinRecordInfoRespList) {
        List<String> jstSoIds = joinRecordInfoRespList.stream().map(JoinRecordInfoResp::getSoId).collect(Collectors.toList());

        // 查询到数据
        OrderQuery2JSTDTO orderQuery2JSTDTO = new OrderQuery2JSTDTO();
        orderQuery2JSTDTO.setSoIds(jstSoIds);
        orderQuery2JSTDTO.setShopId(Integer.parseInt(jstShopId));
        log.info("通过聚水潭进行查询是否发货信息 getJstInfo req= {}",JSONObject.toJSONString(orderQuery2JSTDTO));
        try {
            ResponseResult<List<QueryOrder2JSTRespDTO>> listResponseResult = retailApi.queryOrder(orderQuery2JSTDTO);
            log.info("通过聚水潭进行查询是否发货信息 getJstInfo resp = {}",JSONObject.toJSONString(listResponseResult));
            if(listResponseResult.getCode() == 0){
                return listResponseResult.getData();
            }
        }catch (Exception e ){
            log.info("通过聚水潭进行查询是否发货信息 发生错误  req = {}",JSONObject.toJSONString(orderQuery2JSTDTO),e);
        }
        return null;
    }
}

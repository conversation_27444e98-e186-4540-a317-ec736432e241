package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
@Data
@TableName("BOX_DETAILS")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value = "Box对象", description = "")
public class BoxDetails implements Serializable {
    @TableId(value = "id" ,type = IdType.AUTO)
    private String id;

    @TableField("box_id")
    private String boxId;

    @TableField("product_no")
    private Long productNo;

    @TableField("product_id")
    private String productId;

    @TableField("ORIG_PRODUCT_ID")
    private String origProductId;

    @TableField("product_name")
    private String productName;

    @TableField("product_size")
    private String productSize;

    @TableField("product_price")
    private String productPrice;

    @TableField("product_favorable_price")
    private String productFavorablePrice;

    @TableField("vip_price")
    private String vipPrice;

    @TableField("product_quantity")
    private String productQuantity;

    @TableField("product_color_no")
    private String productColorNo;

    @TableField("product_color")
    private String productColor;

    @TableField("sku")
    private String sku;

    @TableField("product_code")
    private String productCode;

    @TableField("status")
    private Long status;

    @TableField("create_time")
    private Date createTime;

    @TableField("out_id")
    private String outId;

    @TableField("product_brand")
    private String productBrand;

    @TableField("ex_status")
    private Long exStatus;

    @TableField("as_status")
    private Long asStatus;

    @TableField("after_sale_id")
    private String afterSaleId;

    @TableField("supply_id")
    private String supplyId;

    @TableField("big_season")
    private String bigSeason;

    @TableField("year")
    private String year;

    @TableField("promotion_name")
    private String promotionName;

    @TableField("promotion_id")
    private Long promotionId;

    @TableField("topic")
    private String topic;

    @TableField("gbcode")
    private String gbcode;

    @TableField("update_time")
    private Date updateTime;

    @TableField("order_price")
    private String orderPrice;

    @TableField("big_class")
    private String bigClass;

    @TableField("small_class")
    private String smallClass;

    @TableField("remark")
    private String remark;

    @TableField("type")
    private Long type;

    @TableField("originalid")
    private String originalid;

    @TableField("eb")
    private Short eb;

    @TableField("pocket_tel_id")
    private String pocketTelId;

    @TableField("is_warn")
    private Short isWarn;

    @TableField("pocket_details_id")
    private String pocketDetailsId;

    @TableField("express_id")
    private String expressId;

    @TableField("change")
    private String change;

    @TableField("refund_status")
    private Long refundStatus;

    @TableField("order_status")
    private Long orderStatus;
    @TableField("send_store_id")
    private Long sendStoreId;
    @TableField("SEND_UNIONSTORE_ID")
    private Long sendUnionstoreId;

    // 是否千万仓
    @TableField("IS_QWC")
    private Boolean isQwc;
    // 是否城市仓
    @TableField("IS_CSC")
    private Boolean isCsc;

    public Boolean getIsQwc() {
        return isQwc;
    }

    public void setIsQwc(Boolean qwc) {
        isQwc = qwc;
    }

    public Boolean getIsCsc() {
        return isCsc;
    }

    public void setIsCsc(Boolean csc) {
        isCsc = csc;
    }

    public Long getSendUnionstoreId() {
        return sendUnionstoreId;
    }

    public void setSendUnionstoreId(Long sendUnionstoreId) {
        this.sendUnionstoreId = sendUnionstoreId;
    }

    public Long getSendStoreId() {
        return sendStoreId;
    }

    public void setSendStoreId(Long sendStoreId) {
        this.sendStoreId = sendStoreId;
    }

    @TableField("join_shop")
    private String joinShop;

    public String getJoinShop() {
        return joinShop;
    }

    public void setJoinShop(String joinShop) {
        this.joinShop = joinShop;
    }

    public Long getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(Long refundStatus) {
        this.refundStatus = refundStatus;
    }

    public Long getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Long orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getBoxId() {
        return boxId;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId == null ? null : boxId.trim();
    }

    public Long getProductNo() {
        return productNo;
    }

    public void setProductNo(Long productNo) {
        this.productNo = productNo;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getProductSize() {
        return productSize;
    }

    public void setProductSize(String productSize) {
        this.productSize = productSize == null ? null : productSize.trim();
    }

    public String getProductPrice() {
        return productPrice;
    }

    public void setProductPrice(String productPrice) {
        this.productPrice = productPrice == null ? null : productPrice.trim();
    }

    public String getProductFavorablePrice() {
        return productFavorablePrice;
    }

    public void setProductFavorablePrice(String productFavorablePrice) {
        this.productFavorablePrice = productFavorablePrice == null ? null : productFavorablePrice.trim();
    }

    public String getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(String vipPrice) {
        this.vipPrice = vipPrice == null ? null : vipPrice.trim();
    }

    public String getProductQuantity() {
        return productQuantity;
    }

    public void setProductQuantity(String productQuantity) {
        this.productQuantity = productQuantity == null ? null : productQuantity.trim();
    }

    public String getProductColorNo() {
        return productColorNo;
    }

    public void setProductColorNo(String productColorNo) {
        this.productColorNo = productColorNo == null ? null : productColorNo.trim();
    }

    public String getProductColor() {
        return productColor;
    }

    public void setProductColor(String productColor) {
        this.productColor = productColor == null ? null : productColor.trim();
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode == null ? null : productCode.trim();
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getOutId() {
        return outId;
    }

    public void setOutId(String outId) {
        this.outId = outId == null ? null : outId.trim();
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand == null ? null : productBrand.trim();
    }

    public Long getExStatus() {
        return exStatus;
    }

    public void setExStatus(Long exStatus) {
        this.exStatus = exStatus;
    }

    public Long getAsStatus() {
        return asStatus;
    }

    public void setAsStatus(Long asStatus) {
        this.asStatus = asStatus;
    }

    public String getAfterSaleId() {
        return afterSaleId;
    }

    public void setAfterSaleId(String afterSaleId) {
        this.afterSaleId = afterSaleId == null ? null : afterSaleId.trim();
    }

    public String getSupplyId() {
        return supplyId;
    }

    public void setSupplyId(String supplyId) {
        this.supplyId = supplyId == null ? null : supplyId.trim();
    }

    public String getBigSeason() {
        return bigSeason;
    }

    public void setBigSeason(String bigSeason) {
        this.bigSeason = bigSeason == null ? null : bigSeason.trim();
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year == null ? null : year.trim();
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName == null ? null : promotionName.trim();
    }

    public Long getPromotionId() {
        return promotionId;
    }

    public void setPromotionId(Long promotionId) {
        this.promotionId = promotionId;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic == null ? null : topic.trim();
    }

    public String getGbcode() {
        return gbcode;
    }

    public void setGbcode(String gbcode) {
        this.gbcode = gbcode == null ? null : gbcode.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOrderPrice() {
        return orderPrice;
    }

    public void setOrderPrice(String orderPrice) {
        this.orderPrice = orderPrice == null ? null : orderPrice.trim();
    }

    public String getBigClass() {
        return bigClass;
    }

    public void setBigClass(String bigClass) {
        this.bigClass = bigClass == null ? null : bigClass.trim();
    }

    public String getSmallClass() {
        return smallClass;
    }

    public void setSmallClass(String smallClass) {
        this.smallClass = smallClass == null ? null : smallClass.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getOriginalid() {
        return originalid;
    }

    public void setOriginalid(String originalid) {
        this.originalid = originalid == null ? null : originalid.trim();
    }

    public Short getEb() {
        return eb;
    }

    public void setEb(Short eb) {
        this.eb = eb;
    }

    public String getPocketTelId() {
        return pocketTelId;
    }

    public void setPocketTelId(String pocketTelId) {
        this.pocketTelId = pocketTelId == null ? null : pocketTelId.trim();
    }

    public Short getIsWarn() {
        return isWarn;
    }

    public void setIsWarn(Short isWarn) {
        this.isWarn = isWarn;
    }

    public String getPocketDetailsId() {
        return pocketDetailsId;
    }

    public void setPocketDetailsId(String pocketDetailsId) {
        this.pocketDetailsId = pocketDetailsId == null ? null : pocketDetailsId.trim();
    }

    public String getExpressId() {
        return expressId;
    }

    public void setExpressId(String expressId) {
        this.expressId = expressId == null ? null : expressId.trim();
    }

    public String getChange() {
        return change;
    }

    public void setChange(String change) {
        this.change = change == null ? null : change.trim();
    }

    public String getOrigProductId() {
        return origProductId;
    }

    public void setOrigProductId(String origProductId) {
        this.origProductId = origProductId;
    }
}

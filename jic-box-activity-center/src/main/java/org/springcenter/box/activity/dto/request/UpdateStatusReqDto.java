package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class UpdateStatusReqDto {


    @ApiModelProperty(value = "spuId")
    @NotBlank(message = "spuId不能为空")
    private String spuId;



    @ApiModelProperty(value = "上下架(0下架 1上架)")
    @NotNull(message = "上下架不能为空")
    private Long status;



    @ApiModelProperty(value = "修改人")
    @NotBlank(message = "修改人不能为空")
    private String updateBy;





}

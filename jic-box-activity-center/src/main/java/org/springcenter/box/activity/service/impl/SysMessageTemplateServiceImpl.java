package org.springcenter.box.activity.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcenter.box.activity.domain.box.SysMessageTemplate;
import org.springcenter.box.activity.mapper.box.SysMessageTemplateMapper;
import org.springcenter.box.activity.service.ISysMessageTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 消息模板
 * @Author: jeecg-boot
 * @Date:  2019-04-09
 * @Version: V1.0
 */
@Service
public class SysMessageTemplateServiceImpl extends ServiceImpl<SysMessageTemplateMapper, SysMessageTemplate> implements ISysMessageTemplateService {

    @Autowired
    private SysMessageTemplateMapper sysMessageTemplateMapper;


    @Override
    public List<SysMessageTemplate> selectByCode(String code) {
        return sysMessageTemplateMapper.selectByCode(code);
    }
}

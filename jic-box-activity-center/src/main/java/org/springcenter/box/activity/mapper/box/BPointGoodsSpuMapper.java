package org.springcenter.box.activity.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.box.activity.domain.box.BPointGoodsSpu;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2024-12-10 17:27:33
 * @Description: Mapper
 */
public interface BPointGoodsSpuMapper extends BaseMapper<BPointGoodsSpu> {

    List<BPointGoodsSpu> getListBySpuNo(@Param("spuNo") String spuNo);



    List<BPointGoodsSpu> getListBySearch(BPointGoodsSpu bPointGoodsSpu);





    List<BPointGoodsSpu> getMiniListBySearch(BPointGoodsSpu bPointGoodsSpu);


    List<BPointGoodsSpu> getAllList();


    /**
     * 查询上架最低点商品
     * @return
     */
    BPointGoodsSpu getLastLowPointGoodsSpu();
}

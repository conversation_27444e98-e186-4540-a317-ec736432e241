package org.springcenter.box.activity.mapper.box;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcenter.box.activity.domain.box.Order;
import org.springcenter.box.activity.dto.OrderDto;
import org.springcenter.box.activity.mapper.box.condition.OrderQueryCondition;

import java.util.List;


/**
 * @Author: CodeGenerator
 * @Date: 2024-10-07 11:25:06
 * @Description: Mapper
 */
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 根据条件查询主订单ID
     */
    List<OrderDto> listOrderIdLeftJoinPayment(OrderQueryCondition ew);

}

package org.springcenter.box.activity.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 发放状态
 */
public enum GiftSendStatusEnum {
    UN_SEND(0, "未发放"),
    WAIT_RESULT(1, "等待结果"),
    SUCCESS(2, "发放成功"),
    ERROR(3, "发放失败"),
    ;

    private static final Map<Integer, GiftSendStatusEnum> LOOKUP = new HashMap<>();

    static {
        for (GiftSendStatusEnum s : EnumSet.allOf(GiftSendStatusEnum.class)) {
            LOOKUP.put(s.getCode(), s);
        }
    }

    private Integer code;
    private String name;

    GiftSendStatusEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }

    public static String get(Integer code) {
        GiftSendStatusEnum orderStatusEnum = LOOKUP.get(code);
        return orderStatusEnum.getName();
    }

}
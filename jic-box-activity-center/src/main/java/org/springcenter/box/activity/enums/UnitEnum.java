package org.springcenter.box.activity.enums;

import com.google.common.base.Preconditions;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum UnitEnum {

    /**
     * 门槛单位：
     * 1=元
     * 2=件
     */

    AMOUNT(1, "金额"),
    QUANTITY(2, "件数"),
    ;
    public static final Map<Integer, UnitEnum> LOOKUP = new HashMap<>();

    static {
        for (UnitEnum s : EnumSet.allOf(UnitEnum.class)) {
            LOOKUP.put(s.getCode(), s);
        }
    }
    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static UnitEnum getUnitEnum(Integer code) {
        Preconditions.checkArgument(LOOKUP.containsKey(code), "未知单位");
        return LOOKUP.get(code);
    }
}

package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 消息
 * @Author: jeecg-boot
 * @Date:  2019-04-09
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_sms")
public class SysMessage implements Serializable {
	private String id;
	/**推送内容*/
	@Excel(name = "推送内容", width = 15)
	@TableField("es_content")
	private String esContent;

	/**推送所需参数Json格式*/
	@Excel(name = "推送所需参数Json格式", width = 15)
	@TableField("es_param")
	private String esParam;

	/**接收人*/
	@Excel(name = "接收人", width = 15)
	@TableField("es_receiver")
	private String esReceiver;

	/**推送失败原因*/
	@Excel(name = "推送失败原因", width = 15)
	@TableField("es_result")
	private String esResult;
	/**发送次数*/
	@Excel(name = "发送次数", width = 15)
	@TableField("es_send_num")
	private Integer esSendNum;

	/**推送状态 0未推送 1推送成功 2推送失败*/
	@Excel(name = "推送状态 0未推送 1推送成功 2推送失败", width = 15)
	@TableField("es_send_status")
	private String esSendStatus;

	/**推送时间*/
	@Excel(name = "推送时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@TableField("es_send_time")
	private Date esSendTime;

	/**消息code*/
	@Excel(name = "消息标题", width = 15)
	@TableField("es_title")
	private String esTitle;

	/**推送方式：1短信 2邮件 3微信*/
	@Excel(name = "推送方式：1短信 2邮件 3微信", width = 15)
	@TableField("es_type")
	private String esType;

	/**备注*/
	@Excel(name = "备注", width = 15)
	@TableField("remark")
	private String remark;

	@TableField("create_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;
	
	@TableField("update_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;
}

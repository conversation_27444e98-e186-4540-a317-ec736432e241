package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("赠品excel导入")
@Data
public class JoinRecordImportExcelReq {
    @ApiModelProperty(value = "活动ID", required = true)
    private String activityId;
    @ApiModelProperty(value = "操作人", required = true)
    private String optPerson;
    @ApiModelProperty(value = "url", required = true)
    private String excelUrl;
}

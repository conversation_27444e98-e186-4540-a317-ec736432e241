package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: wangchun
 * @Date: 2022-04-19 16:38:21
 * @Description: 
 */
@TableName("B_LOGISTICS_SNAPSHOT")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="BLogisticsSnapshot对象", description="")
public class BLogisticsSnapshot implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "发件人")
    @TableField("SEND_CONTACT")
    private String sendContact;
    @ApiModelProperty(value = "发件人联系方式")
    @TableField("SEND_PHONE")
    private String sendPhone;
    @ApiModelProperty(value = "发件人单位")
    @TableField("SEND_COMPANY")
    private String sendCompany;
    @ApiModelProperty(value = "发件人省")
    @TableField("SEND_PROVINCE")
    private String sendProvince;
    @ApiModelProperty(value = "发件人市")
    @TableField("SEND_CITY")
    private String sendCity;
    @ApiModelProperty(value = "发件人区")
    @TableField("SEND_DISTRICT")
    private String sendDistrict;
    @ApiModelProperty(value = "发件人地址")
    @TableField("SEND_ADDRESS")
    private String sendAddress;
    @ApiModelProperty(value = "收件人")
    @TableField("RECEIVE_CONTACT")
    private String receiveContact;
    @ApiModelProperty(value = "收件人联系方式")
    @TableField("RECEIVE_PHONE")
    private String receivePhone;
    @ApiModelProperty(value = "收件人单位")
    @TableField("RECEIVE_COMPANY")
    private String receiveCompany;
    @ApiModelProperty(value = "收件人省")
    @TableField("RECEIVE_PROVINCE")
    private String receiveProvince;
    @ApiModelProperty(value = "收件人市")
    @TableField("RECEIVE_CITY")
    private String receiveCity;
    @ApiModelProperty(value = "收件人区")
    @TableField("RECEIVE_DISTRICT")
    private String receiveDistrict;
    @ApiModelProperty(value = "收件人地址")
    @TableField("RECEIVE_ADDRESS")
    private String receiveAddress;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;

    @ApiModelProperty(value = "预约取件时间")
    @TableField("GET_TIME")
    private String getTime;


    public String getGetTime() {
        return getTime;
    }

    public BLogisticsSnapshot setGetTime(String getTime) {
        this.getTime = getTime;
        return this;
    }

    public String getId() {
        return id;
    }

    public BLogisticsSnapshot setId(String id) {
        this.id = id;
        return this;
    }

    public String getSendContact() {
        return sendContact;
    }

    public BLogisticsSnapshot setSendContact(String sendContact) {
        this.sendContact = sendContact;
        return this;
    }

    public String getSendPhone() {
        return sendPhone;
    }

    public BLogisticsSnapshot setSendPhone(String sendPhone) {
        this.sendPhone = sendPhone;
        return this;
    }

    public String getSendCompany() {
        return sendCompany;
    }

    public BLogisticsSnapshot setSendCompany(String sendCompany) {
        this.sendCompany = sendCompany;
        return this;
    }

    public String getSendProvince() {
        return sendProvince;
    }

    public BLogisticsSnapshot setSendProvince(String sendProvince) {
        this.sendProvince = sendProvince;
        return this;
    }

    public String getSendCity() {
        return sendCity;
    }

    public BLogisticsSnapshot setSendCity(String sendCity) {
        this.sendCity = sendCity;
        return this;
    }

    public String getSendDistrict() {
        return sendDistrict;
    }

    public BLogisticsSnapshot setSendDistrict(String sendDistrict) {
        this.sendDistrict = sendDistrict;
        return this;
    }

    public String getSendAddress() {
        return sendAddress;
    }

    public BLogisticsSnapshot setSendAddress(String sendAddress) {
        this.sendAddress = sendAddress;
        return this;
    }

    public String getReceiveContact() {
        return receiveContact;
    }

    public BLogisticsSnapshot setReceiveContact(String receiveContact) {
        this.receiveContact = receiveContact;
        return this;
    }

    public String getReceivePhone() {
        return receivePhone;
    }

    public BLogisticsSnapshot setReceivePhone(String receivePhone) {
        this.receivePhone = receivePhone;
        return this;
    }

    public String getReceiveCompany() {
        return receiveCompany;
    }

    public BLogisticsSnapshot setReceiveCompany(String receiveCompany) {
        this.receiveCompany = receiveCompany;
        return this;
    }

    public String getReceiveProvince() {
        return receiveProvince;
    }

    public BLogisticsSnapshot setReceiveProvince(String receiveProvince) {
        this.receiveProvince = receiveProvince;
        return this;
    }

    public String getReceiveCity() {
        return receiveCity;
    }

    public BLogisticsSnapshot setReceiveCity(String receiveCity) {
        this.receiveCity = receiveCity;
        return this;
    }

    public String getReceiveDistrict() {
        return receiveDistrict;
    }

    public BLogisticsSnapshot setReceiveDistrict(String receiveDistrict) {
        this.receiveDistrict = receiveDistrict;
        return this;
    }

    public String getReceiveAddress() {
        return receiveAddress;
    }

    public BLogisticsSnapshot setReceiveAddress(String receiveAddress) {
        this.receiveAddress = receiveAddress;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public BLogisticsSnapshot setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public BLogisticsSnapshot setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return "BLogisticsSnapshotModel{" +
            "id=" + id +
            ", sendContact=" + sendContact +
            ", sendPhone=" + sendPhone +
            ", sendCompany=" + sendCompany +
            ", sendProvince=" + sendProvince +
            ", sendCity=" + sendCity +
            ", sendDistrict=" + sendDistrict +
            ", sendAddress=" + sendAddress +
            ", receiveContact=" + receiveContact +
            ", receivePhone=" + receivePhone +
            ", receiveCompany=" + receiveCompany +
            ", receiveProvince=" + receiveProvince +
            ", receiveCity=" + receiveCity +
            ", receiveDistrict=" + receiveDistrict +
            ", receiveAddress=" + receiveAddress +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            "}";
    }
}

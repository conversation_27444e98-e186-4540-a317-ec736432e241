package org.springcenter.box.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcenter.box.activity.domain.box.CustomerDetails;
import org.springcenter.box.activity.mapper.box.CustomerDetailsMapper;
import org.springcenter.box.activity.service.ICustomerDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomerDetailsServiceImpl extends ServiceImpl<CustomerDetailsMapper, CustomerDetails> implements ICustomerDetailsService {

    @Override
    public Map<String, String> unionId2CustomerIdMap(List<String> unionIdList) {
        log.info("根据unionId查询customerId,入参:{}", JSON.toJSONString(unionIdList));
        Map<String, String> map = Optional.ofNullable(
                        this.list(new LambdaQueryWrapper<CustomerDetails>()
                                        .select(CustomerDetails::getUnionid, CustomerDetails::getId)
                                .in(CustomerDetails::getUnionid, unionIdList))
                )
                .orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(CustomerDetails::getUnionid, CustomerDetails::getId, (a, b) -> a));
        log.info("根据unionId查询customerId,回参:{}", JSON.toJSONString(map));
        return map;
    }
}

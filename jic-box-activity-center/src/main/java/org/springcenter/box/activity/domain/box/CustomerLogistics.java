package org.springcenter.box.activity.domain.box;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: CodeGenerator
 * @Date: 2024-10-10 10:14:03
 * @Description: 
 */
@TableName("CUSTOMER_LOGISTICS")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="CustomerLogistics对象", description="")
public class CustomerLogistics implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键id")
    @TableId(value = "ID")
    private String id;
    @ApiModelProperty(value = "顾客unionid")
    @TableField("UNIONID")
    private String unionid;
    @ApiModelProperty(value = "省")
    @TableField("PROVINCE")
    private String province;
    @ApiModelProperty(value = "市")
    @TableField("CITY")
    private String city;
    @ApiModelProperty(value = "区")
    @TableField("DISTRICT")
    private String district;
    @ApiModelProperty(value = "详细地址")
    @TableField("ADDRESS")
    private String address;
    @ApiModelProperty(value = "邮政编码")
    @TableField("CODE")
    private String code;
    @TableField("STATUS")
    private Long status;
    @ApiModelProperty(value = "收件人")
    @TableField("CONTACT_NAME")
    private String contactName;
    @ApiModelProperty(value = "联系号码")
    @TableField("CONTACT_PHONE")
    private String contactPhone;
    @ApiModelProperty(value = "0:正常;1:删除")
    @TableField("DEL")
    private Long del;
    @ApiModelProperty(value = "创建日期")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "修改日期")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "删除日期")
    @TableField("DEL_TIME")
    private Date delTime;
    @ApiModelProperty(value = "默认地址(0:非默认;1:默认)")
    @TableField("LOG_DEFAULT")
    private Long logDefault;


    public String getId() {
        return id;
    }

    public CustomerLogistics setId(String id) {
        this.id = id;
        return this;
    }

    public String getUnionid() {
        return unionid;
    }

    public CustomerLogistics setUnionid(String unionid) {
        this.unionid = unionid;
        return this;
    }

    public String getProvince() {
        return province;
    }

    public CustomerLogistics setProvince(String province) {
        this.province = province;
        return this;
    }

    public String getCity() {
        return city;
    }

    public CustomerLogistics setCity(String city) {
        this.city = city;
        return this;
    }

    public String getDistrict() {
        return district;
    }

    public CustomerLogistics setDistrict(String district) {
        this.district = district;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public CustomerLogistics setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getCode() {
        return code;
    }

    public CustomerLogistics setCode(String code) {
        this.code = code;
        return this;
    }

    public Long getStatus() {
        return status;
    }

    public CustomerLogistics setStatus(Long status) {
        this.status = status;
        return this;
    }

    public String getContactName() {
        return contactName;
    }

    public CustomerLogistics setContactName(String contactName) {
        this.contactName = contactName;
        return this;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public CustomerLogistics setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
        return this;
    }

    public Long getDel() {
        return del;
    }

    public CustomerLogistics setDel(Long del) {
        this.del = del;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public CustomerLogistics setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public CustomerLogistics setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Date getDelTime() {
        return delTime;
    }

    public CustomerLogistics setDelTime(Date delTime) {
        this.delTime = delTime;
        return this;
    }

    public Long getLogDefault() {
        return logDefault;
    }

    public CustomerLogistics setLogDefault(Long logDefault) {
        this.logDefault = logDefault;
        return this;
    }

    @Override
    public String toString() {
        return "CustomerLogisticsModel{" +
            "id=" + id +
            ", unionid=" + unionid +
            ", province=" + province +
            ", city=" + city +
            ", district=" + district +
            ", address=" + address +
            ", code=" + code +
            ", status=" + status +
            ", contactName=" + contactName +
            ", contactPhone=" + contactPhone +
            ", del=" + del +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", delTime=" + delTime +
            ", logDefault=" + logDefault +
            "}";
    }
}

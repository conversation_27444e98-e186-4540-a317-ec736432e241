package org.springcenter.box.activity.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.xxl.job.core.context.XxlJobContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.domain.box.BPointDetail;
import org.springcenter.box.activity.domain.box.BPointGoodsSpu;
import org.springcenter.box.activity.domain.box.BUserPointAccount;
import org.springcenter.box.activity.domain.box.CustomerDetails;
import org.springcenter.box.activity.dto.request.JoinRecordImportExcelReq;
import org.springcenter.box.activity.dto.request.JoinRecordListReq;
import org.springcenter.box.activity.dto.request.TestDirectSendByAccountIdReqDto;
import org.springcenter.box.activity.dto.request.UpdatePointDetailByIdsReqDto;
import org.springcenter.box.activity.event.TestEvent;
import org.springcenter.box.activity.event.bus.TestEventBus;
import org.springcenter.box.activity.job.*;
import org.springcenter.box.activity.job.jst.JstOrdersLogisticsJob;
import org.springcenter.box.activity.job.jst.PushJstOrdersJob;
import org.springcenter.box.activity.job.point.PointExpireBefore27SendMsgJob;
import org.springcenter.box.activity.job.point.PointExpireBefore7SendMsgJob;
import org.springcenter.box.activity.job.subscribe.ExpireAddRecordToDetailJob;
import org.springcenter.box.activity.job.subscribe.SpuHasStockJob;
import org.springcenter.box.activity.job.subscribe.PointDetailExpireTimeJob;
import org.springcenter.box.activity.job.subscribe.UserAccountPointJob;
import org.springcenter.box.activity.mapper.box.*;
import org.springcenter.box.activity.service.ICActivityService;
import org.springcenter.box.activity.service.ICombinePointDetailService;
import org.springcenter.box.activity.service.IJoinRecordService;
import org.springcenter.box.activity.service.IStoreCenterHttpService;
import org.springcenter.box.activity.service.impl.IHandleHistoryPointService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@RequestMapping("/box/activity/test")
@RestController
@Api(tags = "测试接口")
@Slf4j
public class TestController {
    @Resource
    private IJoinRecordService joinRecordService;
    @Resource
    private ICActivityService activityService;
    @Resource
    private UpdateTaskJob updateTaskJob;
    @Resource
    private IStoreCenterHttpService storeCenterHttpService;

    @Resource
    private TestEventBus testEventBus;

    @Resource
    private PushJstOrdersJob pushJstOrdersJob;

    @Resource
    private JstOrdersLogisticsJob jstOrdersLogisticsJob;

    @Resource
    private PointDetailExpireTimeJob pointDetailExpireTimeJob;

    @Resource
    private SpuHasStockJob spuHasStockJob;


    @Resource
    private UserAccountPointJob userAccountPointJob;

    @Resource
    private PointExpireBefore27SendMsgJob pointExpireBefore27SendMsgJob;

    @Resource
    private BUserPointAccountMapper bUserPointAccountMapper;

    @Resource
    private PointExpireBefore7SendMsgJob pointExpireBefore7SendMsgJob;

    @Resource
    private CustomerDetailsMapper customerDetailsMapper;

    @Resource
    private BSubscribeInfoMapper bSubscribeInfoMapper;

    @Resource
    private BPointDetailMapper bPointDetailMapper;

    @Resource
    private BPointGoodsSpuMapper bPointGoodsSpuMapper;

    @Resource
    private IHandleHistoryPointService iHandleHistoryPointService;

    @Resource
    private ExpireAddRecordToDetailJob expireAddRecordToDetailJob;

    @Resource
    private ICombinePointDetailService iCombinePointDetailService;


    @ApiOperation(value = "导出测试", notes = "默认根据创建时间倒序。支持条件：BOX单号，快递单号")
    @PostMapping("/export")
    public ResponseResult<String> export(@Validated @RequestBody CommonRequest<JoinRecordListReq> request) {
        JoinRecordListReq requestData = request.getRequestData();
        log.info("导出测试,入参:{}", JSON.toJSONString(requestData));
        String exportUrl = joinRecordService.exportJoinRecord(requestData);
        log.info("导出测试,回参:{}", exportUrl);
        return ResponseResult.success(exportUrl);
    }

    @ApiOperation(value = "测试一键发货", notes = "模拟XXL-JOB执行调度任务")
    @PostMapping("/deliveryJobExecute")
    public ResponseResult<String> deliveryJobExecute() {
        log.info("测试一键发货 开始");
        activityService.deliveryJobExecute();
        log.info("测试一键发货 结束");
        return ResponseResult.success();
    }

    @ApiOperation(value = "测试一键发货结果回调", notes = "模拟XXL-JOB执行调度任务")
    @PostMapping("/deliveryCallbackJob")
    public ResponseResult<String> deliveryCallbackJob() {
        log.info("测试一键发货结果回调 开始");
        joinRecordService.deliveryCallbackJob();
        log.info("测试一键发货结果回调 结束");
        return ResponseResult.success();
    }

    @ApiOperation(value = "测试增量兜底任务", notes = "模拟XXL-JOB执行调度任务")
    @PostMapping("/updateTaskJob")
    public ResponseResult<String> updateTaskJob(String jobParam) {
        log.info("测试一键发货结果回调 开始");
        XxlJobContext context = new XxlJobContext(111, jobParam, null, 1, 1);
        XxlJobContext.setXxlJobContext(context);
        updateTaskJob.execute();
        log.info("测试一键发货结果回调 结束");
        return ResponseResult.success();
    }


    @ApiOperation(value = "测试事件", notes = "测试事件")
    @PostMapping("/testEvent")
    public ResponseResult testEvent() throws PersistentBus.EventBusException {


        TestEvent testEvent = new TestEvent("测试事件", "12",
           1L,2L,
                UUID.randomUUID());
        testEventBus.post(testEvent);

        return ResponseResult.success();

    }



    @ApiOperation(value = "推送jstJob", notes = "推送jstJob")
    @PostMapping("/pushJst")
    public ResponseResult pushJst() throws Exception {
        pushJstOrdersJob.execute();
        return ResponseResult.success();
    }



    @ApiOperation(value = "聚水潭拉取物流信息job", notes = "聚水潭拉取物流信息")
    @PostMapping("/jstOrdersLogistics")
    public ResponseResult jstOrdersLogistics() throws Exception {
        jstOrdersLogisticsJob.execute();
        return ResponseResult.success();
    }


    @ApiOperation(value = "处理流水过期时间", notes = "处理流水过期时间")
    @PostMapping("/subTimeJob")
    public ResponseResult subTimeJob() throws Exception {
        pointDetailExpireTimeJob.execute();
        return ResponseResult.success();
    }


    @ApiOperation(value = "spu规整库存job", notes = "spu规整库存job")
    @PostMapping("/spuHasStockJob")
    public ResponseResult spuHasStockJob() throws Exception {
        spuHasStockJob.execute();
        return ResponseResult.success();
    }


    @ApiOperation(value = "用户账户点数job", notes = "用户账户点数job")
    @PostMapping("/userAccountPointJob")
    public ResponseResult userAccountPointJob() throws Exception {
        userAccountPointJob.execute();
        return ResponseResult.success();
    }


    @ApiOperation(value = "测试命中门店")
    @PostMapping("/testHitStore")
    public ResponseResult<Boolean> testHitStore(Long packId, Long storeId) {
        return ResponseResult.success(storeCenterHttpService.hitStore(packId, storeId));
    }

    @ApiOperation(value = "测试导入兑换卡密")
    @PostMapping("/testImportExchangeGift")
    public ResponseResult testImportExchangeGift(JoinRecordImportExcelReq req) {
        return joinRecordService.importExchangeGift(req);
    }


    @ApiOperation(value = "测试获取符合的账户")
    @PostMapping("/testGetAdaptAccount")
    public ResponseResult testGetAdaptAccount(){
        return  ResponseResult.success(pointExpireBefore27SendMsgJob.getAllAdaptAccount(BigDecimal.valueOf(5926)));
    }


    @ApiOperation(value = "指定发送人（条件满足7天）")
    @PostMapping("/sendMsg7DaysByAccountId/{accountId}")
    public ResponseResult sendMsg7DaysByAccountId(@PathVariable String accountId){
        BUserPointAccount bUserPointAccount = bUserPointAccountMapper.selectById(accountId);

        // 1. 先查 最低的spu 点数
        BPointGoodsSpu bPointGoodsSpu = bPointGoodsSpuMapper.getLastLowPointGoodsSpu();
        if (bUserPointAccount.getTotalPoint().compareTo(bPointGoodsSpu.getPoint()) < 0) {
            throw new BoxActivityException("点数不满足");
        }

        pointExpireBefore7SendMsgJob.sendMsg(bUserPointAccount);
        return  ResponseResult.success();
    }

    @ApiOperation(value = "指定发送人（条件满足27天）")
    @PostMapping("/sendMsg27DaysByAccountId/{accountId}")
    public ResponseResult sendMsg27DaysByAccountId(@PathVariable String accountId){
        BUserPointAccount bUserPointAccount = bUserPointAccountMapper.selectById(accountId);
        // 1. 先查 最低的spu 点数
        BPointGoodsSpu bPointGoodsSpu = bPointGoodsSpuMapper.getLastLowPointGoodsSpu();
        if (bUserPointAccount.getTotalPoint().compareTo(bPointGoodsSpu.getPoint()) < 0) {
            throw new BoxActivityException("点数不满足");
        }
        pointExpireBefore27SendMsgJob.sendMsg(bUserPointAccount);
        return  ResponseResult.success();
    }

    @ApiOperation(value = "直接发送人（无任何条件）")
    @PostMapping("/testDirectSendByAccountId")
    public ResponseResult testDirectSendByAccountId(@Validated @RequestBody CommonRequest<TestDirectSendByAccountIdReqDto> request){
        TestDirectSendByAccountIdReqDto testDirectSendByAccountIdReqDto = Preconditions.checkNotNull(request.getRequestData(), "入参不能为空");
        BigDecimal expirePoint =testDirectSendByAccountIdReqDto.getExpirePoint();
        BUserPointAccount bUserPointAccount = bUserPointAccountMapper.selectById(testDirectSendByAccountIdReqDto.getAccountId());

        CustomerDetails customerDetails= customerDetailsMapper.selectById(bUserPointAccount.getId());
        pointExpireBefore27SendMsgJob.sendMiniMsg(expirePoint,bUserPointAccount,customerDetails);
        pointExpireBefore27SendMsgJob.sendSmsMsg(expirePoint,customerDetails);
        return  ResponseResult.success();
    }

    @ApiOperation(value = "job-过期7天前过期的短信和微信小程序消息")
    @PostMapping("/pointExpireBefore7SendMsgJob")
    public ResponseResult pointExpireBefore7SendMsgJob() throws Exception {
        pointExpireBefore7SendMsgJob.execute();
        return  ResponseResult.success();
    }


    @ApiOperation(value = "job-过期27天前过期的短信和微信小程序消息")
    @PostMapping("/pointExpireBefore27SendMsgJob")
    public ResponseResult pointExpireBefore27SendMsgJob() throws Exception {
        pointExpireBefore27SendMsgJob.execute();
        return  ResponseResult.success();
    }

    @ApiOperation(value = "测试-修改点数信息")
    @PostMapping("/updateSubById")
    public ResponseResult updateSubById(@Validated @RequestBody CommonRequest<UpdatePointDetailByIdsReqDto> commonRequest) throws Exception {
        log.info("测试-修改点数信息入参request:{}", JSONObject.toJSONString(commonRequest));
        UpdatePointDetailByIdsReqDto updatePointDetailByIdsReqDto = Preconditions.checkNotNull(commonRequest.getRequestData(), "入参不能为空");
        updatePointDetailByIdsReqDto.getIds().forEach(e->{
            BPointDetail bPointDetailTemp = new BPointDetail();
            bPointDetailTemp.setId(e);
            bPointDetailTemp.setUpdateTime(new Date());
            bPointDetailTemp.setUpdateBy(updatePointDetailByIdsReqDto.getUpdateBy());
            bPointDetailTemp.setExpireTime(updatePointDetailByIdsReqDto.getEndTime());
            bPointDetailMapper.updateById(bPointDetailTemp);
        });
        return  ResponseResult.success();
    }




    @ApiOperation(value = "处理历史积分type类型正确显示")
    @PostMapping("/handleHistoryPointChangeType")
    public ResponseResult handleHistoryPointChangeType()  {
        iHandleHistoryPointService.handleHistoryPointChangeType();
        return ResponseResult.success();
    }


    @ApiOperation(value = "根据subId处理到期数据")
    @PostMapping("/realHandleSubIdAddRecord/{subId}")
    public ResponseResult realHandleSubIdAddRecord(@PathVariable String subId) {
        iCombinePointDetailService.realHandleSubIdAddRecord(subId);
        return ResponseResult.success();
    }



    @ApiOperation(value = "处理历史所有到期的subId的流水数据插入")
    @PostMapping("/handleAllHistoryAddExpireRecord")
    public ResponseResult handleAllHistoryAddExpireRecord() {
        expireAddRecordToDetailJob.handleAllHistoryAddExpireRecord();
        return ResponseResult.success();
    }


    @ApiOperation(value = "处理过期插入过期Job(两天内)")
    @PostMapping("/expireAddRecordToDetailJob")
    public ResponseResult expireAddRecordToDetailJob() throws Exception {
        expireAddRecordToDetailJob.execute();
        return ResponseResult.success();
    }

}

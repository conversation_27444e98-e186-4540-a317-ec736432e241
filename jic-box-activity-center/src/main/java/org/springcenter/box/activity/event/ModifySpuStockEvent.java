package org.springcenter.box.activity.event;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.killbill.bus.api.BusEvent;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class ModifySpuStockEvent implements BusEvent{
    private List<String> spuIdList;
    private final Long searchKey1;
    private final Long searchKey2;
    private final UUID userToken;



    @JsonCreator
    public ModifySpuStockEvent(
            @JsonProperty("spuId") List<String> spuIdList,
            @JsonProperty("searchKey1") final Long searchKey1,
            @JsonProperty("searchKey2") final Long searchKey2,
            @JsonProperty("userToken") final UUID userToken) {
        this.spuIdList = spuIdList;
        this.searchKey1 = searchKey1;
        this.searchKey2 = searchKey2;
        this.userToken = userToken;
    }

    @Override
    public Long getSearchKey1() {
        return this.searchKey1;
    }

    @Override
    public Long getSearchKey2() {
        return searchKey2;
    }

    @Override
    public UUID getUserToken() {
        return this.userToken;
    }


    public List<String> getSpuIdList() {
        return spuIdList;
    }

    public void setSpuIdList(List<String> spuIdList) {
        this.spuIdList = spuIdList;
    }
}

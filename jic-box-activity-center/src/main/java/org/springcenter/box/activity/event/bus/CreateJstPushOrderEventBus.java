package org.springcenter.box.activity.event.bus;

import lombok.extern.slf4j.Slf4j;
import org.killbill.bus.DefaultPersistentBus;
import org.killbill.bus.api.PersistentBus;
import org.springcenter.box.activity.event.CreateJstPushOrderEvent;
import org.springcenter.box.activity.event.handler.CreateJstPushOrderHandler;
import org.springcenter.box.activity.service.BaseEventBus;
import org.springcenter.box.activity.service.IBusEventsService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
@Slf4j
public class CreateJstPushOrderEventBus implements InitializingBean, BaseEventBus<CreateJstPushOrderEvent> {

    @Autowired
    private DefaultPersistentBus persistentBus;

    @Autowired
    private IBusEventsService iBusEventsService;

    @Resource
    private CreateJstPushOrderHandler createJstPushOrderHandler;

    @Override
    public void afterPropertiesSet() throws Exception {
        persistentBus.register(createJstPushOrderHandler);
    }

    @Override
    public void post(CreateJstPushOrderEvent createJstPushOrderEvent) throws PersistentBus.EventBusException {
        iBusEventsService.createBusEvent(createJstPushOrderEvent);
    }
}

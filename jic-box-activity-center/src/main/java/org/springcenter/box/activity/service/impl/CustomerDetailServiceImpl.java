package org.springcenter.box.activity.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springcenter.box.activity.api.dto.SalesCustomerListResp;
import org.springcenter.box.activity.api.dto.SalesCustomerReq;
import org.springcenter.box.activity.domain.box.BCustomerInformation;
import org.springcenter.box.activity.domain.box.BSubscribeInfo;
import org.springcenter.box.activity.domain.box.BSubscribePlan;
import org.springcenter.box.activity.domain.box.CustomerDetails;
import org.springcenter.box.activity.mapper.box.BSubscribeInfoMapper;
import org.springcenter.box.activity.mapper.box.BSubscribePlanMapper;
import org.springcenter.box.activity.mapper.box.CustomerDetailsMapper;
import org.springcenter.box.activity.service.IBCustomerInformationService;
import org.springcenter.box.activity.service.ICombineAccountService;
import org.springcenter.box.activity.service.ICustomerDetailService;
import org.springcenter.box.activity.util.DateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CustomerDetailServiceImpl extends ServiceImpl<CustomerDetailsMapper, CustomerDetails> implements ICustomerDetailService {

    @Resource
    private BSubscribeInfoMapper ibSubscribeInfoService;

    @Resource
    private BSubscribePlanMapper ibSubscribePlanService;

    @Resource
    private IBCustomerInformationService ibCustomerInformationService;

    @Resource
    private ICombineAccountService iCombineAccountService;

    @Override
    public List<SalesCustomerListResp> querySalesBoxSubscribeMember(SalesCustomerReq salesCustomerReq, Page page) {
        com.github.pagehelper.Page<SalesCustomerListResp> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        baseMapper.salesSubscribeCustomerList(salesCustomerReq);
        PageInfo<SalesCustomerListResp> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<SalesCustomerListResp> list = pageInfo.getList();
        if (list.isEmpty()) return list;

        buildCustomerList(list);
        return list;
    }

    private void buildCustomerList(List<SalesCustomerListResp> list){
        List<String> userIds = streamMapUserId(list);
        Map<String, Boolean> mapUsersBlack = streamMapUserBlack(userIds);
        Map<String, BSubscribeInfo> mapSubInfo = streamMapSubInfo(userIds);
        Map<String, List<BSubscribePlan>> mapSubPlan = streamMapUserSubscribePlan(mapSubInfo, userIds);
        list.forEach(item -> {
            item.setIsBlack(mapUsersBlack.get(item.getUserId()));
            if (mapSubPlan != null && mapSubPlan.containsKey(item.getUserId())){
                log.info("查询出用户 userId = {}存在订阅计划节点", item.getUserId());
                List<BSubscribePlan> bSubscribePlans = mapSubPlan.get(item.getUserId());
                BSubscribePlan bSubscribePlan = bSubscribePlans.get(0);
                item.setHaveSubscribe(true);
                SalesCustomerListResp.SubscribePlan subscribePlan = new SalesCustomerListResp.SubscribePlan();
                subscribePlan.setTotalPrice(mapSubInfo.get(bSubscribePlan.getSubId()).getTotalPrice().toString());
                //转化日期
                if (bSubscribePlan.getStatus().equals(0l)){
                    subscribePlan.setDateStr(DateUtil.formatToStr(bSubscribePlan.getPlanMonth(), DateUtil.DATEFORMATE_CN_YEARMONTH));
                    subscribePlan.setMonthStr(bSubscribePlan.getPlanMonthStr().toString());
                }
                subscribePlan.setSubId(bSubscribePlan.getSubId());
                item.setSubscribePlan(subscribePlan);
            }
        });
    }

    private Map<String, BSubscribeInfo> streamMapSubInfo(List<String> userIds){
        List<BSubscribeInfo> subscribeInfos = ibSubscribeInfoService.selectUserEffectSubInfo(userIds);
        if (subscribeInfos.isEmpty()) return null;

        Map<String, BSubscribeInfo> map = subscribeInfos.stream().collect(Collectors.toMap(
                BSubscribeInfo::getId,
                obj -> obj,
                (key1 , key2) -> key1
        ));
        return map;
    }

    private Map<String/** userid */, List<BSubscribePlan>> streamMapUserSubscribePlan(Map<String, BSubscribeInfo> bSubscribeInfoMap, List<String> userIds){
        if (bSubscribeInfoMap == null) return null;

        List<String> subIds = bSubscribeInfoMap.keySet().stream().collect(Collectors.toList());
        Map<String /** subId */, BSubscribePlan> map = getUnCompleteRecentSubscribePlanMapBySubIds(subIds);
        return map.values().stream().collect(Collectors.groupingBy(BSubscribePlan::getCustId));
    }



    private Map<String, Boolean> streamMapUserBlack(List<String> userIds){
        return checkBatchUserIsBlack(userIds);
    }

    private List<String> streamMapUserId(List<SalesCustomerListResp> list){
        return list.stream().map(item -> item.getUserId()).filter(item -> Objects.nonNull(item)).collect(Collectors.toList());
    }

    public Map<String, BSubscribePlan> getUnCompleteRecentSubscribePlanMapBySubIds(List<String> subIds) {
        List<BSubscribePlan> bSubscribePlanList = ibSubscribePlanService.getUnCompleteSubscribePlanListBySubIds(subIds);
        Map<String, BSubscribePlan> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(bSubscribePlanList)) {
            return resultMap;
        }

        Map<String, List<BSubscribePlan>> listMap = bSubscribePlanList.stream().collect(Collectors.groupingBy(BSubscribePlan::getSubId));
        listMap.keySet().stream().forEach(e -> {
            resultMap.put(e, listMap.get(e).stream().sorted(Comparator.comparing(BSubscribePlan::getStatus).thenComparing(BSubscribePlan::getPlanMonth).thenComparing(BSubscribePlan::getCreateTime)).collect(Collectors.toList()).stream().findFirst().orElse(null));
        });
        return resultMap;
    }

    /**
     * 批量查询用户是否被拉黑
     * @param userIds
     * @return
     */
    public Map<String, Boolean> checkBatchUserIsBlack(List<String> userIds){
        List<BCustomerInformation> list = ibCustomerInformationService.listByIds(userIds);
        Map<String, Boolean> map = new HashMap<>();
        userIds.forEach(item -> {
            BCustomerInformation bCustomerInformation = list.stream().filter(i -> i.getUserId().equals(item)).findFirst().orElse(null);
            map.put(item, false);
            if(bCustomerInformation != null
                    && bCustomerInformation.getbCategoryId() != null
                    && bCustomerInformation.getbCategoryId() == 57L ){
                map.put(item, true);
            }
        });
        return map;
    }
}

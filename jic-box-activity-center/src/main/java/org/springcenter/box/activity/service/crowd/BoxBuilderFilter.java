package org.springcenter.box.activity.service.crowd;

import com.google.common.base.Splitter;
import org.springcenter.box.activity.api.dto.BoxBuilderReq;
import org.springcenter.box.activity.service.IStoreCenterHttpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 搭盒人员
 */
@Slf4j
@Service
public class BoxBuilderFilter implements CrowdFilterHandler {

    CrowdFilterHandler next = null;

    @Override
    public String filterName() {
        return "搭盒人员类型";
    }

    @Override
    public CrowdFilterHandler getNext() {
        return next;
    }

    @Override
    public void setNext(CrowdFilterHandler next) {
        this.next = next;
    }

    @Override
    public boolean filter(CrowdFilterContext context) {
        Boolean boxBuilderGuide = context.getActivityInfo().getBoxBuilderGuide();
        String boxBuilderGuideStore = context.getActivityInfo().getBoxBuilderGuideStore();
        Boolean boxBuilderFashioner = context.getActivityInfo().getBoxBuilderFashioner();

        BoxBuilderReq boxBuilderReq = context.getBoxBuilderReq();
        Integer builderType = boxBuilderReq.getBuilderType();
        if (builderType == null) {
            context.setErrorMsg("搭盒人员类型为空，无法判断搭盒人员条件");
            return false;
        }

        if (builderType == 1 && !Boolean.TRUE.equals(boxBuilderFashioner)) {
            context.setErrorMsg("当前活动搭盒人员不支持搭配师");
            return false;
        }

        if (builderType == 2) {
            if (!Boolean.TRUE.equals(boxBuilderGuide)) {
                context.setErrorMsg("当前活动搭盒人员不支持导购");
                return false;
            }

            String guiderStoreId = context.getGuiderStoreId();
            if (guiderStoreId == null) {
                context.setErrorMsg("当前活动搭盒人员导购门店为空，参数不合法");
                return false;
            }
            List<Long> allStoreId = Lists.newArrayList();
            if (StringUtils.isBlank(boxBuilderGuideStore)) {
                context.setErrorMsg("当前活动搭盒人员导购门店为空，参数不合法");
                return false;
            }
//            Splitter.on(",").splitToList(boxBuilderGuideStore).stream().forEach(storeId -> {
//                // 门店包的门店ID,判断导购门店ID是否在包内
//                List<Long> st = context.getIStoreCenterHttpService().listStoreId(Long.valueOf(storeId));
//                if (CollectionUtils.isNotEmpty(st)) {
//                    allStoreId.addAll(st);
//                }
//            });
//            boolean contains = allStoreId.contains(Long.valueOf(guiderStoreId));
//            if (!contains) {
//                context.setErrorMsg("当前活动搭盒人员导购门店不在门店包内，不符合搭盒人员条件");
//                return false;
//            }

            Boolean contains = context.getIStoreCenterHttpService().hitStore(
                    Long.valueOf(boxBuilderGuideStore), Long.valueOf(guiderStoreId));
            if (Boolean.FALSE.equals(contains)) {
                context.setErrorMsg("当前活动搭盒人员导购门店不在门店包内，不符合搭盒人员条件");
                return false;
            }
        }
        log.info("符合搭盒人员条件");
        return true;
    }

    @Override
    public void startLog() {
        log.info("开始判断搭盒人员条件");
    }

    @Override
    public void endLog() {
        log.info("结束判断搭盒人员条件");
    }
}

package org.springcenter.box.activity.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.ResponseResult;
import com.jnby.common.cache.RedisPoolUtil;
import com.jnby.common.cache.RedisTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.Message;
import org.springcenter.box.activity.config.exception.BoxActivityException;
import org.springcenter.box.activity.config.redis.RedissonUtil;
import org.springcenter.box.activity.dto.request.JoinRecordImportExcelReq;
import org.springcenter.box.activity.dto.request.JoinRecordListReq;
import org.springcenter.box.activity.service.IJoinRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Description:监听需要解析的文件
 * @Author: brian
 * @Date: 2021/8/30 15:09
 */
@Component
@Slf4j
@RefreshScope
public class FileAnalysisListener implements IMessageListener {

    @Value("${mq.upload.analysis.topic}")
    private String topic;

    @Value("${mq.upload.analysis.tags}")
    private String tags;

    @Autowired
    private RedisPoolUtil redisPoolUtil;

    @Autowired
    private RedissonUtil redissonUtil;

    @Resource
    private IJoinRecordService joinRecordService;

    @Override
    public String getTopic() {
        return topic;
    }

    @Override
    public String getTags() {
        return tags;
    }

    // 导出获奖的参与记录
    public static final String TAG_EXPORT_C_ACTIVITY_JOIN_RECORD = "EXPORT_C_ACTIVITY_JOIN_RECORD";
    // 导入兑换券发奖信息
    public static final String TAG_IMPORT_EXCHANGE_GIFT = "IMPORT_C_ACTIVITY_JOIN_RECORD_EXCHANGE_GIFT";

    @Override
    public ConsumeConcurrentlyStatus consume(Message msg) {
        String keys = msg.getKeys();
        String msgTags = msg.getTags();
        List<String> confTags = Arrays.asList(tags.split(","));

        if (!confTags.contains(msgTags)) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        String para = null;
        try {
            para = new String(msg.getBody(), "UTF-8");
            JSONObject jsonObject = JSON.parseObject(para);
            log.info("接收到文件解析消息:{}", jsonObject);
            if (TAG_EXPORT_C_ACTIVITY_JOIN_RECORD.equals(msgTags)) {
                JoinRecordListReq req = JSON.parseObject(para, JoinRecordListReq.class);
                // 返回下载地址
                String exportUrl = joinRecordService.exportJoinRecord(req);
                RedisTemplateUtil.setex(redisPoolUtil, keys, exportUrl,60);
            } else if (TAG_IMPORT_EXCHANGE_GIFT.equals(msg.getTags())){
                log.info("开始处理 兑换券 导入，keys[{}]，消息内容:{}", keys, JSON.toJSONString(jsonObject));
                String lockKey = TAG_IMPORT_EXCHANGE_GIFT + ":" + keys;
                // 加锁5分钟处理
                if (!redissonUtil.tryLock(lockKey, 1, 300)) {
                    log.error("重复消费 兑换券 导入，终止执行");
                } else {
                    try {
                        String outId = (String) jsonObject.get("outId");
                        JoinRecordImportExcelReq req = JSON.parseObject(outId, JoinRecordImportExcelReq.class);
                        req.setExcelUrl(jsonObject.getString("url"));
                        // 处理
                        ResponseResult resp = joinRecordService.importExchangeGift(req);
                        log.info("处理完成 兑换券 导入，keys[{}]，结果:{}", keys, JSON.toJSONString(resp));
                        RedisTemplateUtil.setex(redisPoolUtil, keys, "1", 600);
                    } finally {
                        redissonUtil.unlock(lockKey);
                    }
                }
            }
            log.info("接收到文件解析消息 处理完成");
        } catch (BoxActivityException e) {
            log.error("导入导出 业务异常,tags={},param={}", msg.getTags(), para, e);
            if (!RedisTemplateUtil.exists(redisPoolUtil, keys + "error")) {
                // 缓存1分钟
                RedisTemplateUtil.setex(redisPoolUtil, keys + "error", e.getMessage(), 60);
            }
        } catch (Exception e) {
            log.error("导入导出 未知异常,tags={},param={}", msg.getTags(), para, e);
            if (!RedisTemplateUtil.exists(redisPoolUtil, keys + "error")) {
                // 缓存1分钟
                RedisTemplateUtil.setex(redisPoolUtil, keys + "error", msg.getBody() + "文件处理异常", 60);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}

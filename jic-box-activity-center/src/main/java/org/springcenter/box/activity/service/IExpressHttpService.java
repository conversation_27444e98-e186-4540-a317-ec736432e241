package org.springcenter.box.activity.service;


import org.springcenter.box.activity.domain.box.CActivityGift;
import org.springcenter.box.activity.domain.box.CustomerLogistics;
import org.springcenter.box.activity.api.dto.JoinRecordInfoResp;
import org.springcenter.retail.api.dto.resp.QueryOrder2JSTRespDTO;

import java.util.List;
import java.util.Map;

public interface IExpressHttpService {
    /**
     * 发货
     */
    void sendExpress(CActivityGift gift, JoinRecordInfoResp joinRecordInfoResp);
    /**
     * 批量发货
     */
//    void batchSendExpress(CActivityGift gift, List<JoinRecordInfoResp> joinRecordInfoResps);

    /**
     * 获取地址
     *
     * @param logisticsId
     * @return
     */
    CustomerLogistics getLogisticsInfo(String logisticsId);

    /**
     * 批量获取地址
     *
     * @param logisticsIdList
     * @return key: logisticsId, value: CustomerLogistics
     */
    Map<String, CustomerLogistics> listLogisticsInfo(List<String> logisticsIdList);


    List<QueryOrder2JSTRespDTO> queryExpress(List<JoinRecordInfoResp> joinRecordInfoResps);
}


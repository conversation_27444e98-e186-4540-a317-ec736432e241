package org.springcenter.box.activity.api;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.api.dto.JoinRecordInfoResp;
import org.springcenter.box.activity.api.dto.JoinRecordQueryReq;
import org.springcenter.box.activity.api.dto.JoinRecordQueryResp;
import org.springcenter.box.activity.convert.JoinRecordConvertor;
import org.springcenter.box.activity.dto.request.CActivityEffectReq;
import org.springcenter.box.activity.dto.request.JoinRecordListReq;
import org.springcenter.box.activity.dto.request.JoinRecordMiniAppListReq;
import org.springcenter.box.activity.dto.response.JoinRecordMiniAppGainResp;
import org.springcenter.box.activity.service.ICActivityService;
import org.springcenter.box.activity.service.IJoinRecordService;
import org.springcenter.box.activity.util.DateUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RequestMapping("/miniapp/c/activity")
@RestController
@Api(tags = "小程序端-满赠活动")
@Slf4j
public class MiniappCActivityController {

    @Resource
    private ICActivityService icActivityService;
    @Resource
    private IJoinRecordService joinRecordService;
    @Resource
    private JoinRecordConvertor joinRecordConvertor;

    @ApiOperation(value = "有效活动", notes = "根据实付金额计算符合条件的有效活动")
    @PostMapping("/effect")
    public ResponseResult<List<CActivityInfoResp>> effectActivity(@Validated @RequestBody CommonRequest<CActivityEffectReq> request) {
        CActivityEffectReq requestData = request.getRequestData();
        log.info("有效活动 小程序端入参:{}", JSON.toJSONString(requestData));
        requestData.check();
        List<CActivityInfoResp> respList = icActivityService.effectActivity(requestData);
        log.info("有效活动 小程序端回参:{}", JSON.toJSONString(respList));
        return ResponseResult.success(respList);
    }

    @ApiOperation(value = "订单列表", notes = "查询是否获得赠品")
    @PostMapping("/join/record/gain")
    public ResponseResult<List<JoinRecordMiniAppGainResp>> joinRecordGainList(@Validated @RequestBody CommonRequest<JoinRecordMiniAppListReq> request) {
        JoinRecordMiniAppListReq requestData = request.getRequestData();
        log.info("明细列表 小程序端入参:{}", JSON.toJSONString(requestData));
        JoinRecordListReq req = new JoinRecordListReq();
        req.setUnionId(requestData.getUnionId());
        req.setBoxIdList(requestData.getBoxIdList());
        req.setIsolationTime(DateUtil.getStrToTime("2025-03-27 18:00:00"));
        List<JoinRecordInfoResp> joinRecordList = joinRecordService.listJoinRecord(req, request.getPage());
        List<JoinRecordMiniAppGainResp> rspList = Optional.ofNullable(joinRecordList).orElse(Lists.newArrayList()).stream().map(
                joinRecordConvertor::infoList2MiniAppGain).collect(Collectors.toList());
        log.info("明细列表 小程序端回参:{}", JSON.toJSONString(rspList));
        return ResponseResult.success(rspList);
    }

    @ApiOperation(value = "参与记录明细")
    @PostMapping("/join/record/info")
    public ResponseResult<JoinRecordQueryResp> queryJoinRecord(@Validated @RequestBody CommonRequest<JoinRecordQueryReq> request) {
        JoinRecordQueryReq requestData = request.getRequestData();
        log.info("根据BOX单号查询满赠活动达成状态,入参:{}", JSON.toJSONString(requestData));
        requestData.checkMini();
        JoinRecordQueryResp rsp = joinRecordService.queryJoinRecordByBoxSn(requestData, true);
        log.info("根据BOX单号查询满赠活动达成状态,回参:{}", JSON.toJSONString(rsp));
        return ResponseResult.success(rsp);
    }

}

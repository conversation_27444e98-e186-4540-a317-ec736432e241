package org.springcenter.box.activity.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class HitStoreEntityReq {
    @ApiModelProperty("门店包id")
    private Long storePackageId;

    @ApiModelProperty("判断的门店")
    private Long storeId;
}

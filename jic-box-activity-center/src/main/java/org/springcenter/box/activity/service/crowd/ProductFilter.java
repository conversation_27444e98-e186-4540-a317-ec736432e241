package org.springcenter.box.activity.service.crowd;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springcenter.box.activity.api.dto.CActivityInfoResp;
import org.springcenter.box.activity.dto.request.CActivityEffectReq;
import org.springcenter.box.activity.enums.ItemRangeEnum;
import org.springcenter.box.activity.service.IProductHttpService;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品判断
 */
@Service
@Slf4j
public class ProductFilter implements CrowdFilterHandler {
    CrowdFilterHandler next = null;

    @Override
    public String filterName() {
        return "品牌商品";
    }

    @Override
    public CrowdFilterHandler getNext() {
        return next;
    }

    @Override
    public void setNext(CrowdFilterHandler next) {
        this.next = next;
    }

    @Override
    public boolean filter(CrowdFilterContext context) {
        CActivityInfoResp activityInfo = context.getActivityInfo();
        Integer boxBrandType = activityInfo.getBoxBrandType();

        if (boxBrandType == null) {
            context.setErrorMsg("盒子商品品牌为空，无法判断品牌商品");
            return false;
        }

        if (ItemRangeEnum.isAll(boxBrandType)) {
            log.info("未指定商品,不过滤");
            return true;
        }
        log.info("判断类型:{}", ItemRangeEnum.LOOKUP.get(boxBrandType).getMsg());

        // 活动指定的商品包
        List<String> pacIds = Splitter.on(",").splitToList(activityInfo.getBoxBrandList());

        // 当前选择了的商品
        List<Long> productIds = context.getProductList().stream().map(CActivityEffectReq.ProductReq::getSpuId).distinct().collect(Collectors.toList());

        // 获取所有选择的商品是否命中商品包
        Map<Long, Integer> productId2Exist = context.getIProductHttpService().getProductId2ExistMap(pacIds, productIds);

        if (ItemRangeEnum.isAccomplishAndIncludeItem(boxBrandType)) {
//            boolean present = context.getProductList().stream().filter(productReq -> productId2Exist.get(productReq.getSpuId()) == 1).findAny().isPresent();
//            if (!present) {
//                context.setErrorMsg("未包含指定商品，不满足商品条件");
//                return false;
//            }
//            log.info("符合指定商品");
            log.info("满足门槛且含1件指定商品，当前校验指定商品，等待拉单后计算");
        } else {
            List<CActivityEffectReq.ProductReq> productList = context.getProductList();
            Iterator<CActivityEffectReq.ProductReq> iterator = productList.iterator();
            while (iterator.hasNext()) {
                CActivityEffectReq.ProductReq product = iterator.next();
                Long spuId = product.getSpuId();
                Integer exist = productId2Exist.get(spuId);
                // 0不存在 1存在
                if (exist == null || exist == 0) {
                    log.info("商品未命中商品包, 商品ID[{}]", spuId);
                    iterator.remove();
                }
            }

            if (CollectionUtils.isEmpty(productList)) {
                context.setErrorMsg("指定商品包过滤后不存在符合条件的商品，不满足商品条件");
                return false;
            }

            log.info("符合指定商品，过滤后参与累计的商品:{}", JSON.toJSONString(productList));
            context.setProductList(productList);
        }

        return true;
    }

    @Override
    public void startLog() {
        log.info("开始判断指定商品条件");
    }

    @Override
    public void endLog() {
        log.info("结束判断指定商品条件");
    }
}

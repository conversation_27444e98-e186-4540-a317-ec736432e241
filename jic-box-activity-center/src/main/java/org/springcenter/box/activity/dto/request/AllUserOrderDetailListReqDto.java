package org.springcenter.box.activity.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class AllUserOrderDetailListReqDto {
    @ApiModelProperty(value = "spuId")
    @NotBlank(message = "spuId不能为空")
    private String spuId;


    @ApiModelProperty(value = "orderSn")
    private String orderSn;
}

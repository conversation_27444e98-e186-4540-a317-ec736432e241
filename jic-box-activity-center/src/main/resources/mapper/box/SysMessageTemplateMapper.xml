<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.SysMessageTemplateMapper">

    <resultMap id="BaseResuleMap" type="org.springcenter.box.activity.domain.box.SysMessageTemplate">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="TEMPLATE_CODE" jdbcType="VARCHAR" property="templateCode" />
        <result column="TEMPLATE_NAME" jdbcType="VARCHAR" property="templateName" />
        <result column="TEMPLATE_CONTENT" jdbcType="VARCHAR" property="templateContent" />
        <result column="TEMPLATE_TEST_JSON" jdbcType="VARCHAR" property="templateTestJson" />
        <result column="TEMPLATE_TYPE" jdbcType="VARCHAR" property="templateType" />
    </resultMap>

    <sql id="Base_Column">
        ID,TEMPLATE_NAME,TEMPLATE_CODE,TEMPLATE_TYPE,TEMPLATE_TEST_JSON,TEMPLATE_CONTENT
    </sql>

    <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResuleMap">
        SELECT
        <include refid="Base_Column"/>
        FROM SYS_SMS_TEMPLATE WHERE TEMPLATE_CODE = #{code}
    </select>
</mapper>

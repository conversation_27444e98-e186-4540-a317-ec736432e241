<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BPointGoodsSpuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BPointGoodsSpu">
        <id column="ID" property="id" />
        <result column="SPU_NO" property="spuNo" />
        <result column="IMG_URL" property="imgUrl" />
        <result column="GOODS_NAME" property="goodsName" />

        <result column="POINT" property="point" />
        <result column="PRICE" property="price" />


        <result column="STATUS" property="status" />

        <result column="BRAND_ID" property="brandId" />
        <result column="BRAND_NAME" property="brandName" />
        <result column="HAS_STOCK" property="hasStock" />

        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SPU_NO, IMG_URL, GOODS_NAME,POINT,PRICE, STATUS, BRAND_ID, BRAND_NAME, HAS_STOCK, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>
    <select id="getListBySpuNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_GOODS_SPU
        where
        DEL_FLAG=0 and SPU_NO=#{spuNo}
    </select>

    <select id="getListBySearch" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_GOODS_SPU
        where
        DEL_FLAG=0
        <if test="hasStock != null">
           and HAS_STOCK= #{hasStock}
        </if>
        <if test="status != null ">
           and STATUS= #{status}
        </if>
        order by HAS_STOCK desc , CREATE_TIME desc
    </select>
    <select id="getMiniListBySearch" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_GOODS_SPU
        where
        DEL_FLAG=0
        <if test="hasStock != null">
            and HAS_STOCK= #{hasStock}
        </if>
        <if test="status != null ">
            and STATUS= #{status}
        </if>
        order by HAS_STOCK desc , POINT asc, CREATE_TIME desc

    </select>
    <select id="getAllList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_GOODS_SPU
        where
        DEL_FLAG=0
    </select>


    <select id="getLastLowPointGoodsSpu" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from (
           select
             <include refid="Base_Column_List"/>
              from
             B_POINT_GOODS_SPU
            where
             DEL_FLAG=0  and status=1
             order by POINT asc
        ) where rownum=1

    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.CustomerDetailsMapper">
    <resultMap id="SalesBoxCustomerListResultMap" type="org.springcenter.box.activity.api.dto.SalesCustomerListResp">
        <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
        <result column="NICKNAME" jdbcType="VARCHAR" property="nickname" />
        <result column="CARDNO" jdbcType="VARCHAR" property="cardNo" />
        <result column="CAS_CARD_TYPE_NAME" jdbcType="VARCHAR" property="brandName" />
        <result column="SEX" jdbcType="VARCHAR" property="sex" />
        <result column="CLERKNAME" jdbcType="VARCHAR" property="clerkName" />
        <result column="CLERKID" jdbcType="DECIMAL" property="clerkId" />
        <result column="STOREID" jdbcType="DECIMAL" property="storeId" />
        <result column="CUMULATIVE_TOTAL_CONSUME" jdbcType="DECIMAL" property="cumulativeTotalConsume" />
        <result column="TEL" jdbcType="VARCHAR" property="mobil"/>
        <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
        <result column="HEADIMGURL" jdbcType="VARCHAR" property="headimgurl" />
        <result column="WEID" jdbcType="DECIMAL" property="weid" />
        <result column="LEVELID" jdbcType="DECIMAL" property="levelId" />
        <result column="SUB_ID" jdbcType="VARCHAR" property="subId" />
        <result column="TOTAL_POINT" jdbcType="DECIMAL" property="totalPoint" />
        <result column="CAN_USE_POINT" jdbcType="DECIMAL" property="canUsePoint" />
        <result column="SELF_BOX_TAG" jdbcType="DECIMAL" property="selfBoxTag" />
    </resultMap>
    <!-- 查询导购会员订阅计划内的会员列表 -->
    <select id="salesSubscribeCustomerList" resultMap="SalesBoxCustomerListResultMap" parameterType="org.springcenter.box.activity.api.dto.SalesCustomerReq">
        SELECT
        DISTINCT bsi.UNIONID,
        bsi.CUST_ID AS USER_ID,
        a.CUMULATIVE_TOTAL_CONSUME,
        c.NICKNAME,
        c.CARDNO,
        c.CAS_CARD_TYPE_NAME,
        c.HEADIMGURL,
        c.SEX ,
        c.CLERKNAME,
        c.CLERKID,
        c.STOREID,
        c.TEL,
        jct.WEID,
        jct.LEVELID,
        bsi.ID as SUB_ID,
        nvl(bpa.TOTAL_POINT, 0) as TOTAL_POINT,
        nvl(bpa.CAN_USE_POINT, 0) as CAN_USE_POINT,
        CASE
        WHEN nvl(bobl.ID, 0) =0 THEN 0
        ELSE 1 END SELF_BOX_TAG
        FROM
        JNBY.B_SUBSCRIBE_INFO bsi
        LEFT JOIN
        USERWX.CARDMAIN c
        ON 	bsi.UNIONID = c.UNIONID  AND bsi.CARD_TYPE = 1 AND bsi.STATUS = 1 and bsi.DEL_FLAG = 0
        LEFT JOIN
        USERWX.JIC_CARD_TYPE jct
        ON
        jct.ID = c.CARDTYPEID
        <if test="(subStartTime != null and subStartTime !='') or (subEndTime != null and subEndTime != '')">
            left join
            JNBY.B_SUBSCRIBE_PLAN bsp on bsi.ID = bsp.SUB_ID and bsp.status = 0 and bsp.del_flag = 0
        </if>
        LEFT JOIN
        JNBY.CUSTOMER_DETAILS cd ON bsi.UNIONID  = cd.UNIONID
        left join
        jnby.B_USER_POINT_ACCOUNT bpa on bpa.UNION_ID = cd.UNIONID and bpa.DEL_FLAG = 0
        LEFT JOIN
        JNBY.B_CUSTOMER_EXPAND a ON a.USER_ID = cd.ID
        LEFT JOIN
        jnby.B_OPTIONAL_BOX_LINK bobl ON bsi.UNIONID = bobl.UNION_ID AND bobl.STATUS = 0
        where
        c.STOREID IN
        <foreach collection="cStoreIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and c.CLERKID IN
        <foreach collection="hrIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="q != null and q != ''">
            and (c.NICKNAME  like '%${q}%' or c.TEL = #{q} or c.CARDNO = #{q})
        </if>

        <if test="subStartTime != null and subStartTime != ''">
            and TO_CHAR(bsp.PLAN_MONTH, 'yyyy-MM') = #{subStartTime}
        </if>

        <if test="planMonth != null and planMonth != ''">
            and bsp.PLAN_MONTH_STR = #{planMonth}
        </if>
        <if test="minPrice > 0">
            and bsi.TOTAL_PRICE &gt;= #{minPrice}
        </if>
        <if test="maxPrice > 0">
            and bsi.TOTAL_PRICE &lt;= #{maxPrice}
        </if>

        <choose>
            <when test="subTag != null and subTag != ''">
                order by SELF_BOX_TAG desc
            </when>
            <otherwise>
                order by TOTAL_POINT desc
            </otherwise>
        </choose>

    </select>
</mapper>
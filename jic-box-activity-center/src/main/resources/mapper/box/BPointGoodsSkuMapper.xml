<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BPointGoodsSkuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BPointGoodsSku">
        <id column="ID" property="id" />
        <result column="SKU_NO" property="skuNo" />
        <result column="IMG_URL" property="imgUrl" />
        <result column="STOCK" property="stock" />
        <result column="POINT" property="point" />
        <result column="SPU_ID" property="spuId" />
        <result column="SPU_NO" property="spuNo" />
        <result column="PRICE" property="price" />
        <result column="GOODS_NAME" property="goodsName" />

        <result column="COLOR_NAME" property="colorName" />
        <result column="COLOR_NO" property="colorNo" />
        <result column="SIZE_NAME" property="sizeName" />
        <result column="SIZE_NO" property="sizeNo" />

        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SKU_NO, IMG_URL, STOCK, POINT, SPU_ID, SPU_NO, PRICE, GOODS_NAME,
        COLOR_NAME,COLOR_NO,SIZE_NAME,SIZE_NO,
        CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>
    <select id="selectListBySpuId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_GOODS_SKU
        where
        DEL_FLAG=0
        and SPU_ID =#{spuId}
    </select>


    <select id="getSpuIdListHasStock" resultType="java.lang.String">
        select
          distinct SPU_ID
        from
        B_POINT_GOODS_SKU
        where
        DEL_FLAG=0  and STOCK>0
        and  SPU_ID in
        <foreach collection="spuIdList" item="spuId" open="(" close=")" separator=",">
             #{spuId}
        </foreach>
    </select>

    <update id="subtractStockBySkuId">
        update B_POINT_GOODS_SKU
        set STOCK=STOCK - 1
        where STOCK >= 1 and id =#{skuId}
    </update>

</mapper>
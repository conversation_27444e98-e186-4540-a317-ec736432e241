<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BUserPointOrderDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BUserPointOrderDetail">
        <id column="ID" property="id"/>
        <result column="UNION_ID" property="unionId"/>
        <result column="CUSTOMER_ID" property="customerId"/>
        <result column="USED_POINT" property="usedPoint"/>
        <result column="ORDER_ID" property="orderId"/>
        <result column="ORDER_SN" property="orderSn"/>
        <result column="SKU_ID" property="skuId"/>
        <result column="SKU_NO" property="skuNo"/>
        <result column="SPU_ID" property="spuId"/>
        <result column="SPU_NO" property="spuNo"/>
        <result column="IMG_URL" property="imgUrl"/>
        <result column="GOODS_NAME" property="goodsName"/>

        <result column="COLOR_NAME" property="colorName"/>
        <result column="COLOR_NO" property="colorNo"/>
        <result column="SIZE_NAME" property="sizeName"/>
        <result column="SIZE_NO" property="sizeNo"/>
        <result column="LOGISTICS_SNAPSHOT_ID" property="logisticsSnapshotId"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="DEL_FLAG" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, UNION_ID,CUSTOMER_ID, USED_POINT, ORDER_ID, ORDER_SN, SKU_ID, SKU_NO, SPU_ID, SPU_NO, IMG_URL, GOODS_NAME,
        COLOR_NAME,COLOR_NO,SIZE_NAME,SIZE_NO,LOGISTICS_SNAPSHOT_ID,
        CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>

    <select id="getByOrderIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM B_USER_POINT_ORDER_DETAIL
        where DEL_FLAG = 0
        and ORDER_ID in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <select id="getBySpuIdOrOrderSn" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM B_USER_POINT_ORDER_DETAIL
        where DEL_FLAG = 0
        and SPU_ID= #{spuId}
        <if test="orderSn != null and orderSn != ''">
            and ORDER_SN = #{orderSn}

        </if>
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BSubscribeInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BSubscribeInfo">
        <id column="ID" property="id"/>
        <result column="APP_ID" property="appId"/>
        <result column="APP_NAME" property="appName"/>
        <result column="CUST_ID" property="custId"/>
        <result column="UNIONID" property="unionid"/>
        <result column="SUB_ORDER_ID" property="subOrderId"/>
        <result column="START_TIME" property="startTime"/>
        <result column="END_TIME" property="endTime"/>
        <result column="TOTAL_PRICE" property="totalPrice"/>

        <result column="STATUS" property="status"/>
        <result column="CARD_TYPE" property="cardType"/>
        <result column="USER_CARD_LEVEL" property="userCardLevel"/>

        <result column="PAY_WAY" property="payWay"/>
        <result column="UNSUB_TIME" property="unsubTime"/>

        <result column="CARD_ID" property="cardId"/>
        <result column="FIRST_RECOVER" property="firstRecover"/>
        <result column="OUT_NO" property="outNo"/>
        <result column="BIND_FASHIONER_ID" property="bindFashionerId"/>
        <result column="RECOMMENDER" property="recommender"/>
        <result column="RENEW" property="renew"/>
        <result column="INVITE_PEOPLE" property="invitePeople"/>

        <result column="TYPE" property="type"/>

        <result column="CREATE_CARD_TYPE" property="createCardType"/>
        <result column="CREATE_CARD_STORE_ID" property="createCardStoreId"/>

        <result column="CREATE_CARD_BRAND_ID" property="createCardBrandId"/>
        <result column="CREATE_CARD_HR_ID" property="createCardHrId"/>
        <result column="UNSUB_MEMO" property="unsubMemo"/>


        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="DEL_FLAG" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , APP_ID, APP_NAME,STATUS, CUST_ID, UNIONID, SUB_ORDER_ID, START_TIME, END_TIME, TOTAL_PRICE,CARD_ID,USER_CARD_LEVEL,PAY_WAY,UNSUB_TIME, FIRST_RECOVER,OUT_NO,BIND_FASHIONER_ID, RECOMMENDER,RENEW,INVITE_PEOPLE,TYPE, CREATE_CARD_TYPE, CREATE_CARD_STORE_ID,CREATE_CARD_HR_ID,CREATE_CARD_BRAND_ID, UNSUB_MEMO, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>

    <!-- 获取用户有效的订阅周期记录 -->
    <select id="selectUserEffectSubInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_SUBSCRIBE_INFO bsi
        where
        bsi.DEL_FLAG=0  and
        bsi.CARD_TYPE = 1 AND
        (bsi.STATUS = 1 and bsi.END_TIME <![CDATA[ >=]]> sysdate)
        AND
        CUST_ID IN
        <foreach collection="userIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>

    <select id="selectPointSubInfoByUnionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_SUBSCRIBE_INFO
        where
        DEL_FLAG=0
        and UNIONID =#{unionId}
        and STATUS in (1,2) and CARD_TYPE=1
        and END_TIME>= add_months(sysdate,-1)
        and TOTAL_PRICE>0
        order by CREATE_TIME desc
    </select>


    <select id="selectPointUnSubInfoByUnionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_SUBSCRIBE_INFO
        where
        DEL_FLAG=0
        and UNIONID =#{unionId}
        and STATUS =3 and CARD_TYPE=1
        and UNSUB_TIME>= add_months(sysdate,-1)
        and TOTAL_PRICE>0
        order by CREATE_TIME desc
    </select>


    <select id="selectPointSubInfoByUnionIdAndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_SUBSCRIBE_INFO
        where
        DEL_FLAG=0
        and UNIONID =#{unionId}
        and CARD_TYPE=1
        and CREATE_TIME  >= to_date('2024-01-01' , 'yyyy-mm-dd')
        order by CREATE_TIME desc
    </select>


    <select id="selectInfosByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_SUBSCRIBE_INFO
        where
        DEL_FLAG=0
        and
        ID in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        order by CREATE_TIME
    </select>


</mapper>
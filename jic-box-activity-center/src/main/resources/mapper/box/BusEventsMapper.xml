<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BusEventsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BusEvents">
        <id column="RECORD_ID" property="recordId"/>
        <result column="CLASS_NAME" property="className" />
        <result column="USER_TOKEN" property="userToken" />
        <result column="CREATED_DATE" property="createdDate" />
        <result column="CREATING_OWNER" property="creatingOwner" />
        <result column="PROCESSING_OWNER" property="processingOwner" />
        <result column="PROCESSING_AVAILABLE_DATE" property="processingAvailableDate" />
        <result column="ERROR_COUNT" property="errorCount" />
        <result column="PROCESSING_STATE" property="processingState" />
        <result column="SEARCH_KEY1" property="searchKey1" />
        <result column="SEARCH_KEY2" property="searchKey2" />
        <result column="EVENT_JSON" property="eventJson" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RECORD_ID, CLASS_NAME, USER_TOKEN, CREATED_DATE, CREATING_OWNER, PROCESSING_OWNER, PROCESSING_AVAILABLE_DATE, ERROR_COUNT, PROCESSING_STATE, SEARCH_KEY1, SEARCH_KEY2, EVENT_JSON
    </sql>

</mapper>

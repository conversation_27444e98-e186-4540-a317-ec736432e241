<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BPointDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BPointDetail">
        <id column="ID" property="id"/>
        <result column="SUB_ID" property="subId"/>
        <result column="UNION_ID" property="unionId"/>
        <result column="CUSTOMER_ID" property="customerId"/>

        <result column="OUT_ID" property="outId"/>
        <result column="OUT_SN" property="outSn"/>

        <result column="POINT" property="point"/>
        <result column="MEMO" property="memo"/>
        <result column="TYPE" property="type"/>

        <result column="ACTIVE_TIME" property="activeTime"/>
        <result column="EXPIRE_TIME" property="expireTime"/>

        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="DEL_FLAG" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SUB_ID,  UNION_ID,
        CUSTOMER_ID,OUT_ID,OUT_SN,
        POINT, MEMO, TYPE,
        ACTIVE_TIME, EXPIRE_TIME,
        CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>


    <select id="getListByUnionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_DETAIL
        where
        DEL_FLAG=0
        and UNIONID =#{unionId}
        and sysdate  <![CDATA[ >= ]]> ACTIVE_TIME
        and sysdate <![CDATA[ <= ]]> EXPIRE_TIME
        order by CREATE_TIME desc
    </select>

    <select id="getListByCustomerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_DETAIL
        where
        DEL_FLAG=0
        and CUSTOMER_ID =#{customerId}
        and sysdate  <![CDATA[ >= ]]> ACTIVE_TIME
        and sysdate <![CDATA[ <= ]]> EXPIRE_TIME
        order by CREATE_TIME desc
    </select>
    <select id="getListByOutId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_DETAIL
        where
        DEL_FLAG=0
        and OUT_ID =#{outId}

    </select>
    <select id="getTotalPoint" resultType="java.math.BigDecimal">
        select
            SUM(POINT)
        from
        B_POINT_DETAIL
        where
        DEL_FLAG=0
        and CUSTOMER_ID = #{customerId}
        and sysdate <![CDATA[ <= ]]> EXPIRE_TIME
        and sysdate  <![CDATA[ >= ]]> ACTIVE_TIME
    </select>


    <select id="getActiveSubTimeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_DETAIL
        where
        DEL_FLAG=0
        and sysdate <![CDATA[ <= ]]> EXPIRE_TIME
    </select>

    <select id="getAllListByUnionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_DETAIL
        where
        DEL_FLAG=0
        and UNION_ID = #{unionId}
    </select>


    <select id="getExpirePointInDays" resultType="java.math.BigDecimal">
        select
            sum(POINT)
        from
        B_POINT_DETAIL
        where
        DEL_FLAG=0
        and CUSTOMER_ID = #{customerId}
        and TO_CHAR(EXPIRE_TIME,'YYYY-MM-DD')=TO_CHAR(SYSDATE+#{days},'YYYY-MM-DD')
    </select>
    <select id="getListByUnionIdAndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_DETAIL
        where
        DEL_FLAG=0
        and UNION_ID = #{unionId}
        and EXPIRE_TIME <![CDATA[ >= ]]> SYSDATE and EXPIRE_TIME  <![CDATA[ <= ]]> SYSDATE+#{days}
    </select>


    <select id="getExpireSubIdsInTwoDays" resultType="java.lang.String">
        select DISTINCT SUB_ID from B_POINT_DETAIL
        where DEL_FLAG=0 and EXPIRE_TIME <![CDATA[ < ]]>SYSDATE
              and EXPIRE_TIME   <![CDATA[ >= ]]> (SYSDATE-2)
    </select>



    <select id="getAllExpireSubIds" resultType="java.lang.String">
        select DISTINCT SUB_ID from B_POINT_DETAIL
        where DEL_FLAG=0 and EXPIRE_TIME <![CDATA[ < ]]>SYSDATE
    </select>


    <select id="getShowPointBySubId" resultType="java.math.BigDecimal">
        select
            sum(POINT)
        from
            B_POINT_DETAIL
        where
            DEL_FLAG=0
          and SUB_ID = #{subId}
    </select>

    <select id="getListBySubId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        B_POINT_DETAIL
        where
        DEL_FLAG=0
        and SUB_ID = #{subId}
    </select>


</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BSubscribePlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BSubscribePlan">
        <id column="ID" property="id" />
        <result column="APP_ID" property="appId" />
        <result column="APP_NAME" property="appName" />
        <result column="CUST_ID" property="custId" />
        <result column="UNIONID" property="unionid" />
        <result column="SUB_ID" property="subId" />
        <result column="PLAN_MONTH_STR" property="planMonthStr" />
        <result column="PLAN_MONTH" property="planMonth" />
        <result column="PLAN_END_MONTH" property="planEndMonth" />
        <result column="STATUS" property="status" />
        <result column="USED_NUM" property="usedNum" />

        <result column="BOX_ID" property="boxId" />
        <result column="ASK_BOX_ID" property="askBoxId" />

        <result column="PAY_PRICE" property="payPrice" />
        <result column="SAVE_PRICE" property="savePrice" />
        <result column="OBTAIN_INTEGRAL" property="obtainIntegral" />
        <result column="VERSION_COUNT" property="versionCount" />

        <result column="IS_SEND" property="isSend" />
        <result column="IS_CONTACT" property="isContact" />


        <result column="REMARK" property="remark" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, APP_ID, APP_NAME, CUST_ID, UNIONID, SUB_ID, PLAN_MONTH_STR, PLAN_MONTH,PLAN_END_MONTH,
        STATUS, USED_NUM,BOX_ID,ASK_BOX_ID,PAY_PRICE,SAVE_PRICE,OBTAIN_INTEGRAL,VERSION_COUNT,
        IS_SEND,IS_CONTACT,
        REMARK, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>

    <!-- 查询未完结节点通过subIds -->
    <select id="getUnCompleteSubscribePlanListBySubIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM
        JNBY.B_SUBSCRIBE_PLAN
        where
        DEL_FLAG=0 and
        STATUS IN (0,1,2)
         and
        SUB_ID in
        <foreach collection="subIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        order by PLAN_MONTH desc
    </select>


</mapper>
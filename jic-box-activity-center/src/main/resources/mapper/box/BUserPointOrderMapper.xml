<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BUserPointOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BUserPointOrder">
        <id column="ID" property="id"/>
        <result column="UNION_ID" property="unionId"/>
        <result column="CUSTOMER_ID" property="customerId"/>
        <result column="USED_POINT" property="usedPoint"/>
        <result column="ORDER_SN" property="orderSn"/>
        <result column="LOGISTICS_SNAPSHOT_ID" property="logisticsSnapshotId"/>
        <result column="CUSTOMER_LOGISTICS_ID" property="customerLogisticsId"/>


        <result column="HAS_JST_ORDER" property="hasJstOrder"/>

        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="DEL_FLAG" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , UNION_ID, CUSTOMER_ID, USED_POINT, ORDER_SN, LOGISTICS_SNAPSHOT_ID,CUSTOMER_LOGISTICS_ID,HAS_JST_ORDER, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>
    <select id="userOrderList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM B_USER_POINT_ORDER where DEL_FLAG=0 and UNION_ID =#{unionId}
        order by  CREATE_TIME desc
    </select>

</mapper>
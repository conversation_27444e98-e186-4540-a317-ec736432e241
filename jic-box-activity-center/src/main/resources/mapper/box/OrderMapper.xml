<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.OrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.Order">
        <id column="ID" property="id" />
        <result column="BOX_SN" property="boxSn" />
        <result column="ORDER_SN" property="orderSn" />
        <result column="SERVICE_SN" property="serviceSn" />
        <result column="SERIAL_NUMBER" property="serialNumber" />
        <result column="DAYSTR" property="daystr" />
        <result column="CUSTOMER_ID" property="customerId" />
        <result column="TRADE_NO" property="tradeNo" />
        <result column="WECHAT_TRANSACTION_ID" property="wechatTransactionId" />
        <result column="ORDER_STATUS" property="orderStatus" />
        <result column="PAID_AMOUNT" property="paidAmount" />
        <result column="PRODUCT_TOTAL_PRICE" property="productTotalPrice" />
        <result column="DISCOUNT_AMOUNT" property="discountAmount" />
        <result column="PRODUCT_DISCOUNT" property="productDiscount" />
        <result column="VIP_DISCOUNT" property="vipDiscount" />
        <result column="PRODUCT_TOTAL_QUANTITY" property="productTotalQuantity" />
        <result column="PAYMENT_TYPE" property="paymentType" />
        <result column="COIN" property="coin" />
        <result column="TRACKING_NUMBER" property="trackingNumber" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="VOUCHERS_NO" property="vouchersNo" />
        <result column="VOU_DIS" property="vouDis" />
        <result column="IS_YD" property="isYd" />
        <result column="SEND_TIME" property="sendTime" />
        <result column="INTEGRAL" property="integral" />
        <result column="IS_DS" property="isDs" />
        <result column="APPID" property="appid" />
        <result column="ISRETAIL" property="isretail" />
        <result column="TYPE" property="type" />
        <result column="FLAG" property="flag" />
        <result column="IS_EB" property="isEb" />
        <result column="IF_HAVE_UN_REFUND_ORDER" property="ifHaveUnRefundOrder" />
        <result column="EB_EXPRESS_SYNC_FINISH" property="ebExpressSyncFinish" />
        <result column="PAY_CHANNEL" property="payChannel" />
        <result column="SQB_SN" property="sqbSn" />
        <result column="WEIMOB_ORDER_SN" property="weimobOrderSn" />
        <result column="APP_ID" property="appId" />
        <result column="CALC_ID" property="calcId" />
        <result column="BALANCE_AMT" property="balanceAmt" />
        <result column="SHOP_VOU_AMT" property="shopVouAmt" />
        <result column="MERCHANT_CODE" property="merchantCode" />
        <result column="HB_FQ_NUM" property="hbFqNum" />
        <result column="IS_USE_WECHAT_COUPON" property="isUseWechatCoupon" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, BOX_SN, ORDER_SN, SERVICE_SN, SERIAL_NUMBER, DAYSTR, CUSTOMER_ID, TRADE_NO, WECHAT_TRANSACTION_ID, ORDER_STATUS, PAID_AMOUNT, PRODUCT_TOTAL_PRICE, DISCOUNT_AMOUNT, PRODUCT_DISCOUNT, VIP_DISCOUNT, PRODUCT_TOTAL_QUANTITY, PAYMENT_TYPE, COIN, TRACKING_NUMBER, CREATE_TIME, UPDATE_TIME, VOUCHERS_NO, VOU_DIS, IS_YD, SEND_TIME, INTEGRAL, IS_DS, APPID, ISRETAIL, TYPE, FLAG, IS_EB, IF_HAVE_UN_REFUND_ORDER, EB_EXPRESS_SYNC_FINISH, PAY_CHANNEL, SQB_SN, WEIMOB_ORDER_SN, APP_ID, CALC_ID, BALANCE_AMT, SHOP_VOU_AMT, MERCHANT_CODE, HB_FQ_NUM, IS_USE_WECHAT_COUPON
    </sql>

    <resultMap id="OrderDtoMap" type="org.springcenter.box.activity.dto.OrderDto">
        <id column="ID" property="id" />
        <result column="ORDER_SN" property="orderSn" />
        <result column="PAID_AMOUNT" property="paidAmount" />
        <result column="BALANCE_AMT" property="balanceAmt" />
        <result column="SHOP_VOU_AMT" property="shopVouAmt" />
    </resultMap>
    <select id="listOrderIdLeftJoinPayment" resultMap="OrderDtoMap">
        SELECT o.ID ,o.ORDER_SN ,o.PAID_AMOUNT ,o.BALANCE_AMT ,o.SHOP_VOU_AMT FROM ORDER_ o LEFT JOIN PAYMENT p ON o.id = p.ORDER_ID
        WHERE P.UPDATE_TIME BETWEEN
            to_date(#{paymentUpdateTimeStart} , 'yyyy-mm-dd hh24:mi:ss') AND to_date(#{paymentUpdateTimeEnd} , 'yyyy-mm-dd hh24:mi:ss')
          AND o.ORDER_STATUS IN
            <foreach item="orderStatus" index="index" collection="orderStatusList" open="(" separator="," close=")">
                #{orderStatus}
            </foreach>
          AND p.PAYMENT_STATUS = #{paymentStatus}
          AND o.BOX_SN = #{boxSn}
    </select>

</mapper>
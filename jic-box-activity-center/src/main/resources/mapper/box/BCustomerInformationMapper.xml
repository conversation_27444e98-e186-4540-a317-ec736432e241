<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BCustomerInformationMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BCustomerInformation">
                    <id column="USER_ID" property="userId" />
                    <result column="B_CATEGORY_ID" property="bCategoryId" />
                    <result column="JOIN_CATEGORY_REASON_ID" property="joinCategoryReasonId" />
                    <result column="JOIN_CATEGORY_REASON" property="joinCategoryReason" />
                    <result column="JOIN_CATEGORY_TIME" property="joinCategoryTime" />
                    <result column="CREATE_TIME" property="createTime" />
                    <result column="UPDATE_TIME" property="updateTime" />
                    <result column="last_fashioner_id" property="lastFashionerId" />
                    <result column="last_fashioner_name" property="lastFashionerName" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        USER_ID, B_CATEGORY_ID, JOIN_CATEGORY_REASON_ID, JOIN_CATEGORY_REASON, JOIN_CATEGORY_TIME, CREATE_TIME, UPDATE_TIME,last_fashioner_id,last_fashioner_name
    </sql>
    <select id="selectBySelective"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from B_CUSTOMER_INFORMATION
        <where>
            <if test="userId != null">
                and USER_ID = #{userId}
            </if>
        </where>
    </select>

</mapper>
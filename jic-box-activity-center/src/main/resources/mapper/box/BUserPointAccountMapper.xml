<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BUserPointAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BUserPointAccount">
        <id column="ID" property="id" />
        <result column="UNION_ID" property="unionId" />
        <result column="TOTAL_POINT" property="totalPoint" />
        <result column="CAN_USE_POINT" property="canUsePoint" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, UNION_ID, TOTAL_POINT, CAN_USE_POINT, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>
    <select id="getListByUnionIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM  B_USER_POINT_ACCOUNT
        where DEL_FLAG=0
        and UNION_ID in
        <foreach collection="unionIds" item="unionId" open="(" close=")" separator=",">
            #{unionId}
        </foreach>
    </select>
    <select id="selectByCustomerId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM  B_USER_POINT_ACCOUNT where id =#{customerId} and DEL_FLAG=0
    </select>


    <select id="selectByUnionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM  B_USER_POINT_ACCOUNT where UNION_ID =#{unionId} and DEL_FLAG=0
    </select>


    <select id="getListByCustomerIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM  B_USER_POINT_ACCOUNT
        where DEL_FLAG=0
        and ID in
        <foreach collection="customerIds" item="customerId" open="(" close=")" separator=",">
            #{customerId}
        </foreach>
    </select>

    <update id="addPointById">
        update B_USER_POINT_ACCOUNT
        <set>
            TOTAL_POINT = TOTAL_POINT+#{point}
        </set>
        where id =#{id}
    </update>
    <update id="subtractPointById">
        update B_USER_POINT_ACCOUNT
        <set>
            TOTAL_POINT = TOTAL_POINT-#{point}
        </set>
        where id =#{id}
    </update>

    <select id="getAllUserAccount" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM  B_USER_POINT_ACCOUNT
        where DEL_FLAG=0
    </select>


    <select id="getListByOverPoint" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM  B_USER_POINT_ACCOUNT
        where DEL_FLAG=0 and TOTAL_POINT <![CDATA[ >= ]]>  #{point}
    </select>


</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BOrderPromotionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BOrderPromotion">
        <id column="ID" property="id" />
        <result column="ORDER_ID" property="orderId" />
        <result column="SKU_ID" property="skuId" />
        <result column="PROMO_TYPE" property="promoType" />
        <result column="PROMO_ID" property="promoId" />
        <result column="PROMO_NO" property="promoNo" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DIS_TYPE" property="disType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, ORDER_ID, SKU_ID, PROMO_TYPE, PROMO_ID, PROMO_NO, CREATE_TIME, UPDATE_TIME, DIS_TYPE
    </sql>

</mapper>
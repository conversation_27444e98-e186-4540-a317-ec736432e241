<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BJstPushOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BJstPushOrder">
        <id column="ID" property="id" />
        <result column="UNION_ID" property="unionId" />
        <result column="CUSTOMER_ID" property="customerId" />
        <result column="SO_ID" property="soId" />
        <result column="ADDRESS_NAME" property="addressName" />
        <result column="ADDRESS_PHONE" property="addressPhone" />
        <result column="ADDRESS_PROVINCE" property="addressProvince" />
        <result column="ADDRESS_CITY" property="addressCity" />
        <result column="ADDRESS_DISTRICT" property="addressDistrict" />
        <result column="ADDRESS" property="address" />
        <result column="LOGISTICS" property="logistics" />
        <result column="LOGISTICS_NO" property="logisticsNo" />
        <result column="OUT_ID" property="outId" />
        <result column="OUT_ORDER_SN" property="outOrderSn" />

        <result column="GOODS_ID" property="goodsId" />
        <result column="GOODS_NAME" property="goodsName" />
        <result column="STATUS" property="status" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, UNION_ID, CUSTOMER_ID, SO_ID, ADDRESS_NAME, ADDRESS_PHONE, ADDRESS_PROVINCE, ADDRESS_CITY, ADDRESS_DISTRICT, ADDRESS, LOGISTICS, LOGISTICS_NO, OUT_ID,OUT_ORDER_SN, GOODS_ID, GOODS_NAME, STATUS, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>
    <select id="getListByOutId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM B_JST_PUSH_ORDER
        where DEL_FLAG = 0 and OUT_ID=#{outId}
    </select>


    <select id="getNoPushList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM B_JST_PUSH_ORDER
        where DEL_FLAG = 0 and STATUS=0
        order by CREATE_TIME desc
    </select>


    <select id="getNeedLogisticsList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"></include>
        FROM B_JST_PUSH_ORDER
        where DEL_FLAG = 0 and STATUS=1 and LOGISTICS_NO is null
        order by CREATE_TIME desc
    </select>

</mapper>
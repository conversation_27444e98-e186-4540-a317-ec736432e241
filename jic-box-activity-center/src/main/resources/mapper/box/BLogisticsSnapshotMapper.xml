<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcenter.box.activity.mapper.box.BLogisticsSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcenter.box.activity.domain.box.BLogisticsSnapshot">
        <id column="ID" property="id" />
        <result column="SEND_CONTACT" property="sendContact" />
        <result column="SEND_PHONE" property="sendPhone" />
        <result column="SEND_COMPANY" property="sendCompany" />
        <result column="SEND_PROVINCE" property="sendProvince" />
        <result column="SEND_CITY" property="sendCity" />
        <result column="SEND_DISTRICT" property="sendDistrict" />
        <result column="SEND_ADDRESS" property="sendAddress" />
        <result column="RECEIVE_CONTACT" property="receiveContact" />
        <result column="RECEIVE_PHONE" property="receivePhone" />
        <result column="RECEIVE_COMPANY" property="receiveCompany" />
        <result column="RECEIVE_PROVINCE" property="receiveProvince" />
        <result column="RECEIVE_CITY" property="receiveCity" />
        <result column="RECEIVE_DISTRICT" property="receiveDistrict" />
        <result column="RECEIVE_ADDRESS" property="receiveAddress" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="GET_TIME" property="getTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SEND_CONTACT, SEND_PHONE, SEND_COMPANY, SEND_PROVINCE, SEND_CITY, SEND_DISTRICT, SEND_ADDRESS,
    RECEIVE_CONTACT, RECEIVE_PHONE, RECEIVE_COMPANY, RECEIVE_PROVINCE, RECEIVE_CITY, RECEIVE_DISTRICT,
            RECEIVE_ADDRESS, CREATE_TIME, UPDATE_TIME, GET_TIME
    </sql>

</mapper>